/*
 * @Author: 016645
 * @Description: 删除会话功能
 * @Date: 2025-04-02 19:45:14
 */
import { useState, useEffect, useCallback } from 'react';
import { deepMerge, fetchData, fetchMS } from '../utils';
import { DeleteMessages } from '../types';
import getBaseUrl from '../../../utils/getBaseUrl';
export interface Data {
  code: string;
  msg: string;
  resultData: {};
}

/**
 * 删除消息的自定义Hook
 * @param options - 配置选项
 * @returns 包含删除消息方法和状态的对象
 */
const useDeleteMessages = (options: DeleteMessages) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    url = '',          // 请求地址
    type = 'http',     // 请求类型：http或tcp
    action = '',       // TCP请求action
    method = 'post',   // 请求方法
    paramsKey = '',    // 参数包裹的key
    headers,           // 请求头
    requestTransfer,   // 请求转换器
    manual = false,    // 是否手动触发
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取完整url
  const newBaseUrl = getBaseUrl(baseUrl);
  let fullUrl = '';
  if(url) {
    fullUrl = (`${newBaseUrl.replace(/\/+$/, '')}/${url.replace(/^\/+/, '')}`);
  } else {
    fullUrl = (`${newBaseUrl.replace(/\/+$/, '')}/delete_message`);
  }

  /**
   * 删除消息方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);

    try {
      // 解构请求参数
      const { appKey, appId, userId, conversationId, messageIds } = deepMerge(requestParams, customParams);
      
      if (!Array.isArray(messageIds) || messageIds.length === 0) {
        throw new Error('消息ID列表不能为空');
      }

      const promises = messageIds.map(async (item: { messageId: string, role?: string }) => {
        const { messageId } = item;
        const initRequestData = {
          appKey,
          appId,
          userId,
          conversationId,
          messageId,
        };
        
        const requestData = requestTransfer 
          ? await requestTransfer(initRequestData) 
          : initRequestData;
      
        if (type.toLowerCase() === 'tcp') {
          return fetchMS({
            action, 
            url: fullUrl, 
            method, 
            paramsKey, 
            data: requestData, 
            headers
          });
        } else {
          return fetchData({
            url: fullUrl, 
            data: requestData, 
            headers
          });
        }
      });
      
      await Promise.all(promises);
      
      setData({
        code: '0',
        msg: '删除消息成功',
        resultData: {},
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('删除消息失败'));
    } finally {
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    headers, 
    type, 
    method, 
    paramsKey, 
    action,
    requestParams
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useDeleteMessages;
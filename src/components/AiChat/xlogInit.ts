/*
 * @Author: 020446 <EMAIL>
 * @Date: 2025-07-22 20:37:09
 * @LastEditors: 020446 <EMAIL>
 * @LastEditTime: 2025-08-08 13:28:44
 * @FilePath: /chatui/src/components/AiChat/xlogInit.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// /*
//  * @Author: 020446 <EMAIL>
//  * @Date: 2025-07-08 15:18:53
//  * @LastEditors: 020446 <EMAIL>
//  * @LastEditTime: 2025-07-25 19:05:19
//  */
// import { init} from '@ht/xlog';
// import canUse from '../../utils/canUse';

// // 用来区分手机端、pc端
// const isMobile = canUse('touch');
// // 是否为SCK站点环境
// export const isSCKDoc = window.location.origin.includes('sckit.sit.saas.htsc');

// export const PV_LOADED_PARAMS = {
//     page_id: 'AICHAT_CONVERSATION_PAGE_LOADED',
//     page_title: '智能助手加载完成',
// }

// export const getChannelEnv = (isDevEnv?: boolean) => {
//   if (isMobile) {
//     return isDevEnv ? 'prd_outer_test' :'prd_outer';
//   } else {
//     return isDevEnv ? 'prd_inner_test' : 'prd_inner';
//   }
// }

// export const initXlog = (appId: string, isDev?: boolean) => {
//   try {
//     init({
//       from: 'SpriteCopilotKit',
//       types: ['windowError', 'unhandledrejection', 'xhr', 'fetch'], //
//       myTrackConfig: {
//         product_id: '542', // 用户上报
//         product_name: isSCKDoc ? 'SCK-Doc' : appId,
//         channel_env: isSCKDoc ? 'prd_outer_test' : getChannelEnv(isDev), // 项目运行环境
//         // product_type: 'web', // 用户上报
//         // prodcut_subid: '70', // 子系统id
//         // is_auto_track: false, // 配置项
//         // is_single_page: true, // 配置项
//         // show_log: false, // 是否在控制台打印数据
//       },
//     //   traceIdApiList: ['/llmops/'],
//     });
//   } catch (e) {
//     console.error('xlog初始化失败！', e);
//   }
// }

export {};


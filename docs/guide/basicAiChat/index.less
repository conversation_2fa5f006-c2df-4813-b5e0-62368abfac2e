@import '~@ht/chatui/es/styles/index.less';

.aiChatContainer {
  display: flex;
  align-items: center;
  flex-direction: column;
  background-color: gray;

  .aiChatPanel {
    height: calc(100vh - 200px);
    width: 430px;
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    background: linear-gradient(180deg, #cfe1f4 0%, #fff 50%, #fff 100%);
    overflow: hidden;
  }

  .aiChatConversation {
    width: 100%;
    display: flex;
    transition: transform .2s ease;
  }

  .navBar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 44px;
    padding: 8px 20px 0 15px;

    .iconSize {
      width: 18px;
      height: 18px;
    }

    &_title {
      font-size: 15.75px;
      font-weight: bold;
      color: #333;
    }
  }

  .historyPannel {
    width: 100%;
    display: flex;
  }

  .NewConversation {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    position: relative;
    z-index: 1;

    .historyMask {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 50%);
      top: 0;
      position: absolute;
      z-index: 2;
    }
  }

  .messageContainer {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    padding: 0 20px;
    overflow-y: auto;
    scrollbar-width: none;
  }

  .messageFooter {
    padding: 15px;
  }

  // .MobileNavBar .title span {
  //   text-align: center;
  // }
}

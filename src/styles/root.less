:root {
  --@{ai-kit-prefix}-brand-1: @brand-1;
  --@{ai-kit-prefix}-brand-2: @brand-2;
  --@{ai-kit-prefix}-brand-3: @brand-3;
  --@{ai-kit-prefix}-black: @black;
  --@{ai-kit-prefix}-white: @white;
  --@{ai-kit-prefix}-gray-1: @gray-1;
  --@{ai-kit-prefix}-gray-2: @gray-2;
  --@{ai-kit-prefix}-gray-3: @gray-3;
  --@{ai-kit-prefix}-gray-4: @gray-4;
  --@{ai-kit-prefix}-gray-5: @gray-5;
  --@{ai-kit-prefix}-gray-6: @gray-6;
  --@{ai-kit-prefix}-gray-7: @gray-7;
  --@{ai-kit-prefix}-gray-8: @gray-8;
  --@{ai-kit-prefix}-light-1: @light-1;
  --@{ai-kit-prefix}-light-2: @light-2;
  --@{ai-kit-prefix}-highlight-1: @highlight-1;
  --@{ai-kit-prefix}-highlight-2: @highlight-2;
  --@{ai-kit-prefix}-link-color: @link-color;
  --@{ai-kit-prefix}-blue: @blue;
  --@{ai-kit-prefix}-gray-dark: @gray-dark;
  --@{ai-kit-prefix}-green: @green;
  --@{ai-kit-prefix}-orange: @orange;
  --@{ai-kit-prefix}-red: @red;
  --@{ai-kit-prefix}-yellow: @yellow;
  --@{ai-kit-prefix}-yellow-light: @yellow-light;
  --@{ai-kit-prefix}-font-size-xs: @font-size-xs;
  --@{ai-kit-prefix}-font-size-sm: @font-size-sm;
  --@{ai-kit-prefix}-font-size-md: @font-size-md;
  --@{ai-kit-prefix}-font-size-lg: @font-size-lg;
  --@{ai-kit-prefix}-radius-sm: @border-radius-sm;
  --@{ai-kit-prefix}-radius-md: @border-radius-md;
  --@{ai-kit-prefix}-radius-lg: @border-radius-lg;
  --@{ai-kit-prefix}-shadow-1: @shadow-1;
  --@{ai-kit-prefix}-shadow-2: @shadow-2;
  --@{ai-kit-prefix}-shadow-3: @shadow-3;
  --@{ai-kit-prefix}-safe-top: @safe-top;
  --@{ai-kit-prefix}-safe-bottom: @safe-bottom;
  --@{ai-kit-prefix}-gutter: @gutter;
  --@{ai-kit-prefix}-btn-primary-border-color: @btn-primary-border-color;
  --@{ai-kit-prefix}-btn-primary-bg: @btn-primary-bg;
  --@{ai-kit-prefix}-btn-primary-color: @btn-primary-color;
  --@{ai-kit-prefix}-bubble-text-font-size: @bubble-text-font-size;
  --@{ai-kit-prefix}-bubble-think-text-font-size: @bubble-think-text-font-size;
  --@{ai-kit-prefix}-navbar-height: @navbar-height;
  --@{ai-kit-prefix}-navbar-bg: @navbar-bg;
  --@{ai-kit-prefix}-navbar-title-font-size: @navbar-title-font-size;
  --@{ai-kit-prefix}-navbar-title-color: @navbar-title-color;
  --@{ai-kit-prefix}-navbar-box-shadow: @navbar-box-shadow;
  --@{ai-kit-prefix}-chat-bg: @chat-bg;
  --@{ai-kit-prefix}-chat-color: @chat-color;
  --@{ai-kit-prefix}-guide-page-bg: @guide-page-bg;
  --@{ai-kit-prefix}-guide-page-title-color: @guide-page-title-color;
  --@{ai-kit-prefix}-guide-page-subtitle-color: @guide-page-subtitle-color;
  --@{ai-kit-prefix}-guide-page-risk-tip-color:  @guide-page-risk-tip-color;
  --@{ai-kit-prefix}-quick-reply-bg: @quick-reply-bg;
  --@{ai-kit-prefix}-quick-reply-font-color: @quick-reply-font-color;
  --@{ai-kit-prefix}-history-conversation-bg: @history-conversation-bg;
  --@{ai-kit-prefix}-history-conversation-active-bg: @history-conversation-active-bg;
  --@{ai-kit-prefix}-history-conversation-active-color: @history-conversation-active-color;
  --@{ai-kit-prefix}-bubble-right-color: @bubble-right-color;
  --@{ai-kit-prefix}-bubble-right-bg: @bubble-right-bg;
  --@{ai-kit-prefix}-bubble-left-color: @bubble-left-color;
  --@{ai-kit-prefix}-bubble-left-bg: @bubble-left-bg;
  --@{ai-kit-prefix}-bubble-think-color: @bubble-think-color;
  --@{ai-kit-prefix}-bubble-think-bg: @bubble-think-bg;
  --@{ai-kit-prefix}-bubble-left-padding-left:@bubble-left-padding-left;
  --@{ai-kit-prefix}-bubble-left-padding-right:@bubble-left-padding-right;
  --@{ai-kit-prefix}-bubble-left-padding-top:@bubble-left-padding-top;
  --@{ai-kit-prefix}-bubble-left-padding-bottom:@bubble-left-padding-bottom;
  --@{ai-kit-prefix}-bubble-right-padding-left:@bubble-left-padding-left;
  --@{ai-kit-prefix}-bubble-right-padding-right:@bubble-left-padding-right;
  --@{ai-kit-prefix}-bubble-right-padding-top:@bubble-left-padding-top;
  --@{ai-kit-prefix}-bubble-right-padding-bottom:@bubble-left-padding-bottom;
  --@{ai-kit-prefix}-toast-content-bg: @toast-content-bg;
  --@{ai-kit-prefix}-toast-message-color: @toast-message-color;
  --@{ai-kit-prefix}-toast-typing-content-bg: @toast-typing-content-bg;
  --@{ai-kit-prefix}-toast-typing-message-color: @toast-typing-message-color;
  --@{ai-kit-prefix}-composer-compact-bg: @composer-compact-bg;
  --@{ai-kit-prefix}-composer-compact-input-color: @composer-compact-input-color;
}

@supports (top: constant(safe-area-inset-top)) {
  :root {
    --@{ai-kit-prefix}-safe-top: constant(safe-area-inset-top);
    --@{ai-kit-prefix}-safe-bottom: constant(safe-area-inset-bottom);
  }
}

@supports (top: env(safe-area-inset-top)) {
  :root {
    --@{ai-kit-prefix}-safe-top: env(safe-area-inset-top);
    --@{ai-kit-prefix}-safe-bottom: env(safe-area-inset-bottom);
  }
}

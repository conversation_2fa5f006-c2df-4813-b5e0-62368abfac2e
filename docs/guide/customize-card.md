---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 6
---

# 自定义卡片方案
<p>使用场景说明：当前组件内部定义了文本，markdown，图片，富文本，音频，视频，文件卡片，如果业务线有其他卡片需求，可以使用自定义方案实现。</p>  
<p>自定义卡片方案灵活性较高，且根据业务需求不同，最佳实践可能有所不同。因此对接自定义卡片前，可联系张敏生，陈常超共同针对业务需求为你评估最佳实践方案。</p>   
<p>下面提供2种通用的自定义卡片方案。</p>   

## 方案1：使用魔方搭建卡片UI
### 魔方卡片搭建流程步骤如下：
<img src="../../public/home/<USER>" alt="描述" width="40%" height="40%" style="margin-left: 30%;" />

- 1、搭建卡片：业务线在魔方平台上搭建卡片，定义好卡片的参数，如果需要自定义魔方组件，可以获取指定脚手架创建，搭建完成后发布卡片。
- 2、卡片绑定：业务线在管理端绑定用户意图与对应卡片id
- 3、卡片渲染：在接口中按照约定格式返回卡片消息数据给前端，前端即可正常渲染


### 卡片协议如下：

魔方卡片消息为SSE数据中消息体，结构如下；
```
   {
     type: 'card', //类型为card
     content: {
      url: 'http://lowcode.fe.htsc/app/447e0542ca8f79b1cbbc/editor', //魔方卡片的地址
      data: { }
     },
   },
```
| 字段名                           | 类型       | 描述                                 |
| -------------------------------- | ---------- | ------------------------------------ |
| url                             | `string` | 魔方卡片的地址, 格式为：域名+appId+params |
| data                    | `any` | 透传至魔方页面的props                   |

【注意】当config.bridge中配置了openWebpage时，默认通过data透传至魔方页面，供页面内选择是否用该方法打开子页面。

### 示例如下：
```
// 魔方卡片数据示例, 该消息是sse中的消息体数据
   {
     type: 'card', //类型为card
     content: {
      url: 'http://lowcode.fe.htsc/app/5cb810902dc3a68cb52f/editor?agentId=1000265', //魔方卡片的地址
      data: { 
        source:'SCK',  //自定义参数，透传至魔方页面
        openWebpage: config.bridge.openWebpage, // 默认透传全局配置的打开页面方法至魔方页面
      },
     },
   },
```

其中data对象是卡片需要的props，可以自行定义，确保魔方页面通过this.props接收后完成逻辑处理即可。比如，该示例中传入自定义字段source，用于在卡片内判断是否来源以做兼容。


支持卡片上的交互，例如点击弹窗、下转到二级页面等均在魔方页面内编辑，效果如下：

![显示效果](../../public/home/<USER>


<code src="./card/index.tsx" ></code>

## 方案2：通过自定义方法实现卡片UI渲染
业务线引用chat组件时，实现自定义renderMessageContent方法，与后端约定好消息的卡片type和content的格式，在方法中实现对应类型的卡片UI渲染，并传入该方法给chat组件，代码示例如下：

```
  假设约定消息格式为：
  {
    type: 'txt',
    content: { text: 'Hi，我是定制卡片~' },
  },

  //定义卡片方法,与后端约定卡片消息格式
  function renderMessageContent(msg: any) {
      const { type, content } = msg;
  
      // 根据消息类型来渲染
      switch (type) {
        case 'txt': //自定义卡片类型
          return <p>{content?.text}</p>; //自定义卡片UI
        default:
          return null;
      }
    }

  <AiChat
      ...
      renderMessageContent={renderMessageContent}
    >

```
注意自定义type不能与内置type冲突，内置type有text,image,markdown,richtext,video,thinking,references,relatedQuestionList,file,card。
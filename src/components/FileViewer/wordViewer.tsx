/* 使用docx-preview插件预览word文件 */
import React, { useRef, useEffect } from "react";
// @ts-ignore
import { renderAsync } from "docx-preview";
import CloseIcon from '../Navbar/images/closeIcon.svg';

interface WordPreviewByUrlProps {
    fileUrl: string;
    handleClose: () => void;
}

const WordPreviewByUrl = ({ fileUrl, handleClose }: WordPreviewByUrlProps) => {
    const previewContainerRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        const fetchAndRenderWord = async () => {
            try {
                const xhr = new XMLHttpRequest();
                xhr.open("GET", fileUrl, true);
                xhr.responseType = "arraybuffer";
                xhr.onload = async () => {
                    if (xhr.status === 200) {
                        const arrayBuffer = xhr.response;
                        if (previewContainerRef.current) {
                            previewContainerRef.current.innerHTML = ""; // 清空之前的内容
                            await renderAsync(arrayBuffer, previewContainerRef.current
                            );
                            // 调整文档内容宽度
                            const adjustDocWidth = () => {
                                const container = previewContainerRef.current as HTMLElement;
                                const content = container?.querySelector('.docx') as HTMLElement;
                                if (content) {
                                    const containerWidth = container.offsetWidth;
                                    const contentWidth = content.scrollWidth;
                                    if (contentWidth > containerWidth) {
                                        content.style.transform = `scale(1)`;
                                        content.style.transformOrigin = 'left top';
                                        content.style.width = '100%';
                                        content.style.padding = '10px';
                                    } else {
                                        content.style.transform = 'scale(1)';
                                        content.style.transformOrigin = 'left top';
                                        content.style.width = '100%';
                                    }
                                }
                            }
                            adjustDocWidth()
                            window.addEventListener('resize', adjustDocWidth);

                            // 强制调整图片的宽度
                            const adjustImages = () => {
                                if (!previewContainerRef.current) return;
                                const images = previewContainerRef.current.querySelectorAll("img");
                                images.forEach((img) => {
                                    const parentDiv = img.parentNode as HTMLElement;
                                    parentDiv.style.width = '100%';
                                    img.style.maxWidth = "100%"; // 限制图片最大宽度
                                    img.style.height = "auto"; // 保持图片比例
                                    img.style.objectFit = "contain"; // 保持图片比例
                                });
                            };

                            setTimeout(adjustImages, 500); // 等待渲染完成后调整图片
                            const images = previewContainerRef.current.querySelectorAll("img");
                            images.forEach((img) => {
                                img.onload = adjustImages;
                            })
                            // 监听动态变化
                            const observer = new MutationObserver(adjustImages);
                            observer.observe(previewContainerRef.current, {
                                childList: true,
                                subtree: true,
                            });
                            return () => { window.removeEventListener('resize', adjustDocWidth); observer.disconnect(); }

                        } else {
                            return false;
                        }

                    } else {
                        console.error("Failed to fetch the file:", xhr.statusText);
                        return false;
                    }
                };
                xhr.send();
            } catch (error) {
                console.error("加载 Word 文件失败:", error);
            }
        };

        if (fileUrl) {
            fetchAndRenderWord();
        }
    }, [fileUrl]);

    return (
        <div className="WordViewWrap">
            <div className='closeWrap'>
                <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
            </div>
            <div
                ref={previewContainerRef}
                className="WordContent"
                style={{
                    fontSize: '14px',
                    transform: 'scale(1)',
                    transformOrigin: '0 0'
                }}
            >
                {/* Word 文件内容会渲染到这里 */}
            </div>
        </div>
    );
};

export default WordPreviewByUrl;

// 使用方式
// <WordPreviewByUrl fileUrl="https://example.com/sample.docx" />

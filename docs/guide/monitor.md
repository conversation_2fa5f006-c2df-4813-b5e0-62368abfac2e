---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 5
---

# 可观测方案


用户端反馈可观测作为可观测能力重要一环，SCK提供快速接入LangFuse可观测平台能力

在集成SCK chat组件时，根据以下方式可以快速接入LangFuse平台，接入后即可在LangFuse平台查看用户点赞点踩反馈信息。

```

  //1、引入SDK
  import { LangfuseWeb } from 'langfuse';

  //2、绑定langfuse平台的publicKey与接口请求的地址baseUrl，以下为示例
  const langfuseWeb = new LangfuseWeb({
    publicKey: 'pk-lf-199aa5e6-348d-4305-9368-6c11bea6674b',
    baseUrl: '/llmops/connect/langfuse-web', // 🇪🇺 EU region
    // baseUrl: "https://us.cloud.langfuse.com", // 🇺🇸 US region
  });

  //3、按照下述实现配置SCK的反馈回调方法onFeedback
  const config = {
    ...

    // SCK反馈接口回调请求 
    onFeedback: (...params: string[]) => {
      if( params && params[0] && params[1] ) {    
          langfuseWeb.score({
            traceId: params[1]?.messageId,
            name: "user_feedback",
            value: params[0] == 'good' ? 1 :0,
         });
      }
    }
    
    //4、将配置传入sck的chat组件
    return (
        <WideAiChat
          ...
          config={config}}
        />
    );
```
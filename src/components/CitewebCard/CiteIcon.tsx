import React from 'react';


type CiteIconProps = {
  className?: string;
};


export default function CiteIcon(props: CiteIconProps) {
  return (
    <svg
      fill="none"
      version="1.1"
      width="32"
      height="32"
      viewBox="0 0 32 32"
      className={props?.className}
    >
      <defs>
        <clipPath id="master_svg0_54_02883">
          <rect x="0" y="0" width="32" height="32" rx="8" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_54_02883)">
        <rect x="0" y="0" width="32" height="32" rx="8" fill="#FFFFFF" fillOpacity="1" />
        <g>
          <g>
            <path
              d="M4.183564,21.365099999999998L10.6004,8.876065C10.87101,8.346745,11.41158,8.00990595,12.00598,8.0002147C12.60039,7.99052344,13.15165,8.30956,13.43937,8.829777L20.3448,21.2726C21.0425,22.530900000000003,19.8751,24.0087,18.493299999999998,23.6127L12.80678,21.980600000000003C12.51773,21.8977,12.21121,21.8977,11.922170000000001,21.980600000000003L6.05907,23.6607C4.7013,24.0498,3.537252,22.6252,4.183564,21.3669L4.183564,21.365099999999998Z"
              fill="#3065F3"
              fillOpacity="1"
            />
          </g>
          <g>
            <path
              d="M23.29009170349121,22.8515L25.94049170349121,23.6127C27.32056170349121,24.0087,28.48976170349121,22.532600000000002,27.79196170349121,21.2726L20.88485170349121,8.829777C20.59713170349121,8.309561,20.045871703491212,7.99052344,19.451461703491212,8.0002147C18.857061703491212,8.00990595,18.31649170349121,8.346745,18.04588170349121,8.876065L17.63326670349121,9.678889999999999C17.180227703491212,10.56036,17.19177170349121,11.60871,17.66411070349121,12.48L23.28503170349121,22.8485C23.28604170349121,22.8503,23.287981703491212,22.8515,23.29009170349121,22.8515Z"
              fill="#7A9EFF"
              fillOpacity="1"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}

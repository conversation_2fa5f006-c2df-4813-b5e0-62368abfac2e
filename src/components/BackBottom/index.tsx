import React, { useEffect } from 'react';
// import BackBottomIcon from './images/back-bottom.svg'
import BackBottomIcon from './images/back-bottom.png';

interface BackBottomProps {
  count?: number;
  onClick: () => void;
  onDidMount?: () => void;
  backBottomButton?: { icon?: string }
}

export const BackBottom = ({ onClick, onDidMount, backBottomButton }: BackBottomProps) => {

  useEffect(() => {
    if (onDidMount) {
      onDidMount();
    }
  }, [onDidMount]);

  return (
    <div className="BackBottom">
      <img src={backBottomButton?.icon || BackBottomIcon} className="BackBottomIcon" onClick={onClick} />
    </div>
  );
};

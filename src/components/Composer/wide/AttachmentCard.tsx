import React from 'react';
import { Icon } from '../../Icon';
import { UploadFile } from './Upload';
import { Progress } from '../../Progress';
import clsx from 'clsx';

export type FileCardProps = {
  prefixCls: string;
  file: UploadFile;
  items: UploadFile[];
  onRemove?: (file: UploadFile) => void;
  onPreview?: (file: UploadFile, e: React.SyntheticEvent<HTMLElement>) => void;
  onDownload?: (file: UploadFile) => void;
  iconRender?: (file: UploadFile<any>) => React.ReactNode;
};

const AttachmentCard: React.FC<FileCardProps> = (props) => {
  const { prefixCls, file, onRemove, onPreview, iconRender } = props;
  const clsName = `${prefixCls}-filecard`;

  // Status: which will ignore `removed` status
  const { status } = file;
  const [mergedStatus, setMergedStatus] = React.useState(status);
  React.useEffect(() => {
    console.log('status', status);

    if (status !== 'removed') {
      setMergedStatus(status);
    }
  }, [status]);

  const [showProgress, setShowProgress] = React.useState(false);
  const progressRafRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);
  React.useEffect(() => {
    progressRafRef.current = setTimeout(() => {
      setShowProgress(false); //todo 暂禁用上传进度条
    }, 300);
    return () => {
      if (progressRafRef.current) {
        clearTimeout(progressRafRef.current);
      }
    };
  }, []);

  const isUploading = mergedStatus === 'uploading';

  const icon = React.useMemo(() => {
    if (mergedStatus === 'uploading') {
      return <Icon type="spinner" spin />;
    } else if (mergedStatus === 'error') {
      return null;
    } else if (mergedStatus === 'done') {
      return iconRender?.(file) ?? <img  alt="" className={`${clsName}-icon`} />;
    }
    return null;
  }, [clsName, iconRender, file, mergedStatus]);

  return (
    <div
      className={clsx(`${clsName}`, mergedStatus === 'error' ? `error` : null)}
      onClick={(e) => onPreview?.(file, e)}
    >
      {icon}
      <span className={`${clsName}-name`}>{file.name}</span>
      <Icon type="x-circle-fill" onClick={() => onRemove?.(file)} className={`${clsName}-remove`} />
      {/* todo showProgress */}
      {showProgress && isUploading && 'percent' in file && (
        <Progress value={file.percent ?? 0} status="active"></Progress>
      )}
    </div>
  );
};

export default AttachmentCard;

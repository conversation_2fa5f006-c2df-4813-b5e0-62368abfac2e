.operations {
  width: 100%;
  height: 24px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;

  .operationButton {
    min-width: 36px;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border: 0.5px solid #dddee0;
    background: #ffffff;
    padding: 3px 6px;
    border-radius: 4px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    color: #1d222c;
  }

  .operationButton:not([disabled]):hover {
    color: #1677ff;
    border: 0.5px solid #91caff;
  }

  .operationButton[disabled] {
    color: #cccccc;
  }
}

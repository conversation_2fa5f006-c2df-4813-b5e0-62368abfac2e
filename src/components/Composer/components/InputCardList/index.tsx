/*
 * @Author: 020446 <EMAIL>
 * @Date: 2025-08-12 18:43:32
 * @LastEditors: 020446 <EMAIL>
 * @LastEditTime: 2025-08-22 11:29:01
 */
import React from 'react';
import InputCard from '../InputCard';
import type {
  InputCard as InputCardProps,
  InputType,
  Operation,
} from '../type';

import Operations from '../Operations';
import './style.less';
import ScrollBtnSvg from './svg/scrollButton.svg';

export interface InputCardListProps {
  inputCardList: InputCardProps[];
  setInputCardList?: (list: InputCardProps[]) => void;
  operationConfig: Record<InputType, Operation[]>;
}

export default function InputCardList(props: InputCardListProps) {
  const { inputCardList, operationConfig, setInputCardList } = props;

  const [showScrollBtn, setShowScrollBtn] = React.useState(false);
  const listContainerRef = React.useRef<HTMLDivElement>(null);
  const listRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (
      listRef.current &&
      listRef.current.scrollWidth > listRef.current.clientWidth
    ) {
      setShowScrollBtn(true);
    } else {
      setShowScrollBtn(false);
    }
  }, [inputCardList]);

  const onScrollButtonClick = () => {
    if (listContainerRef.current && listRef.current) {
      listContainerRef.current.scrollLeft = Math.min(
        listContainerRef.current.scrollLeft + 200,
        listRef.current.scrollWidth
      );
    }
  };
  const operationList: Operation[] = React.useMemo(() => {
    if (inputCardList.length === 0) {
      return [] as Operation[];
    }
    if (inputCardList.length === 1) {
      return operationConfig[inputCardList[0].type] || [];
    }

    const typeTemp = inputCardList[0].type;
    for (const item of inputCardList) {
      if (item.type !== typeTemp) {
        return [] as Operation[];
      }
    }
    return operationConfig[typeTemp] || ([] as Operation[]);
  }, [inputCardList, operationConfig]);

  const onCardDelete = (index: number) => {
    console.log('onCardDelete:', index, inputCardList[index]);

    const newArr = Array.from(inputCardList);
    newArr.splice(index, 1);

    if (setInputCardList) {
      setInputCardList(newArr);
    }
  };
  return (
    <div className="inputCardList">
      <div className="listContainer" ref={listContainerRef}>
        <div className="list" ref={listRef}>
          {inputCardList?.map((card, index) => (
            <InputCard
              {...card}
              key={index}
              onDelete={() => onCardDelete(index)}
            />
            // <div
            //   style={{
            //     width: `${100 + index * 10}px`,
            //     height: '58px',
            //     backgroundColor: '#ff0000',
            //     flexShrink: 0,
            //   }}
            // />
          ))}
        </div>
        {operationList.length > 0 && (
          // <div className="operations">
          <Operations operations={operationList} />
          // </div>
        )}
      </div>

      {showScrollBtn && (
        <div className="scrollButton" onClick={onScrollButtonClick}>
          <img src={ScrollBtnSvg} />
        </div>
      )}
    </div>
  );
}

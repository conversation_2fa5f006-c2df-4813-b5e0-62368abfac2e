/*
 * @Author: 016645
 * @Description: 反馈功能演示组件
 * @Date: 2025-04-02 19:29:31
 */
import React, { useCallback } from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button } from '../../../src';
import { useFeedback } from '../../../src/hooks/aiAgentChat/index';

/**
 * 反馈功能演示组件
 * 展示如何使用useFeedback钩子提交用户反馈
 */
export default function FeedbackDemo() {
  // 初始化反馈钩子
  const { 
    loading: feedbackLoading, 
    error: feedbackError,
    run: submitFeedback 
  } = useFeedback({
    // 请求配置
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
      type: 'http',
      manual: true,
      headers: { 
        empid: '002332', 
        token: 'token', 
        'iv-user': '002332', 
        'deviceId': 'deviceId' 
      },
    },
    // 请求参数
    requestParams: {
      userId: '002332',
      conversationId: '16ba2e40-4f4f-4dbb-b690-d1440b6b99d0',
      messageId: '0217436762676676772e928cf7b101d9838bd97851ecdae1d7d2f',
      score: 'good',
      type: 'submit', // 接口定义用的是type而不是submitType
    },
  });

  // 处理提交反馈
  const handleSubmitFeedback = useCallback(() => {
    submitFeedback();
  }, [submitFeedback]);

  return (
    <DemoPage>
      <DemoSection title="用户反馈演示">
        <Button 
          onClick={handleSubmitFeedback} 
          loading={feedbackLoading}
        >
          提交反馈
        </Button>
        
        {feedbackError && (
          <div style={{ color: 'red', marginTop: '8px' }}>
            {feedbackError.message}
          </div>
        )}
      </DemoSection>
    </DemoPage>
  );
}
---
nav:
  title: hooks
  order: -1
group:
  title: API
  order: 20
order: 2
---

# 对话聊天

**useSendMessage**

```js
import { useSendMessage } from '@ht/chatui';
```

## HiAgent

### 示例

<code src="./demo-hiagent.tsx" ></code>

### Options

requestConfig
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestConfig.type | 请求类型:tcp/http | `string` | `http` |
| requestConfig.isAibag | 是否是aibag包装的数据格式 | `boolean` | `flase` |
| requestConfig.needCreateConversation | 是否需要创建会话 | `boolean` | `flase` |
| requestConfig.createConversationUrl | 自定义创建会话接口url | `string` | - |
| requestConfig.createConversationMethod | 自定义创建会话接口方法 | `functon` | - |
| requestConfig.needUpdateConversation | 是否需要更新会话 | `boolean` | `false` |
| requestConfig.updateConversationUrl | 自定义更新会话接口url | `string` | `http` |
| requestConfig.action | 站内请求action | `string` | - |
| requestConfig.tcpRequest | 站内请求方法 | `functon` | - |
| requestConfig.method | 请求类型 | `string` | `post` |
| requestConfig.paramsKey | 站内请求时外层包裹key | `string` | - |
| requestConfig.requestTransfer | 请求转换器 | `function` | - |
| requestConfig.responseTransfer | 响应转换器 | `function` | - |
| requestConfig.manual | 是否手动触发 | `boolean` | `false` |
| requestConfig.headers | 头信息 | `object` | - |

requestParams
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestParams.userId | 用户id | `string` | - |
| requestParams.conversationId | 会话id | `string` | - |
| requestParams.question | 问题 | `string` | - |
| requestParams.questionExtends | 扩展问题 | `object` | - |
| requestParams.isFirstChat | 是否是首次问答 | `boolean` | `false` |
| requestParams.stream | 是否流式 | `boolean` | `false` |
| requestParams.inputs | 会话变量 | `object` | - |
| requestParams.appKey | appkey | `string` | - |


## 自定义服务

### 示例

<code src="./demo-custom.tsx" ></code>

### Options

requestConfig
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestConfig.method | 请求类型 | `string` | `post` |
| requestConfig.requestTransfer | 请求转换器 | `function` | - |
| requestConfig.responseTransfer | 响应转换器 | `function` | - |
| requestConfig.manual | 是否手动触发 | `boolean` | `false` |
| requestConfig.headers | 头信息 | `object` | - |

requestParams
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestParams.appId | 应用id | `string` | - |
| requestParams.userId | 用户id | `string` | - |
| requestParams.conversationId | 会话id | `string` | - |
| requestParams.question | 问题 | `string` | - |
| requestParams.questionExtends | 扩展问题 | `object` | - |
| requestParams.model | 模型 | `string` | - |
| requestParams.stream | 是否流式 | `boolean` | `false` |
| requestParams.enableInternetSearch | 是否联网搜索 | `boolean` | `false` |
| requestParams.extendParams | 扩展参数,平铺传递 | `object` | - |

---
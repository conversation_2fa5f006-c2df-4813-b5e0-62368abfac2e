/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useDeleteConversation } from '@ht/chatui';

export default () => {

  const { data: conversationData, loading: conversationLoading, run: conversationRun, error: conversationError } = useDeleteConversation({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      manual: true,
      type: 'http',
    },
    requestParams: {
      userId: '016645',
      appKey: 'd07gpo0im975aeu764t0',
      conversationId: 'd07irt1stbuc5krfr3pg',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>删除会话</Button>
      {conversationData && !conversationError ? (
        <span style={{ padding: '10px', color: 'green'}}>会话创建成功</span>
      ) : ''}
    </div>
    )
}

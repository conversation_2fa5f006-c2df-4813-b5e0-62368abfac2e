import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { Message } from '../../../src';
import { toast } from '../../../src';

export default () => (
  <DemoPage>
    <DemoSection title="文本消息">
      <Message
        _id={1}
        messageId={1}
        type="text"
        content={{ text: '尊敬的客户您好！请问什么可以帮到您？' }}
        position="left"
        config={{}}
        updateMsg={() => {}}
      />
      <Message
        _id={2}
        messageId={3}
        type="text"
        content={{ text: 'Hello' }}
        position="right"
        config={{}}
        updateMsg={() => {}}
      />
    </DemoSection>
    <DemoSection title="图片消息">
      <Message
        _id={1}
        messageId={1}
        type="text"
        content={{ text: '您好！' }}
        position="left"
        config={{}}
        updateMsg={() => {}}
      />
      <Message
        _id={2}
        messageId={2}
        type="image"
        content={{ picUrl: 'https://d.inst-service.htsc.com/s3-001005-inst/public/ai/1742865406332.png' }}
        position="left"
        config={{}}
        updateMsg={() => {}}
      />
    </DemoSection>
    <DemoSection title="支持姓名、头像及时间显示">
      <Message
        _id={1}
        messageId={1}
        type="text"
        content={{ text: '尊敬的客户，您好！' }}
        position="left"
        user={{
          name: '投顾1',
          avatar: '//gw.alicdn.com/tfs/TB1U7FBiAT2gK0jSZPcXXcKkpXa-108-108.jpg'
        }}
        createdAt={new Date().getTime()}
        config={{}}
        updateMsg={() => {}}
      />
      <Message
        _id={2}
        messageId={2}
        type="text"
        content={{ text: '您好' }}
        position="right"
        user={{
          name: '客户1',
          avatar: '//gw.alicdn.com/tfs/TB1U7FBiAT2gK0jSZPcXXcKkpXa-108-108.jpg'
        }}
        config={{}}
        updateMsg={() => {}}
      />
    </DemoSection>
    <DemoSection title="支持弱文字提示，和点赞踩功能自定义">
      <Message
          _id={1}
          messageId={1}
          type="text"
          content={{ text: '尊敬的客户，您好！' }}
          position="left"
          showHallucination={true}
          needFeedback={true}
          onFeedBack={() => {toast.success('可以对回调函数自定义')}}
          config={{}}
          updateMsg={() => {}}
        />
      <Message
        _id={2}
        messageId={2}
        type="text"
        content={{ text: '您好！' }}
        position="right"
        config={{}}
        updateMsg={() => {}}
      />
    </DemoSection>
    <DemoSection title="支持自定义“正在思考中”的样式">
      <Message
        _id={1}
        messageId={1}
        type="text"
        content={{ text: '尊敬的客户，您好！' }}
        position="left"
        showHallucination={true}
        customThinkingStyle={<div>正在思考中</div>}
      />
    </DemoSection>
  </DemoPage>
);

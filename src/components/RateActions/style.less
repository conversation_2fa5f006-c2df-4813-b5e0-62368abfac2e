.RateActions {
  position: relative;
  z-index: @zindex-rate-actions;
  align-self: flex-end;
  width: 32px;
  margin: 0 8px;
}

.RateBtn {
  padding: 6px;
  border-radius: 50%;
  background: @rate-btn-bg;
  font-size: 20px;

  & + .RateBtn {
    margin-top: 9px;
  }
  &[data-type='up'] {
    &:hover,
    &.active {
      color: @brand-1;
    }
  }
  &[data-type='down'] {
    &:hover,
    &.active {
      color: @blue;
    }
  }
}

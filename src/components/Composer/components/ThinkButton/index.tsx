import { Button } from '@ht/sprite-ui';
import React from 'react';
import ThinkIcon from './icon.svg';
import ThinkIconSelect from './iconSelect.svg';

import './style.less';

export interface ThinkButtonProps {
  isReasoningLLM: boolean; // 是否是推理模型
}
export default function ThinkButton(props: ThinkButtonProps) {
  const { isReasoningLLM } = props;
  React.useEffect(() => {}, []);

  return (
    <Button
      className="thinkButton"
      onClick={() => {}}
      style={
        isReasoningLLM
          ? { color: '#1677ff', border: '0.5px solid #91caff' }
          : {}
      }
    >
      <img src={isReasoningLLM ? ThinkIconSelect : ThinkIcon} />
      <span>思考</span>
    </Button>
  );
}

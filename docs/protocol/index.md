---
nav:
  title: 通信协议
  order: -1
group:
  title: 通信协议
  order: -1
order: -1
---

# 通信协议


## 1.问答接口

### 入参

| 字段名               | 类型            | 描述                                                                                                                                                                                                                                                                                                                                                                        |
| -------------------- | --------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| appId                | `string`     | 系统id，标识业务平台，如 `"aorta"`                                                                                                                                                                                                                                                                                                                                        |
| userId               | `string`     | 当前系统登录用户id，如聊他empid、如果未登录生成一个唯一id                                                                                                                                                                                                                                                                                                                   |
| question             | `string`     | 问题文本                                                                                                                                                                                                                                                                                                                                                                    |
| conversationId       | `string`     | 会话id，来自初始化接口（首次可以为空，非首次接口返回后下次带入）                                                                                                                                                                                                                                                                  |
| model                | `string` | 当前使用模型字典，可选，如"deep_seek"                                                                                                                                                                                                                                                                                                                                                |
| enableInternetSearch | `boolean`        | 是否支持联网搜索，可选                                                                                                                                                                                                                                                                                                                                                            |
| appKey               | `string`      | hiagent接口涉及，鉴权字段，可选                                                                                                                                                                                                                                                                                                                                                       |
| inputs               | `object`      | hiagent接口涉及，agent变量,可选                                                                                                                                                                                                                                                                                                                                                      |
| agentId               | `string`      | 指定智能体id,可选                                                                                                                                                                                                                                                                                                                                                      |
| questionExtends               | `object`      | 扩展字段，如上传的附件信息                                                                                                                                                                                                                                                                                                                                                      |
|actions      | `object[]`      | actions列表,可选，如：<br>[ {<br> name: string,   // action的唯一标识 <br> description: string ,   // action的作用描述 <br> },] |

### 出参

| 字段名 | 类型 | 描述 |
| ---- | ---- | ---- |
| code | `string` | 表示成功，非‘0’表示失败 |
| msg | `string` | 失败时的错误消息 |
| resultData | `{ TMessage }` | |

#### TMessage字段说明
| 字段名 | 类型 | 描述 |
| ---- | ---- | ---- |
| conversationId | `string` | 会话ID |
| messageId | `string` | 消息ID |
| list | `obj[]` | 消息列表数组，具体格式参考下表，内置类型包括：文本，富文本，markdown，图片，视频，文件，魔方卡片 |
| list[].type | `string` | 消息类型，如'text' |
| list[].content | `object` | 消息内容 |
| createTime | `number` | 创建时间戳，如1746804521439 |
| role | `string` | 发送者信息 ，user：用户，assistant: 智能助手 |
| model | `string` | 大模型来源，例如TEC，GPT |
| relatedQuestionList | `obj[]` | 关联问题 |
| relatedQuestionList[].title | `string` | 例如：'天气？' |
| relatedQuestionList[].content | `string` | 例如：'今天天气怎么样？' |
| relatedQuestionList[].url | `string` | 如果需要跳转，对应跳转url |
| needFeedback | `boolean` | 是否展示点赞点踩，兜底的话术不需要 |
| feedbackResult | `string` | 点赞点踩结果, ‘good’-点赞，‘bad’-点踩 |
| endFlag | `boolean` | sse接口时返回，当前消息响应状态，当前回答是否结束 |
| shootIntention | `false` | 可选，大模型是否命中字段，如未命中生成一条内容为backUpTalking的兜底消息 |
| taskId | `string` | 可选，气泡消息的唯一Id，一条气泡追加文本靠此字段知道加哪条气泡上 |
| backUpTalking | `string` | 可选，兜底消息 |
| totalTime | `string` | 总耗时，单位秒，可选 |
| totalTokens | `string` | 总消耗tokens数量，可选 | 
| queryExtends | `string` | 问题的附件参数，对应入参的questioExtends，当role='user'返回，可选 |
| action | `string` | 可选，后端根据入参actions及场景进行决策，返回给前端需要执行的acton名称，可选 |

#### 内置消息卡片内容说明
| 卡片消息类型 | 页面效果 | 消息格式 |
| ---- | ---- | ---- |
| text文本消息 | ![显示效果](../../public/home/<USER>/text.png) | {<br>        type: 'text',<br>        content: {<br>          text: '智能助理为您服务，请问有什么可以帮您？',<br>        },<br>      } |
| 富文本消息 |  ![显示效果](../../public/home/<USER>/richtext.png)  | {<br>        type: 'richtext',<br>        content: {<br>          text: '富文本内容',<br>        },<br>      } |
| markdown消息 |  ![显示效果](../../public/home/<USER>/markdown.png)  | {<br>        type: 'markdown',<br>        content: {<br>          text: 'Markdown 表格示例如下：\n\|左对齐\|右对齐\|居中对齐\|\n\|:---\|----:\|:----:\|\n\|单元格\|单元格\|单元格\|',<br>        },<br>      } |
| 图片消息 |  ![显示效果](../../public/home/<USER>/image.png)  | {<br>        type: 'image',<br>        content: {<br>          picUrl: 'https://gw.alicdn.com/tfs/TB1j2Y3xUY1gK0jSZFMXXaWcVXa-602-337.png'<br>        }<br> |
| 文件卡片消息 |  ![显示效果](../../public/home/<USER>/file.png)  | {<br>        type: 'file',<br>        content: {<br>          "name": "这是一个word文件",<br>          "url": "https://c.zhangle.com/airobottest/iwen/material/docs/文件9.docx"<br>        }<br>      } |
| 视频卡片消息 |  ![显示效果](../../public/home/<USER>/video.png) | {<br>        type: 'video',<br>        content: {<br>          url: 'https://alime-channel.oss-cn-zhangjiakou.aliyuncs.com/customer-upload/1715943072999_d6adce42366a4eabb773d1251ca224ff.mov?Expires=1716375078&OSSAccessKeyId=LTAI5tApwLpSAsD6RjcwxwKJ&Signature=jdsuqZvN44J9x9KUzU6CXDCVUuI%3D'<br>        }<br>      } |
| 魔方卡片消息 |  ![显示效果](../../public/home/<USER>/lccard.png) | {<br>        type: 'card',<br>        content: {<br>          url: 'http://lowcode.fe.htsc/app/447e0542ca8f79b1cbbc/editor',<br>          data: { <br>              annReturnTot: 7.85722786,<br>              companyQuality: { managerName: '基金管理人', rankPercent: 69.0 },<br>              exchangeCode: 'OF',<br>              existing: true,<br>              htTypeCodeii: '002002',<br>              htTypeNameii: '灵活配置基金',<br>              opinionDate: 1726761600000,<br>              secuabbr: '华夏成长混合A',<br>              secucode: 53110,<br>              simpleEvaluateResults: [<br>                { conclusion: '历史收益能力一般', rankPercent: 34.0 },<br>                { conclusion: '风险控制能力一般', rankPercent: 41.0 },<br>                { conclusion: '择券配置能力较差', rankPercent: 14.0 },<br>                { conclusion: '投资独立性优秀', rankPercent: 100.0 }<br>              ],<br>              totalScoreScript: {<br>                highlightKeyWords: ['33.42', '较差'],<br>                lowlightKeyWords: [],<br>                script: '本基金近1年得分为33.42，超过12%的基金，总体表现较差'<br>              },<br>              tradingCode: '000001'<br>            }<br>          <br>        }<br>      } |
| 思维链消息 | ![显示效果](../../public/home/<USER>/thinking.png) | {<br>        type: 'thinking',<br>        content: {<br>          text: '让我想一想'<br>        }<br>      } |
| 参考引用 | ![显示效果](../../public/home/<USER>/refer.png) | {<br>        type: 'references',<br>        references: [{<br>          "title":"豆包(中国传统食品)-百科",<br>         "url":"https://m.baike.com/wiki/%E8%B1%86%E5%8C%85/447550"<br>        }]<br>      } |

## 接口说明

- 问答接口，支持sse与非sse两种方式。


## 2. 停止sse生成接口

### 入参

| 字段名         | 类型        | 描述                                         |
| -------------- | ----------- | -------------------------------------------- |
| appId          | `string` | 系统id，标识不同平台，如 `"aorta"` |
| userId         | `string` | 当前系统用户id              |
| conversationId | `string` | 会话id                                       |
| messageId      | `string` | 消息id                                       |
| taskId         | `string` | 任务id                                       |

### 出参

| 字段名 | 类型       | 描述                                |
| ------ | ---------- | ----------------------------------- |
| code   | `string` | `'0'` 表示成功，非 `0` 表示失败 |
| msg    | `string` | 失败时的错误消息                    |

## 接口说明

- 仅针对sse接口，用于终止sse接口流式输出

## 3. 历史会话列表

### 入参

| 字段名 | 类型        | 描述                                   |
| ------ | ----------- | -------------------------------------- |
| appId  | `string` | 系统id，标识平台，例如 `"aorta"` |
| userId | `string` | 当前系统登录用户id    |
| appKey | `string`  | hiagent接口涉及，鉴权字段，可选                |

### 出参

| 字段名                           | 类型       | 描述                                 |
| -------------------------------- | ---------- | ------------------------------------ |
| code                             | `string` | `'0'` 表示成功，非 `0` 表示失败  |
| msg                              | `string` | 失败时的错误消息                     |
| resultData.list[].conversationId | `string` | 会话id                               |
| resultData.list[].question       | `string` | 该笔会话的第一个问题                 |
| resultData.list[].createTime     | `string` | 会话创建时间（会话第一条消息时间）   |
| resultData.list[].updateTime     | `string` | 会话更新时间（会话最后一条消息时间） |
| resultData.list                  | `array`  | 包含会话相关信息的数组               |
| resultData                       | `object` | 包含 `list` 等相关数据的对象       |

## 接口说明

- 展示30天内的历史会话列表

- 排序默认按照updateTime

## 4. 历史消息详情接口

### 入参

| 字段名         | 类型                                 | 描述                                 |
| -------------- | ------------------------------------ | ------------------------------------ |
| appId          | `string`                          | 系统id，标识业务平台，如 `"aorta"` |
| userId         | `string`                          | 当前系统登录用户id，如聊他empid      |
| pageNum        | `number` | 分页页码                             |
| pageSize       | `number`             | 分页大小,默认值为 `6`                             |
| conversationId | `string`  | 会话id                               |
| appKey         | `string`                           | hiagent接口涉及，鉴权字段，可选              |

### 出参

| 字段名            | 类型           | 描述                                                                                       |
| ----------------- | -------------- | ------------------------------------------------------------------------------------------ |
| code              | `string`     | `'0'` 表示成功，非 `0` 表示失败                                                        |
| msg               | `string`     | 失败时的错误消息                                                                           |
| resultData.list   | `TMessage[]` | 一个包含 `TMessage` 类型元素的数组，推测 `TMessage` 是一个自定义类型，包含相关消息数据 |
| resultData.noMore | `Boolean`    | 当不存在更多消息时，该值为 `true`                                                        |
| resultData        | `object`     | 包含 `list` 和 `noMore` 字段的对象                                                     |

## 接口说明

- 点击历史会话进入历史会话详情页面调用，支持分页


## 5. 点赞/点踩接口

### 入参

| 字段名             | 类型         | 描述                                                                                        |
| ------------------ | ------------ | ------------------------------------------------------------------------------------------- |
| appId              | `string`  | 系统id，标识不同业务平台，例如 `"aorta"`                                                  |
| userId             | `string`  | 当前系统唯一用户id，如聊他empid                                                             |
| conversationId     | `string`   | 会话id                                                                                      |
| messageId          | `string`   | 消息id                                                                                      |
| score              | `string`   | 值为 `"good"` 或 `"bad"` ，可能表示评分相关                                             |
| type               | `string`   | 值为 `"submit"` 或 `"reset"` ，首次点击传 `"submit"` ，已点击过再次点击传 `"reset"` |
| reason.selectedIds | `string[]` | 用户选中的快捷原因标签id列表（点踩原因相关，可选）                                          |
| reason.inputReason | `string`   | 用户输入的原因（点踩原因相关，可选）                                                        |
| appKey             | `string`   | hiagent接口涉及，鉴权字段，可选                                                                     |

### 出参

| 字段名 | 类型       | 描述                                                   |
| ------ | ---------- | ------------------------------------------------------ |
| code   | `string` | 当值为 `'0'` 时表示操作成功，非 `0` 值表示操作失败 |
| msg    | `string` | 用于存储失败时的错误消息，成功时内容可根据实际情况而定 |

## 接口说明

- 同步接口，第一次点击后等结果返回才可以点第二次


## 6. 初始化创建会话接口 -可选

### 入参

| 字段名 | 类型        | 描述                                                            |
| ------ | ----------- | --------------------------------------------------------------- |
| appId  | `string` | 租户系统的标识 ID，例如业务平台名称 “aorta”       |
| userId | `string` | 当前系统中唯一的用户 ID，比如 “聊他” 平台的 empid |
| appKey | `string`  | hiagent接口涉及，鉴权字段，可选                           |

### 出参

| 字段名                    | 类型       | 描述                                |
| ------------------------- | ---------- | ----------------------------------- |
| code                      | `string` | `'0'` 表示成功，非 `0` 表示失败 |
| msg                       | `string` | 失败时的错误消息                    |
| resultData.conversationId | `string` | 会话 ID                             |
| resultData                | `object` | 包含 `conversationId` 字段的对象  |

## 接口说明

- 初始化接口,每次进入新会话页面调用，获取新会话id即conversationId


- 更多接口参考： https://xcg1a1l6ku.feishu.cn/wiki/S3gLwt0gaiE4zzkyScVcbtignug
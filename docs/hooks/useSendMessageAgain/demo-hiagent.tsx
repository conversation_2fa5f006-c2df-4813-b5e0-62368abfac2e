/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useSendMessageAgain } from '@ht/chatui';

export default () => {
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useSendMessageAgain({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      type: 'http',
      manual: true,
    },
    requestParams: {
      userId: '016645',
      appKey: 'd07gpo0im975aeu764t0',
      conversationId: 'd07irt1stbuc5krfr3pg',
      messageId: '01JS162KBMTHYQ0Y267BERSMEA',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>重新生成回复</Button>
      <div>
        {conversationData?.resultData?.list?.map((item: any, index: number) => (
          <span key={index}>{item.content.text}</span>
        ))}
      </div>
    </div>
    )
}

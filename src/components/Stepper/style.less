@step-icon-width: 24px;

.Stepper {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.Step {
  position: relative;
  padding-left: (@step-icon-width + 5px);
  padding-bottom: 18px;

  &:last-child {
    padding-bottom: 0;

    .Step-line {
      display: none;
    }
  }
}

.Step--active {
  .Step-dot {
    background: @brand-1;
  }
  .Step-title {
    span {
      color: @brand-1;
      font-weight: 500;
    }
  }
  .Step-desc {
    color: @gray-1;
  }
  &[data-status] {
    .Step-line {
      top: (@step-icon-width + 2px);
    }
    .Step-icon {
      color: @red;
    }
  }
  &[data-status='success'] {
    .Step-icon {
      color: @green;
    }
  }
}

.Step-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: @step-icon-width;
  height: @step-icon-width;
  font-size: @step-icon-width;
  display: flex;
  justify-content: center;
  align-items: center;
}

.Step-dot {
  width: 8px;
  height: 8px;
  background: @white;
  border: 1px solid @brand-1;
  border-radius: 50%;
}

.Step-line {
  position: absolute;
  top: (@step-icon-width / 2 + 5px + 3px);
  left: (@step-icon-width / 2);
  bottom: -4px;
  width: 1px;
  background: @brand-1;
  opacity: 0.5;
}

.Step-title {
  display: flex;
  align-items: center;
  color: @gray-1;
  font-size: @font-size-md;

  small {
    margin-left: 12px;
    color: @gray-3;
    font-size: @font-size-xs;
  }
}

.Step-desc {
  margin-top: 3px;
  color: @gray-3;
  font-size: @font-size-sm;
}

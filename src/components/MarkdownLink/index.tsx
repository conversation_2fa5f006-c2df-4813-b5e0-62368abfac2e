import React from 'react';
import { CustomLinkProps } from './interface';

// 自定义链接组件
const MarkdownLink: React.FC<CustomLinkProps> = ({
  href,
  className,
  children,
  openPage,
}) => {

  // 点击处理函数
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();

    if (!href) return;

    if (openPage) {
      // 自定义链接点击打开方法
      openPage(href);
    } else {
      window.open(href, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <a
      href={href}
      className={className}
      onClick={handleClick}
      role="link"
      aria-label={typeof children === 'string' ? children : 'external link'}
    >
      {children}
    </a>
  );
};

export default MarkdownLink;
.Toast {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  z-index: @zindex-toast;
  display: flex;
  justify-content: center;
  transition: all 300ms ease 0s;
  transform: translateY(-50%);
  opacity: 0;
  visibility: hidden;

  &[data-type='success'] .Icon {
    color: @green;
  }

  &[data-type='success'] .Toast-content {
    background-color: #fff;
    height: 40px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.4);
    border-radius: 2px;
    // min-width: 182px;

    & .Toast-message {
      color: #333;
    }
  }

  &[data-type='error'] .Icon {
    color: @red;
  }

  &[data-type='loading'] .Icon {
    color: @brand-1;
  }

  &.show {
    opacity: 1;
    visibility: visible;
  }

  .Icon {
    margin-right: 6px;
    font-size: 24px;
  }
}

.Toast-top {
  top: 15%;
}

.Toast-bottom {
  top: auto;
  bottom: 15%;
}

.Toast-typing {
  top: auto;
  bottom: 5px;
  transform: none;
  right: 15px;
  justify-content: flex-end;

  .Toast-content {
    // background: #cfe2ff;
    background: @toast-typing-content-bg;
    border-radius: 20px;
    font-size: 12px;
    line-height: 30px;
    text-align: center;
    padding: 0 10px;
  }

  .Toast-message {
    line-height: 30px;
    // color: #3e74f7;
    color: @toast-typing-message-color;
  }
}

.Toast-sensitive {
  top: auto;
  bottom: 100%; // 整个飘在输入框上面
  transform: none;
  right: 0;
  justify-content: flex-end;

  .Toast-content {
    background: rgba(227,60,57,0.1);
    max-width: max-content;
    border-radius: 20px;
    font-size: 12px;
    line-height: 30px;
    padding: 0 10px;
  }

  .Toast-message {
    line-height: 30px;
    color: #e33c39;
  }
}

.Toast-content {
  display: flex;
  max-width: 300px;
  padding: 4px 15px;
  box-sizing: border-box;
  background: @toast-content-bg;
  border-radius: 16px;
  align-items: center;

  img {
    width: 14px;
    height: 14px;
    margin-right: 10px;
  }
}

.Toast-message {
  flex: 1;
  font-size: 14px;
  color: @toast-message-color;
  line-height: 24px;
  word-break: break-word;
  margin-top: 0;
  margin-bottom: 0;
}
---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 3
---

# 样式定制

## 1. 定制主题

SCK 提供了一套默认主题，CSS 命名采用类 BEM 的风格，方便使用者覆盖样式。如果你想完全替换主题色或者其他样式，可以使用下面提供的方法。

### CSS样式变量
SCK 对部分经常有定制需求的属性用了 CSS 变量 抽象，并大致分成了主题色、文字色、填充色、线条色、功能色等几类，你可以根据需求进行相应调整。

下面是一些基本的样式变量，更多的可以查看 [root.less](http://gitlab2.htsc/aorta-web/tools/chatui/-/blob/master/src/styles/root.less) 。

```js
:root {
  --@{ai-kit-prefix}-brand-1: #255ff2;
  --@{ai-kit-prefix}-brand-2: #78a7ff;
  --@{ai-kit-prefix}-brand-3: #f0f7ff;
  --@{ai-kit-prefix}-black: #000;
  --@{ai-kit-prefix}-white: #fff;
  --@{ai-kit-prefix}-gray-1: #333;
  --@{ai-kit-prefix}-gray-2: #666;
  --@{ai-kit-prefix}-gray-3: #999;
  --@{ai-kit-prefix}-gray-4: #ccc;
  --@{ai-kit-prefix}-gray-5: #ddd;
  --@{ai-kit-prefix}-gray-6: #eee;
  --@{ai-kit-prefix}-gray-7: #f5f5f5;
  --@{ai-kit-prefix}-gray-8: #f8f8f8;
  --@{ai-kit-prefix}-light-1: #eee;
  --@{ai-kit-prefix}-light-2: #f5f5f5;
  --@{ai-kit-prefix}-highlight-1: #255ff2;
  --@{ai-kit-prefix}-highlight-2: #78a7ff;
  --@{ai-kit-prefix}-link-color: #255ff2;
  --@{ai-kit-prefix}-blue: #255ff2;
  --@{ai-kit-prefix}-gray-dark: #333;
  --@{ai-kit-prefix}-green: #62d957;
  --@{ai-kit-prefix}-orange: #f70;
  --@{ai-kit-prefix}-red: #ff3634;
  --@{ai-kit-prefix}-yellow: #ffc233;
  --@{ai-kit-prefix}-yellow-light: #fff9db;
  --@{ai-kit-prefix}-font-size-xs: 12px;
  --@{ai-kit-prefix}-font-size-sm: 14px;
  --@{ai-kit-prefix}-font-size-md: 16px;
  --@{ai-kit-prefix}-font-size-lg: 18px;
  --@{ai-kit-prefix}-radius-sm: 4px;
  --@{ai-kit-prefix}-radius-md: 12px;
  --@{ai-kit-prefix}-radius-lg: 20px;
  --@{ai-kit-prefix}-shadow-1: 0 3px 4px 0 rgba(0, 0, 0, 0.04);
  --@{ai-kit-prefix}-shadow-2: 0 4px 8px 0 rgba(0, 0, 0, 0.08);
  --@{ai-kit-prefix}-shadow-3: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  --@{ai-kit-prefix}-safe-top: 0px;
  --@{ai-kit-prefix}-safe-bottom: 0px;
  --@{ai-kit-prefix}-gutter: 12px;
  --@{ai-kit-prefix}-btn-primary-border-color: transparent;
  --@{ai-kit-prefix}-btn-primary-bg: linear-gradient(90deg, #78a7ff 0%, #255ff2 98%);
  --@{ai-kit-prefix}-btn-primary-color: #fff;
}

@supports (top: constant(safe-area-inset-top)) {
  :root {
    --@{ai-kit-prefix}-safe-top: constant(safe-area-inset-top);
    --@{ai-kit-prefix}-safe-bottom: constant(safe-area-inset-bottom);
  }
}

@supports (top: env(safe-area-inset-top)) {
  :root {
    --@{ai-kit-prefix}-safe-top: env(safe-area-inset-top);
    --@{ai-kit-prefix}-safe-bottom: env(safe-area-inset-bottom);
  }
}

@supports (top: constant(safe-area-inset-top)) {
  :root {
    --@{ai-kit-prefix}-safe-top: constant(safe-area-inset-top);
    --@{ai-kit-prefix}-safe-bottom: constant(safe-area-inset-bottom);
  }
}

@supports (top: env(safe-area-inset-top)) {
  :root {
    --@{ai-kit-prefix}-safe-top: env(safe-area-inset-top);
    --@{ai-kit-prefix}-safe-bottom: env(safe-area-inset-bottom);
  }
}

```

### 定制方法
通过覆盖 CSS 变量的方法即可修改样式：

```js
// 引入样式文件
import '@ht/chatui/dist/index.css';
import '@ht/chatui/es/styles/index.less';
// 覆盖样式
:root {
  --brand-1: #46c800;
  --brand-2: #08b71e;
  --brand-3: #fcf8ec;
}
```


## 2. 样式覆盖
SCK 支持通过global关键字覆盖样式：

![](/home/<USER>

```js

  :global {
    .PushDivContainer {
      background: #fff;
    }

    .Message.left .Bubble {
      background: transparent;
      padding: 0;
    }

    .markdown-body hr {
      height: 2px;
    }

    .ReferencesWrap .ReferencesTitle {
      margin-bottom: 0;
    }
  }
```




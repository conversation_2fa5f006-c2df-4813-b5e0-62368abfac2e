/* eslint-disable import/no-extraneous-dependencies */
import React, { useCallback } from 'react';
import { log } from '@ht/xlog';
import dayjs from 'dayjs';
// import  {API_PREFIX} from '@lowcode/lc-render';
import LCRender from '@lowcode/lc-render';
import { DemoPage } from '../components';
import AiChat from '../../../src';
import LogoPng from './images/deepseekChat.png';
//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;


export default () => {
  // 公共全局配置
  const config = {
    //系统id：表示当前系统标识
    appId: 'system',

    //用户id：代表当前系统唯一用户id，动态获取
    userId: '016353',

    //场景id
    // sceneId: 'scene',
    
    //可选，用于魔方环境判断，测试环境调试
    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },

    //接口请求 method默认post
    requests: {
      baseUrl: '/hiAgent',//不同业务线可以自定义前缀
      // 如果对接泰为hiagent接口，需要设置platform='hiAgent'
      platform: 'hiAgent',
      // 对应泰为智能体的apikey，联系泰为平台获取
      // appKey: 'd0e4lcgim975aeu7usa0',
      appKey: 'd07gpo0im975aeu764t0',
      
      //问答接口
      send: {
        stream: true,//表示流式输出
        messageInterval:50,//可选，控制数据帧在页面显示的间隔，单位毫秒，默认30ms
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },
    },
    // 支持使用时传入指定方法并替换内部行为
    bridge: {
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },
    // 水印，可选，如果传了会话页面背景展示使用信息
    waterMark: {
      show: true,
      text: `张三       002332`,
    },
    //全局logo替换，涉及欢迎页，导航，会话页面
    robot: {
      logo: LogoPng,
      // showMsgLogo:true // 是否在消息答案左侧展示logo头像
    }
  }

  // 导航栏参数
  const navbarProps = {

    // open?: boolean,//是否展示navbar，默认true  

    // 返回按钮设置项
    // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
    // returnButtonIcon?: string;//返回按钮图标路径
    // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

    // 标题区域设置
    showLogo: false,//是否展示logo,默认为true            
    // logo: LogoPng,// logo图标的地址 
    title: 'AI智能助手', //头部标题文案，展示于logo右侧,默认为空字符串
    // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

    // 历史会话按钮设置项
    // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
    // historyButtonIcon?: string,//历史会话按钮图标路径
    // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
    // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

    //新建会话按钮设置项
    // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
    // newButtonIcon?: string,//新建会话按钮图标路径               
    // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

    // 关闭按钮设置项
    // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
    // closeButtonIcon?: string;//关闭按钮图标路径
    // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数 
    // wrapstyle: {
    //   paddingTop: '20px',
    // }
  }

  // 支持自定义欢迎页面
  // const renderWelcome = () => {
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: '100%' }}>这是欢迎页</div>
  //   )
  // }
  // const renderQuickReplies = () => {
  //   return (
  //     <div style={{ marginLeft: 20, marginBottom: 10 }}>11111</div>
  //   )
  // }

  // 埋点方法
  const onReportLog = useCallback((params: any) => {
    log(params);
  }, []);

  const RenderHistoryConversationFooter = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的底部自定义模块</div>
  }

  const RenderHistoryConversationBrand = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的品牌区域自定义模块</div>
  }

  return (
    <DemoPage>
      <div style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }} id="chat-container">
        <AiChat
          navbar={navbarProps} //标题栏参数配置
          // renderNavbar={() => null} //支持自定义的navbar
          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={initialMessages} //初始化消息
          // renderMessageContent={renderMessageContent} //自定义卡片渲染
          config={config} // 通用全局配置
          onReportLog={onReportLog} // 自定义埋点方法
          // quickReplies={[{
          //   title: 'string',//页面展示的快捷问题
          //   content: 'realString',//点击后实际发送的问题
          //   url: '',//如果配置，点击后跳转链接
          //   isHighlight: true,
          //   isNew: true,
          //   img: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg'
          // }]}

          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推              
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75
            title: '历史会话列表', //历史会话列表标题
            logo: LogoPng,
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域   
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为"请输入搜索关键字"
            renderBrand: RenderHistoryConversationBrand, //支持传入自定义brand区域
            renderFooter: RenderHistoryConversationFooter, //支持传入自定义底部区域  
            showSearch: false,
          }}

          // 欢迎页配置
          welcome={{
            // open: false, //是否展示欢迎页，默认true
            riskTip: "内容由AI大模型生成，请谨慎识别", //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别"
            title: 'Hi～我是 AI助手',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            // showsubTitle: false,
            // logo: '',
            // openMessages 表示欢迎页初始问题，可选
            // openMessages: [{type: 'text', content: '欢迎使用问TA' }, {type:'list', content: '如何做好投资？'}],
            openMessages: [{type: 'list', content: '如何投资ETF？' }, {type:'list', content: '如何做好价值投资？'}, {type:'list', content: '如何看待中美关系？'}],
            // 欢迎页标题配置，配置和上面的navbarProps相同，优先在欢迎页生效
            // navbar: {
              // 配置和上面的navbarProps相同，优先在欢迎页生效
              // title: '欢迎页标题',
              // wrapstyle: {
              //   paddingTop: '20px',
              // },
            // },
            // 支持自定义navbar
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}
          // 是否展示反馈弹窗
          // showFeedbackModal={true}
          // feedbackModalConfig={{
          //   title: 'string',
          //   inputPlaceholder: 'string',
          //   showLabels: true,
          // }}
          // 是否展示大模型消耗的tokens数量，默认true，如果是hiagent则需要
          showToken={false}
          // 是否在答案结束后展示合规话术'内容有Ai生产，balabala...',默认true
          showHallucination={true}
        />
      </div>
    </DemoPage>
  );
};

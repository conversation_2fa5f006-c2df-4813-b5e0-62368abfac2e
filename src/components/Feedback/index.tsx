/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useCallback, useState } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import copy from 'copy-to-clipboard';
import { MessageId, MessageProps } from '../Message';
import { Tooltip } from '../Tooltip';
import { toast } from '../Toast';
import { FeedbackTipApp } from '../FeedbackTipApp';
import { FeedbackTipPc } from '../FeedbackTipPc';
import { isMobile } from '../../utils/canUse';
import DefaultDown from './images/default-down.svg'
import DefaultUp from './images/default-up.svg'
import ThumbsDown from './images/thumbs-down.svg'
import ThumbsUp from './images/thumbs-up.svg'
import Copy from './images/copy.svg'
import Quote from './images/quote.svg';
import { LogPointConfigMap, ILogParams } from '../../LogPointConfigMap';
import { getThinkContent } from '../../utils/aiChat';

interface LabelProps {
  id: string,
  label: string
}
export interface FeedbackProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
 * 消息
 */
  message: MessageProps;
  /**
   * 点赞点踩回调
   */
  onFeedBack?: (score: MessageProps['feedbackResult'], message: MessageProps, reason?: any) => void;
  /**
  * log上报回调
  */
  onConsolidateReports?: (params: ILogParams) => void;

  /**
  * 复制文本回调
  */
  copyText?: (text: string, messageId?: MessageId) => void;
  /**
   * 引用文本回调
   */
  onQuote?: (message: MessageProps) => void;
  showFeedbackModal?: boolean;
  feedbackLabels?: LabelProps[];
  /**
    * 点踩反馈弹窗配置
    */
  feedbackModalConfig?: {
    title?: string,
    inputPlaceholder?: string,
    showLabels?: boolean,
  };
  /**
   * 需要的反馈按钮数组
   */
  feedbackArray?: Array<'copy' | 'good' | 'bad' | 'quote'>;
}

export const Feedback = (props: FeedbackProps) => {
  const {
    onConsolidateReports,
    onFeedBack,
    message,
    copyText,
    onQuote,
    showFeedbackModal = false,
    feedbackLabels,
    feedbackModalConfig,
    feedbackArray = ['copy', 'good', 'bad', 'quote']
  } = props;

  const upSrc = message?.feedbackResult === 'good' ? ThumbsUp : DefaultUp;
  const downSrc = message?.feedbackResult === 'bad' ? ThumbsDown : DefaultDown;

  // 点踩反馈弹窗
  const [showFeedbackTip, setShowFeedbackTip] = useState(false);

  const handleCopy = useCallback(() => {
    // 只复制正文（去掉开头的换行符）
    const copyMsg = getThinkContent(message?.content?.text)?.content?.replace(/^\n+/, '') || '';
    if (copyText) {
      copyText(copyMsg, message.messageId);
    } else {
      copy(copyMsg);
      console.log('复制成功!');
      toast.success('复制成功', 1000, 'center');
    }
    // 埋点上报
    const params = LogPointConfigMap.get('handleAnswerCopy');
    if (params) {
      onConsolidateReports?.({
        ...params,
        btn_title: {
          ...(typeof params.btn_title === 'object' ? params.btn_title : {}),
          value: message?.messageId,
        }
      });
    }
  }, [copyText, message?.messageId, message?.content?.text, onConsolidateReports]);

  // 点踩反馈弹窗展示
  const handleFeedBackOpen = () => {
    setShowFeedbackTip(true);
  }

  // 点踩反馈弹窗关闭
  const handleFeedbackClose = () => {
    setShowFeedbackTip(false);
  }

  // 点踩操作
  const handleDisLike = () => {
    // 不需要展示反馈弹窗  或  取消点踩  可直接下发接口
    if (!showFeedbackModal || message?.feedbackResult === 'bad') {
      onFeedBack && onFeedBack('bad', message);
      return;
    }
    // 需要展示反馈弹窗  且  点踩(包括点赞直接到点踩)  展示反馈弹窗
    if (showFeedbackModal && (!message?.feedbackResult || message?.feedbackResult === 'good')) {
      handleFeedBackOpen();
    }

  }

  // 反馈弹窗提交操作
  const handleFeedBackSubmit = (eTags?: any, eValue?: string) => {
    console.log('点踩反馈标签提交数据', eTags, eValue)
    const reason = { selectedIds: eTags?.map((t: { id: string; }) => t.id) || [], inputReason: eValue || '' }
    onFeedBack && onFeedBack('bad', message, reason);
  }

  const handleQuote = () => {
    if (onQuote) {
      onQuote(message);
    } else {
      console.log('引用内容:', message?.content?.text);
    }
  };

  if (!message?.needFeedback && !message?.showToken) {
    return null;
  }

  return (
    <div className="FeedbackWrap">
      {
        message?.showToken && (
          <div className="FeedbackToken">
            <div>{message?.totalTime ?? 0}s</div>
            <div>{message?.totalTokens ?? 0}Tokens</div>
          </div>
        )
      }
      {
        message?.needFeedback && (
          <div className="Feedback">
            {showFeedbackModal &&
              <>
                {showFeedbackTip && isMobile && <FeedbackTipApp onClose={handleFeedbackClose} labels={feedbackLabels} onSubmit={handleFeedBackSubmit} {...feedbackModalConfig} />}
                {showFeedbackTip && !isMobile && <FeedbackTipPc onClose={handleFeedbackClose} labels={feedbackLabels} onSubmit={handleFeedBackSubmit} {...feedbackModalConfig} />}
              </>
            }
            {
              feedbackArray.includes('copy') && (
              isMobile ? (
                <div onClick={handleCopy}>
                  <img src={Copy} alt="copy" className='Thumbs' />
                </div>
              )
                : (
                  <Tooltip content='复制' placement='bottom' >
                    <div onClick={handleCopy}>
                      <img src={Copy} alt="copy" className='Thumbs' />
                    </div>
                  </Tooltip>
                ))
            }
            {
              feedbackArray.includes('good') && (
              isMobile ? (
                <div onClick={() => onFeedBack && onFeedBack('good', message)}>
                  <img src={upSrc} alt="up" className='Thumbs' />
                </div>
              )
                : (
                  <Tooltip content='喜欢' placement='bottom' >
                    <div onClick={() => onFeedBack && onFeedBack('good', message)}>
                      <img src={upSrc} alt="up" className='Thumbs' />
                    </div>
                  </Tooltip>
                )
              )
            }
            {
              feedbackArray.includes('bad') && (
              isMobile ? (
                <div onClick={handleDisLike}>
                  <img src={downSrc} alt="down" className='Thumbs' />
                </div>
              )
                : (
                  <Tooltip content='不喜欢' placement='bottom' >
                    <div onClick={handleDisLike}>
                      <img src={downSrc} alt="down" className='Thumbs' />
                    </div>
                  </Tooltip>
                )
              )
            }
            {
              feedbackArray.includes('quote') && (
                <div onClick={handleQuote}>
                  <img src={Quote} alt="quote" className='Thumbs' />
                </div>
              )
            }
          </div>
        )
      }
    </div>
  );
};
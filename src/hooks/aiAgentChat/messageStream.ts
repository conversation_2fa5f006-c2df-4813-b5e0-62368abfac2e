/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-05-21 11:13:53
 */
class MessageStreamController {
  private messageQueue: any[] = [];

  private isProcessing = false;

  private interval: number;

  private processCallback: (data: any) => void;
  
  private requestAnimationId: number | null = null;
  
  constructor(
    callback: (data: any) => void, 
    messagesPerSecond = 10
  ) {
    this.processCallback = callback;
    this.interval = Math.floor(1000 / messagesPerSecond);
  }
  
  // 添加消息到队列
  enqueue(message: any) {
    this.messageQueue.push(message);
    if (!this.isProcessing) {
      this.startProcessing();
    }
  }
  
  // 批量添加消息
  enqueueBatch(messages: any[]) {
    this.messageQueue.push(...messages);
    if (!this.isProcessing) {
      this.startProcessing();
    }
  }
  
  // 开始处理队列
  private startProcessing() {
    this.isProcessing = true;
    this.processNextMessage();
  }
  
  // 处理下一条消息
  private processNextMessage = () => {
    if (this.messageQueue.length === 0) {
      this.isProcessing = false;
      return;
    }
    
    const message = this.messageQueue.shift();
    this.processCallback(message);
    
    // 使用 requestAnimationFrame 和 setTimeout 配合
    // 提供平滑的视觉效果同时控制速率
    this.requestAnimationId = requestAnimationFrame(() => {
      setTimeout(this.processNextMessage, this.interval);
    });
  }
  
  // 停止处理
  stop() {
    if (this.requestAnimationId) {
      cancelAnimationFrame(this.requestAnimationId);
      this.requestAnimationId = null;
    }
    this.isProcessing = false;
    this.messageQueue = [];
  }
  
  // 调整处理速率
  setRate(messagesPerSecond: number) {
    this.interval = Math.floor(1000 / messagesPerSecond);
  }
}

export default MessageStreamController;
/*
 * @Author: 016645
 * @Description: useCreateConversation Hook 的单元测试
 * @Date: 2025-03-24 07:10:00
 */
import { renderHook, act } from '@testing-library/react-hooks';
import useCreateConversation from '../useCreateConversation';

// 模拟 fetch 函数
global.fetch = jest.fn();

describe('useCreateConversation Hook', () => {
  beforeEach(() => {
    // 每个测试前重置模拟
    jest.resetAllMocks();
  });

  it('应该初始化状态', () => {
    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      manual: true, // 设置为手动模式，避免自动请求
    };

    // 渲染 Hook
    const { result } = renderHook(() => useCreateConversation(options));

    // 验证初始状态
    expect(result.current.data).toBeNull();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(typeof result.current.run).toBe('function');
  });

  it('非手动模式应该自动发起请求', async () => {
    // 模拟成功响应
    const mockResponse = {
      code: '0',
      msg: 'success',
      resultData: {
        conversationId: 'conv123'
      }
    };

    // 模拟 fetch 实现
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      manual: false, // 非手动模式
    };

    // 渲染 Hook
    const { result, waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证 fetch 被自动调用
    expect(global.fetch).toHaveBeenCalledTimes(1);
    expect(result.current.data).toEqual(mockResponse);
  });

  it('手动模式应该需要显式调用 run 函数', async () => {
    // 模拟成功响应
    const mockResponse = {
      code: '0',
      msg: 'success',
      resultData: {
        conversationId: 'conv123'
      }
    };

    // 模拟 fetch 实现
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      manual: true, // 手动模式
    };

    // 渲染 Hook
    const { result, waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 验证初始时没有发起请求
    expect(global.fetch).not.toHaveBeenCalled();

    // 手动调用 run 函数
    act(() => {
      result.current.run();
    });

    // 等待状态更新
    await waitForNextUpdate();

    // 验证 fetch 被调用
    expect(global.fetch).toHaveBeenCalledTimes(1);
    expect(result.current.data).toEqual(mockResponse);
  });

  it('应该成功发送请求并更新数据', async () => {
    // 模拟成功响应
    const mockResponse = {
      code: '0',
      msg: 'success',
      resultData: {
        conversationId: 'conv123'
      }
    };

    // 模拟 fetch 实现
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      inputs: { key: 'value' }
    };

    // 渲染 Hook
    const { result, waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证 fetch 被正确调用
    expect(global.fetch).toHaveBeenCalledWith(
      'https://api.example.com/create_conversation',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        }),
        body: expect.stringContaining('user123')
      })
    );

    // 验证数据已更新
    expect(result.current.data).toEqual(mockResponse);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('isDev参数应该影响请求URL', async () => {
    // 模拟成功响应
    const mockResponse = {
      code: '0',
      msg: 'success',
      resultData: {
        conversationId: 'conv123'
      }
    };

    // 模拟 fetch 实现
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      isDev: true // 开发环境模式
    };

    // 渲染 Hook
    const { waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证 fetch 被调用的URL是开发环境的
    expect(global.fetch).toHaveBeenCalledWith(
      '/hiAgent/create_conversation',
      expect.anything()
    );
  });

  it('应该处理请求错误', async () => {
    // 模拟错误响应
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error'
    });

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http'
    };

    // 渲染 Hook
    const { result, waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证错误状态
    expect(result.current.data).toBeNull();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeInstanceOf(Error);
    expect(result.current.error?.message).toBe('Network response was not ok');
  });

  it('应该使用转换器函数', async () => {
    // 模拟成功响应
    const mockResponse = {
      code: '0',
      msg: 'success',
      resultData: {
        conversationId: 'conv123'
      }
    };

    // 模拟 fetch 实现
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    // 模拟请求和响应转换器
    const requestTransfer = jest.fn(data => ({ ...data, extraParam: 'value' }));
    const responseTransfer = jest.fn(data => ({ 
      ...data, 
      transformed: true 
    }));

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      requestTransfer,
      responseTransfer
    };

    // 渲染 Hook
    const { result, waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证转换器被调用
    expect(requestTransfer).toHaveBeenCalled();
    expect(responseTransfer).toHaveBeenCalledWith(mockResponse);

    // 验证转换后的数据
    expect(result.current.data).toEqual(expect.objectContaining({
      transformed: true
    }));
  });

  it('应该调用成功回调函数', async () => {
    // 模拟成功响应
    const mockResponse = {
      code: '0',
      msg: 'success',
      resultData: {
        conversationId: 'conv123'
      }
    };

    // 模拟 fetch 实现
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    // 模拟回调函数
    const onSuccess = jest.fn();

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      onSuccess
    };

    // 渲染 Hook
    const { waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证回调被调用
    expect(onSuccess).toHaveBeenCalledWith(mockResponse);
  });

  it('应该调用错误回调函数', async () => {
    // 模拟错误
    const mockError = new Error('Network error');
    (global.fetch as jest.Mock).mockRejectedValueOnce(mockError);

    // 模拟回调函数
    const onError = jest.fn();

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http',
      onError
    };

    // 渲染 Hook
    const { waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证回调被调用
    expect(onError).toHaveBeenCalled();
  });

  it('应该处理网络异常', async () => {
    // 模拟网络错误
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    // 模拟选项
    const options = {
      platform: 'hiAgent',
      baseUrl: 'https://api.example.com',
      userId: 'user123',
      type: 'http'
    };

    // 渲染 Hook
    const { result, waitForNextUpdate } = renderHook(() => useCreateConversation(options));

    // 等待状态更新
    await waitForNextUpdate();

    // 验证错误状态
    expect(result.current.data).toBeNull();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeInstanceOf(Error);
    expect(result.current.error?.message).toBe('Network error');
  });
}); 
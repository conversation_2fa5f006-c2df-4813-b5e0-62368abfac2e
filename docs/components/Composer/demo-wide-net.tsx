import React, { useState } from 'react';
import { Composer } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';

export default () => {
  const [enableInternetSearch, setEnableInternetSearch] = useState(false);

  const toggleEnableInternetSearch = () => {
    setEnableInternetSearch(!enableInternetSearch);
  };

  const onSend = async (type: string, content: string) => {
    return new Promise<boolean>((resolve) => {
      console.log('type', type, 'content', content);
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
  };

  return (
    <Composer
      isWide
      text="ceshi"
      inputType="text"
      onSend={onSend}
      showInternetSearch={true}
      showLLMSwitch={true}
      llmConfig={{
        selectProps: {
          defaultValue: 'gpt-3.5-turbo',
        },
        llmOptions: [
          { label: 'gpt-3.5-turbo', value: 'gpt-3.5-turbo' },
          { label: '千问', value: '千问' },
        ],
      }}
      enableInternetSearch={enableInternetSearch}
      toggleEnableInternetSearch={toggleEnableInternetSearch}
    ></Composer>
  );
};

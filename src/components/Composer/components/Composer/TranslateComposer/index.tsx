import type { SelectProps } from '@ht/sprite-ui';
import { Input, Select } from '@ht/sprite-ui';
import React, { useState }  from 'react';
import type { ComposerProps } from '../../type';

import SendButton from '../components/SendButton';
import CloseSvg from '../svg/close.svg';
import DefaultIcon from './defaultIcon.svg';
import LanguageIcon from './language.svg';
import './style.less';

export interface TranslateComposerProps extends ComposerProps {
  [key: string]: any; // 其他扩展属性
  translateOptions: {        
    src_lang: {
      defaultValue?: string,
      options: SelectProps['options'],
    },
    tgt_lang: {
      defaultValue?: string,
      options: SelectProps['options'],
    },
  },
}
export default function TranslateComposer(props: TranslateComposerProps) {
  const { icon, title, onclose, text = '', setText, onSend, translateOptions, ...rest } = props;
  const [srcLang, setSrcLang] = useState(translateOptions?.src_lang?.defaultValue ?? null); // 来源文本语言
  const [tgtLang, setTgtLang] = useState(translateOptions?.tgt_lang?.defaultValue ?? null); // 目标语言

  const onSendBtnClick = async () => {
    onSend({
      extendParams: {
        srcLang, 
        tgtLang,
      },
    });
  };

  const onInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  // const handleTranslatorChange = (value: string) => {
  //   console.log('TranslateComposer.handleTranslatorChange', value);
  // };

  const handleSrcLanguageChange = (value: string) => {
    console.log('TranslateComposer.handleSrcLanguageChange', value);
    setSrcLang(value);
  };

  const handleTargetLanguageChange = (value: string) => {
    console.log('TranslateComposer.handleSrcLanguageChange', value);
    setTgtLang(value);
  };

  return (
    <div className="translateComposer">
      <div className="header">
        <div className="title">
          {icon || <img src={DefaultIcon} />}
          <span>{title}</span>
        </div>
        <div className="handle">
          {/* <Select
            onChange={handleTranslatorChange}
            options={translatorOptions}
            defaultValue={'baidu'}
          /> */}
          <img className="close" src={CloseSvg} onClick={onclose} />
        </div> 
      </div>
      <div className="content">
        <div className="language">
          <Select
            onChange={handleSrcLanguageChange}
            options={translateOptions?.src_lang?.options ?? []}
            defaultValue={translateOptions?.src_lang?.defaultValue ?? null}
            style={{ width: '100px' }}
          />
          <img src={LanguageIcon} />
          <Select
            style={{ width: '100px' }}
            onChange={handleTargetLanguageChange}
            options={translateOptions?.tgt_lang?.options ?? []}
            defaultValue={translateOptions?.tgt_lang?.defaultValue ?? null}
          />
        </div>
        <Input.TextArea {...rest} rows={3} value={text} onChange={onInputChange} />
      </div>
      <div className="footer">
        <SendButton hasContent={!!text.trim()} onClick={onSendBtnClick} />
      </div>
    </div>
  );
}

<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 142 146" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>APP-智能助手-首页@2x</title>
    <defs>
        <radialGradient cx="25.6007369%" cy="18.1604472%" fx="25.6007369%" fy="18.1604472%" r="87.8817098%" gradientTransform="translate(0.256007,0.181604),scale(0.871881,1.000000),rotate(51.891024),scale(1.000000,1.070147),translate(-0.256007,-0.181604)" id="radialGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#D6D6D6" offset="100%"></stop>
        </radialGradient>
        <path d="M72.2427825,3.42857143 C93.5476245,3.42857143 136.86747,34.6040816 136.86747,67.3340494 C136.86747,100.064017 113.964765,118.285714 72.2427825,118.285714 C30.5208002,118.285714 5.13253012,103.172932 5.13253012,67.3340494 C5.13253012,31.4951665 50.9379404,3.42857143 72.2427825,3.42857143 Z" id="path-2"></path>
        <filter x="-4.6%" y="-5.2%" width="109.1%" height="110.4%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-2" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <radialGradient cx="24.2867034%" cy="31.0973505%" fx="24.2867034%" fy="31.0973505%" r="112.077277%" gradientTransform="translate(0.242867,0.310974),scale(1.000000,0.676514),rotate(49.997221),scale(1.000000,0.781850),translate(-0.242867,-0.310974)" id="radialGradient-4">
            <stop stop-color="#2E3037" offset="0%"></stop>
            <stop stop-color="#000000" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="50%" cy="73.3234335%" fx="50%" fy="73.3234335%" r="43.3441391%" gradientTransform="translate(0.500000,0.733234),scale(1.000000,0.676514),rotate(90.000000),scale(1.000000,1.568052),translate(-0.500000,-0.733234)" id="radialGradient-5">
            <stop stop-color="#000896" offset="0%"></stop>
            <stop stop-color="#000000" stop-opacity="0.5" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="44.3097899%" y1="64.2214942%" x2="59.2559817%" y2="97.8368416%" id="linearGradient-6">
            <stop stop-color="#C8C8C8" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M58.4375764,45.9700715 C62.6250509,49.6682869 66.8125255,51.5173945 71,51.5173945 C75.1874745,51.5173945 79.3749491,49.6682869 83.5624236,45.9700715 C93.2188482,37.4418729 107.960397,38.3564966 116.488585,48.0129303 C120.372668,52.4108613 122.45672,58.1109973 122.325301,63.977054 L122.325301,63.977054 L122.325301,63.977054 C123.853714,86.2391836 107.447388,105.70035 85.2474348,107.958787 C80.0624461,108.486262 75.4799678,108.75 71.5,108.75 C67.2350593,108.75 62.2372775,108.447142 56.5066544,107.841426 C34.4289491,105.507883 18.1540835,86.125603 19.6746988,63.977054 L19.6746988,63.977054 L19.6746988,63.977054 C19.386145,51.0970911 29.5934989,40.4218991 42.4734618,40.1333453 C48.3395185,40.0019263 54.0396545,42.0859789 58.4375764,45.9700715 Z" id="path-7"></path>
        <filter x="-22.1%" y="-56.4%" width="144.2%" height="212.7%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="3.42857143" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-22.1%" y="-56.4%" width="144.2%" height="212.7%" filterUnits="objectBoundingBox" id="filter-9">
            <feGaussianBlur stdDeviation="3.42857143" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="-100.079007%" y1="-100.934582%" x2="100%" y2="100%" id="linearGradient-10">
            <stop stop-color="#C045FF" offset="0%"></stop>
            <stop stop-color="#1AFFFF" offset="58.6263604%"></stop>
            <stop stop-color="#171FFF" offset="100%"></stop>
        </linearGradient>
        <circle id="path-11" cx="16" cy="16" r="16"></circle>
        <filter x="-3.1%" y="-3.1%" width="112.5%" height="112.5%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.104026101 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-3.1%" y="-3.1%" width="112.5%" height="112.5%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-1" dy="-1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0588235294   0 0 0 0 0.525490196   0 0 0 0 0.811764706  0 0 0 0.522807515 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M23.5470605,22.3437446 L21.4463087,21.4640275 C21.3755211,21.4348143 21.3089119,21.4074122 21.246481,21.3818213 C20.7110029,21.1623245 20.0664705,21.0076051 19.5754393,20.9105864 C19.443893,20.8846502 19.3307715,20.804943 19.2648763,20.69168 L16.1532695,14.5068017 C16.1010836,14.3828952 15.9234353,14.3828952 15.870979,14.5068017 L12.7843671,20.6411432 C12.7256918,20.7810721 12.5991478,20.880945 12.4488092,20.9105864 C12.0355426,20.992253 11.5372852,21.1203592 10.9622528,21.3145985 L10.7677431,21.3818213 C10.5034607,21.4752047 9.73212531,21.7927396 8.45373687,22.334426 C8.09442821,22.4866734 7.67817982,22.3224305 7.52401969,21.9675783 C7.43715195,21.7676225 7.44949719,21.5391235 7.55741661,21.3494322 L15.1849569,7.94240859 C15.4403166,7.4935595 16.0157599,7.33413813 16.4702455,7.58633097 C16.6215562,7.67029289 16.7464978,7.7938299 16.8313365,7.94336249 L24.442872,21.3590904 C24.6339789,21.695949 24.512407,22.1220317 24.1713238,22.3107782 C23.9796854,22.4168259 23.7490569,22.429005 23.5470605,22.3437446 Z M16.034321,17.9566462 L17.0666667,20.6593047 C16.7171389,20.6352946 16.364296,20.6230878 16.0085488,20.6230878 C15.6705936,20.6230878 15.3352594,20.6341041 15.0028986,20.6557909 L16.034321,17.9566462 Z" id="path-14"></path>
        <filter x="-5.9%" y="-6.7%" width="123.4%" height="126.8%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0901960784   0 0 0 0 0.662745098   0 0 0 0 0.874509804  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="40.7301884%" y1="11.2223963%" x2="64.0997595%" y2="112.702821%" id="linearGradient-16">
            <stop stop-color="#1AFFFF" offset="0%"></stop>
            <stop stop-color="#171FFF" offset="65.0634177%"></stop>
            <stop stop-color="#C045FF" offset="100%"></stop>
        </linearGradient>
        <path d="M5.13253012,64.2857143 C11.2630522,73.4285714 14.8273092,79.6428571 15.8253012,82.9285714 C16.8232932,86.2142857 19.5321285,94.1428571 23.9518072,106.714286 C14.5421687,103 7.9126506,95.4285714 4.06325301,84 C0.213855422,72.5714286 0.570281124,66 5.13253012,64.2857143 Z" id="path-17"></path>
        <filter x="-4.4%" y="-4.7%" width="117.8%" height="109.4%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.103862202 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-13.3%" y="-9.4%" width="135.5%" height="118.9%" filterUnits="objectBoundingBox" id="filter-19">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-2" dy="-3" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.395570048   0 0 0 0 0   0 0 0 0 0.621715159  0 0 0 0.199812882 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M121.46988,64.2857143 C127.600402,73.4285714 131.164659,79.6428571 132.162651,82.9285714 C133.160643,86.2142857 135.869478,94.1428571 140.289157,106.714286 C130.879518,103 124.25,95.4285714 120.400602,84 C116.551205,72.5714286 116.907631,66 121.46988,64.2857143 Z" id="path-20"></path>
        <filter x="-4.4%" y="-4.7%" width="117.8%" height="109.4%" filterUnits="objectBoundingBox" id="filter-21">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.103862202 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-13.3%" y="-9.4%" width="135.5%" height="118.9%" filterUnits="objectBoundingBox" id="filter-22">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-2" dy="-3" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.395570048   0 0 0 0 0   0 0 0 0 0.621715159  0 0 0 0.199812882 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-22.5%" y="-112.5%" width="145.0%" height="325.0%" filterUnits="objectBoundingBox" id="filter-23">
            <feGaussianBlur stdDeviation="3" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="APP-智能助手-首页" transform="translate(-117.000000, -130.000000)">
            <g id="编组-18" transform="translate(117.000000, 130.000000)">
                <g id="矩形">
                    <use fill="url(#radialGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                </g>
                <g id="矩形">
                    <use fill="url(#radialGradient-4)" xlink:href="#path-7"></use>
                    <path stroke="url(#linearGradient-6)" stroke-width="0.705882353" d="M83.7960576,46.2346138 C88.5512179,42.0350314 94.5584244,40.1604362 100.426735,40.5245275 C106.295048,40.8886189 112.024464,43.4913981 116.224043,48.2465641 C119.94597,52.4608875 121.989637,57.8893246 121.97817,63.5010926 C122.732281,74.7749367 119.060521,85.2161081 112.456645,93.133024 C105.826095,101.081917 96.2399398,106.485739 85.211714,107.607658 C80.0401978,108.133763 75.4696336,108.397059 71.5,108.397059 C67.2456035,108.397059 62.2601965,108.094657 56.5437525,107.49044 C45.7174818,106.346138 36.2986187,101.066867 29.7258062,93.36594 C23.1424253,85.6526305 19.4142114,75.5098802 19.9866994,64.6620561 L20.0278832,63.9837533 C19.8817097,57.6355083 22.3245208,51.8301758 26.3909807,47.577336 C30.4543221,43.3277578 36.138823,40.6282919 42.4813669,40.486198 C48.2586692,40.3567673 53.8725614,42.4092878 58.2039427,46.2346141 C62.4692951,50.0016082 66.7377756,51.8703357 71,51.8703357 C75.2622244,51.8703357 79.5307049,50.0016082 83.7960576,46.2346138 Z" stroke-linejoin="square" fill="url(#radialGradient-5)"></path>
                </g>
                <path d="M92.3352244,63.7142869 C94.6696158,63.7142869 96.5620139,65.6104927 96.5620139,67.949581 L96.5620139,79.2530631 C96.5620139,81.5921515 94.6696158,83.4883573 92.3352244,83.4883573 C90.000833,83.4883573 88.1084349,81.5921515 88.1084349,79.2530631 L88.1084349,67.949581 C88.1084349,65.6104927 90.000833,63.7142869 92.3352244,63.7142869 Z M47.4590662,64.9266889 C49.125211,63.2883625 51.8013473,63.3136315 53.4363839,64.9831287 L59.0721032,70.7376287 C60.7063729,72.4063429 60.6820562,75.0863401 59.0177773,76.7250046 L53.382058,82.2739868 C51.7169982,83.9134203 49.0408458,83.8899302 47.4047043,82.2215201 C45.7685628,80.5531101 45.7920057,77.871573 47.4570656,76.2321395 L50.0799594,73.6487239 L47.4027397,70.9160333 C45.8172497,69.2971269 45.7929176,66.7266023 47.3121195,65.0784442 L47.4590662,64.9266889 Z" id="形状结合" stroke="#942FFF" stroke-width="1.71428571" fill="#FFFFFF" fill-rule="nonzero"></path>
                <path d="M92.3352244,63.7142869 C94.6696158,63.7142869 96.5620139,65.6104927 96.5620139,67.949581 L96.5620139,79.2530631 C96.5620139,81.5921515 94.6696158,83.4883573 92.3352244,83.4883573 C90.000833,83.4883573 88.1084349,81.5921515 88.1084349,79.2530631 L88.1084349,67.949581 C88.1084349,65.6104927 90.000833,63.7142869 92.3352244,63.7142869 Z M47.4590662,64.9266889 C49.125211,63.2883625 51.8013473,63.3136315 53.4363839,64.9831287 L59.0721032,70.7376287 C60.7063729,72.4063429 60.6820562,75.0863401 59.0177773,76.7250046 L53.382058,82.2739868 C51.7169982,83.9134203 49.0408458,83.8899302 47.4047043,82.2215201 C45.7685628,80.5531101 45.7920057,77.871573 47.4570656,76.2321395 L50.0799594,73.6487239 L47.4027397,70.9160333 C45.8172497,69.2971269 45.7929176,66.7266023 47.3121195,65.0784442 L47.4590662,64.9266889 Z" id="形状结合" stroke="#8032FF" stroke-width="1.71428571" fill="#18FBFF" fill-rule="nonzero" filter="url(#filter-8)"></path>
                <path d="M92.3352244,63.7142869 C94.6696158,63.7142869 96.5620139,65.6104927 96.5620139,67.949581 L96.5620139,79.2530631 C96.5620139,81.5921515 94.6696158,83.4883573 92.3352244,83.4883573 C90.000833,83.4883573 88.1084349,81.5921515 88.1084349,79.2530631 L88.1084349,67.949581 C88.1084349,65.6104927 90.000833,63.7142869 92.3352244,63.7142869 Z M47.4590662,64.9266889 C49.125211,63.2883625 51.8013473,63.3136315 53.4363839,64.9831287 L59.0721032,70.7376287 C60.7063729,72.4063429 60.6820562,75.0863401 59.0177773,76.7250046 L53.382058,82.2739868 C51.7169982,83.9134203 49.0408458,83.8899302 47.4047043,82.2215201 C45.7685628,80.5531101 45.7920057,77.871573 47.4570656,76.2321395 L50.0799594,73.6487239 L47.4027397,70.9160333 C45.8172497,69.2971269 45.7929176,66.7266023 47.3121195,65.0784442 L47.4590662,64.9266889 Z" id="形状结合" stroke="#8032FF" stroke-width="1.71428571" fill="#18FBFF" fill-rule="nonzero" opacity="0.698140462" filter="url(#filter-9)"></path>
                <g id="编组-19" transform="translate(55.000000, 11.000000)">
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                        <use fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-11"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-11"></use>
                    </g>
                    <g id="形状">
                        <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-14"></use>
                    </g>
                </g>
                <g id="路径-7">
                    <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                    <use fill="url(#linearGradient-16)" fill-rule="evenodd" xlink:href="#path-17"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-19)" xlink:href="#path-17"></use>
                </g>
                <g id="路径-7" transform="translate(129.023688, 85.500000) scale(-1, 1) translate(-129.023688, -85.500000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-21)" xlink:href="#path-20"></use>
                    <use fill="url(#linearGradient-16)" fill-rule="evenodd" xlink:href="#path-20"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-22)" xlink:href="#path-20"></use>
                </g>
                <ellipse id="椭圆形" fill-opacity="0.0952635011" fill="#00032D" filter="url(#filter-23)" cx="71" cy="134" rx="20" ry="4"></ellipse>
            </g>
        </g>
    </g>
</svg>
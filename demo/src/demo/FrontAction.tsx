/* eslint-disable import/no-extraneous-dependencies */
import React, { useCallback, useRef } from 'react';
// import  {API_PREFIX} from '@lowcode/lc-render';
import LCRender from '@lowcode/lc-render';
import { DemoPage } from '../components';
import AiChat from '../../../src';
import { AiChatHandle } from '../../../src/components/AiChat/interface';
import LogoPng from './images/deepseekChat.png';

//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;


export default () => {
  const chatUiRef = useRef<AiChatHandle>(null);
  const actions = [{
    name: "updateModalData",
    description: "翻译网页结果回显",
    handler: (response: any) => {
      console.log('执行至response',response);
      if(response.endFlag){
        console.log('执行一次', response);
      }    
    }
  }];
  // 公共全局配置
  const config = {
    //系统id：表示当前系统标识
    appId: 'web-assistant',

    //用户id：代表当前系统唯一用户id，动态获取
    userId: '002332',

    //场景id
    // sceneId: 'scene',
    
    //可选，用于魔方环境判断，测试环境调试
    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      // 系统来源
      host: 'AROTA',
    },

    //接口请求 method默认post
    requests: {

      /**
      * 基础URL，接口请求前缀，拼接在下述方法的url前缀，如业务线的公共前缀，支持传入函数或字符串
      */
      baseUrl: 'aichrome',
      // baseUrl(){
      //   return Math.random().toString()
      // },

      //初始引导接口
      init: {  
        // type: 'http',//请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
        // action:'27006',//移动端如果走tcp，需要传接口请求action号
        // paramsKey：'MS__REQUEST__PAYLOAD'，//移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
        url: '/ai/orchestration/session/createSession',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
        
        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选
        // requestTransfer: (input: object) => {
        //   return new Promise((resolve, reject) => {
        //     try {
        //       const parsedInput = {
        //         ...input,
        //         customerInput1: '123',
        //       }
        //       resolve(parsedInput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        // responseTransfer: (output: object) => {
        //   return new Promise((resolve, reject) => {
        //     const parsedOutput = {
        //       ...output,
        //       customeroutput1: '123',
        //     }
        //     try {
        //       resolve(parsedOutput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        // requestTransfer: (input: object) => { //同步写法
        requestTransfer:async (input: object) => { //支持异步写法
          const parsedInput = {
            ...input,
            customerInput1: '123',
          }
          return parsedInput;
        },
        responseTransfer: (output: object) => {
          const parsedOutput = {
            ...output,
            customeroutput1: '123',
          }
          return parsedOutput;
        },
      },

      //问答接口
      send: {
        url: '/chat/workflow/chrome',
        isAibag: true,
        // url: '/aorta/operation/api/ai/desktop/HiAgentAIChat',
        stream: true,
        messageInterval:50,
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        requestTransfer:async (input: object) => { //支持异步写法
          const parsedInput = {
            ...input,
            agentId: "translate",
          }
          return parsedInput;
        },
      },

      //查询历史消息详情接口
      history: {
        url: '/ai/orchestration/session/getHistoryMessages',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        // pageSize: 6,//支持分页，默认100，希望一次性尽量展示所有，6表示3个问答对，一个问题1条，一个答案1条: 6,
      },

      //快捷问题接口
      // quickReply: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryQuickReplies',
      //   headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      // },

      //点赞点踩接口
      score: {
        url: '/ai/orchestration/session/feedback',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //停止生成接口
      stop: {
        url: '/ai/orchestration/session/interruptSession',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

      //关联问题接口
      // related: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryRelatedQuestions',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

      // 历史会话列表
      historyConversation: {
        url: '/ai/orchestration/session/getHistorySessions',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      deleteHistoryConversation: {
        url: '/ai/orchestration/session/deleteSession',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // 阅读风险提示
      // riskRead: {
      //   url: '/mcrm/api/groovynoauth/chatBar/updateUserBehaviorByType',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 查询是否阅读了风险提示
      // queryRiskRead: {
      //   url: '/mcrm/api/groovynoauth/chatBar/queryUserBehaviorByType',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 敏感词校验
      // sensitive: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 点踩反馈标签列表
      // feedbackTagList: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryAllAiTag',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 删除消息
      deleteMessage: {
        url: '/ai/orchestration/session/deleteMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },
    },
    // 支持使用时传入指定方法并替换内部行为
    bridge: {
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },
    // 水印，可选，如果传了会话页面背景展示使用信息
    waterMark: {
      show: true,
      text: `张三       002332`,
    },
    //全局logo替换，涉及欢迎页，导航，会话页面
    robot: {
      logo: LogoPng,
    }
  }

  // 导航栏参数
  const navbarProps = {
    // 标题区域设置
    showLogo: false,//是否展示logo,默认为true            
    // logo: LogoPng,// logo图标的地址 
    title: 'AI智能助手', //头部标题文案，展示于logo右侧,默认为空字符串
  }

  return (
    <DemoPage>
      <div style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }} id="chat-container">
        <AiChat
          ref={chatUiRef}
          actions={actions}
          navbar={navbarProps}
          config={config}
          messageContainerConfig={{
            showQuote: true,
            showDeleteMessage: true,
          }}
          historyConversation={{
            title: '历史会话列表',
            logo: LogoPng,
            showSearch: false,
          }}
          welcome={{
            riskTip: "内容由AI大模型生成，请谨慎识别",
            title: 'Hi～我是 AI助手',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            openMessages: [{type: 'list', content: '如何投资ETF？' }, {type:'list', content: '如何做好价值投资？'}, {type:'list', content: '如何看待中美关系？'}],
          }}
          composerConfig={{
            quoteOperations: {
              citetext: [{
                disabled: false,
                icon: '',
                label: '翻译',
                question: '翻译翻译',
                agentId: 'agent1',
                customRender: '',
              }],
              citeweb: [{
                disabled: false,
                icon: '',
                label: '翻译',
                question: '翻译翻译',
                agentId: 'agent1',
                customRender: '',
              }],
              image: [{
                disabled: false,
                icon: '',
                label: '翻译',
                question: '翻译翻译',
                agentId: 'agent1',
                customRender: '',
              }],
              file: [{
                disabled: false,
                icon: '',
                label: '翻译',
                question: '翻译翻译',
                agentId: 'agent1',
                customRender: '',
              }],
            },
            skill: [{
              key: 'translatePage',
              disabled: false,
              icon: '',
              label: '翻译此网页',
              question: '翻译此网页who are you',
              agentId: 'translate',
              onClick: async () => {
                if(chatUiRef?.current?.chatContext?.onSend){
                  console.log('chatUiRef', chatUiRef, chatUiRef.current.chatContext.onSend);
                  const response = await chatUiRef?.current.chatContext.onSend('text', `翻译此页面：who are you`, {
                    agentId: 'translate',
                    extendParams: {
                      msgChannel: "contentScript",
                    },
                    hideConversation: true, 
                    conversationId: '',               
                  })
                  console.log('response', response);
                }
              },
            }],
          }}
          showToken={false}
          showHallucination={true}
          renderStopAnswer={(msg) => {
            console.log('msg', msg);
            return <div>自定义停止生成</div>
          }}
        />
      </div>
    </DemoPage>
  );
};

export const lowcodePath = window.isLowCodeDev ? 'http://168.61.127.80/app/' : 'http://lowcode.fe.htsc/app/';

export const createPath = ({
  pathname,
  query,
}: {
  pathname: string;
  query?: Record<string, any>;
}) => {
  const params = [];
  for (const [key, value] of Object.entries(query ?? {})) {
    if (value != null) {
      const isObject =
        Object.prototype.toString.call(value) === '[object Object]';
      params.push(`${encodeURIComponent(key)}=${encodeURIComponent(isObject ? JSON.stringify(value) : value)}`);
    }
  }
  const search = params.join('&');
  return `${pathname}${search.length > 0 ? `?${search}` : search}`;
};

export const getLowcodeUrl = (appId: string, query?: Record<string, any>) => {
  return createPath({ pathname: `${lowcodePath}${appId}/editor`, query })
}

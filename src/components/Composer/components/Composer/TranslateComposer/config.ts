  import type { SelectProps } from '@ht/sprite-ui';
  
  export const srcLanguageOptions: SelectProps['options'] = [
    {
      label: '自动检测',
      value: 'auto',
      disabled: false,
    },
    {
      label: '中文',
      value: 'zh_CN',
      disabled: false,
    },
    {
      label: '英文',
      value: 'en_US',
      disabled: false,
    },
    {
      label: '日文',
      value: 'ja_JP',
      disabled: false,
    },
    {
      label: '法语',
      value: 'fr_FR',
      disabled: false,
    },
  ];

  export const translatorOptions: SelectProps['options'] = [
    {
      label: '百度翻译',
      value: 'baidu',
      disabled: false,
    },
    {
      label: '谷歌翻译',
      value: 'google',
      disabled: false,
    },
  ];
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Message, HistoryConversation, Composer, ConversationItemProps } from '@ht/chatui';
import HistoryIcon from './images/historyIcon.svg';
import newChatIcon from './images/newChatIcon.svg';

import '@ht/chatui/es/styles/index.less';
import './index.less';

const  getMessages = (length: number = 1, text?: string) => {
  return Array.from({ length }).map((_, index) => ({
    _id: index,
    messageId: index,
    type: 'text',
    content: { text: text || `我们有多种产品可以满足您的需求${index}` },
    position: 'left',
    user: {
      name: '华泰客服',
      avatar: 'https://gw.alicdn.com/tfs/TB1U7FBiAT2gK0jSZPcXXcKkpXa-108-108.jpg'
    },
    createdAt: Date.now(),
    config: {},
    updateMsg: () => { },
  }));
};

export default () => {

  const [messages, setMessages] = useState(getMessages());
  const [visible, setVisible] = useState(false);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  const historyList: ConversationItemProps[] = Array.from({ length: 10 }).map((_, index) => ({
    conversationId: index,
    createTime: index < 3 ? Date.now() : index < 7 ? new Date().setDate(new Date().getDate() - 6) : new Date().setDate(new Date().getDate() - 30),  // 当前时间戳，毫秒级
    question: `这是第${index + 1}条历史消息`,
    disabled: index === 3,
  }));


  const handleSend = useCallback(async (type: string, val: string) => {
    if (!val) {
      console.log('请输入您的问题');
      return false;
    }

    const newMessage: any = {
      type,
      position: 'right',
      content: { text: val },
      messageId: `${messages?.length}`,
      _id: `${messages?.length}`,
    };

    setMessages([...messages, newMessage]);
    return true;
  }, [messages]);

  useEffect(() => {
    if (messageContainerRef?.current) {
      const scrollHeight = messageContainerRef?.current?.scrollHeight || 0;
      messageContainerRef?.current?.scrollTo({ top: scrollHeight, behavior: 'smooth' });
    }
  }, [messages]);

  const handleNewButtonClick = () => {
    setMessages(getMessages(1, '新会话'));
  };

  const handleHistoryButtonClick = () => {
    setVisible(true);
  };

  const handleMaskClick = () => {
    setVisible(false);
  };

  const handleConversationClick = (conversation: ConversationItemProps) => {
    console.log(conversation);
    setMessages(getMessages(10));
    setVisible(false);
  };

  return (
    <div className="aiChatContainer">
      <div className="aiChatPanel">
        <div
          className="aiChatConversation"
          style={{ transform: visible ? 'translateX(10%)' : 'translateX(0%)' }}
        >
        <div className="historyPannel">
          {
              visible && (
                <HistoryConversation
                  style={{ width: '260px', height: '100%' }}
                  list={historyList}
                  title="历史会话列表"
                  onConversationClick={handleConversationClick}
                  // 历史会话navBar相关配置
                  navbar={{
                    showNewButton: false,
                    showLogo: false,
                  }}
                />
            )
          }
        </div>
        <div className="NewConversation">
          {visible && (<div className="historyMask" onClick={handleMaskClick}/>)}
          <div className="navBar">
            <img src={HistoryIcon} onClick={handleHistoryButtonClick} className="iconSize" />
            <div className="navBar_title">新会话</div>
            <img src={newChatIcon} onClick={handleNewButtonClick} className="iconSize" />
          </div>
          <div className="messageContainer" ref={messageContainerRef}>
            {messages?.map((messgaeInfo => (
              <Message {...messgaeInfo} key={messgaeInfo?.messageId} />
            )))}
          </div>
          <div className="messageFooter">
            <Composer
              inputType="text"
              onSend={handleSend}
            />
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

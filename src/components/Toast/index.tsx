import React from 'react';
import { mountComponent } from '../../utils/mountComponent';
import { Toast } from './Toast';
import { ToastProps } from './interface';

function show(content: string, type?: ToastProps['type'], duration?: number, position?: ToastProps['position'], root?: HTMLElement) {
  mountComponent(<Toast content={content} type={type} position={position} duration={duration} />, root);
}

export const toast = {
  show,
  success(content: string, duration?: number, position?: ToastProps['position'], root?: HTMLElement) {
    show(content, 'success', duration, position, root);
  },
  fail(content: string, duration?: number, position?: ToastProps['position'], root?: HTMLElement) {
    show(content, 'error', duration, position, root);
  },
  loading(content: string, duration?: number, position?: ToastProps['position'], root?: HTMLElement) {
    show(content, 'loading', duration, position, root);
  },
};

export { Toast };

import commonjs from '@rollup/plugin-commonjs';
import resolve from '@rollup/plugin-node-resolve';
import babel from '@rollup/plugin-babel';
import { terser } from 'rollup-plugin-terser';
import postcss from 'rollup-plugin-postcss';
import json from '@rollup/plugin-json'; // 导入 JSON 插件
import image from '@rollup/plugin-image';
import pkg from './package.json';

const name = 'ChatUI';
const extensions = ['.js', '.jsx', '.ts', '.tsx'];

export default {
  input: './src/index.ts',
  external: ['react', 'react-dom'],
  plugins: [
    resolve({ extensions,
      browser: true, // 强制使用浏览器环境解析，解决fs报错
    }),
    postcss({
      extract: true, // 提取 CSS 文件到独立文件
      minimize: true, // 压缩 CSS
      extensions: ['.css', '.scss'], // 处理的文件类型
    }),
    commonjs(),
    babel({
      extensions,
      babelHelpers: 'runtime',
      include: ['src/**/*'],
    }),
    terser({
      output: { comments: false },
      compress: { drop_console: false },
    }),
    json(), // 添加 JSON 插件
    image(),
  ],
  target: 'web',
  output: {
    file: pkg.browser,
    format: 'umd',
    name,
    globals: {
      react: 'React',
      'react-dom': 'ReactDOM'
    },
    intro: `exports.version = '${pkg.version}';`,
  },
  treeshake: true, // 启用 tree shaking
};

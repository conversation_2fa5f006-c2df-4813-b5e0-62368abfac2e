.skills {
  width: 100%;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;

  :global {
    .sprite-btn:hover {
      background: #f6f6f7;
      border-color: #f2f2f3;
    }
  }

  .skillButton {
    min-width: 72px;
    height: 100%;
    // padding: 5px 12px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    background: #ffffff;
    border: 1px solid #f2f2f3;
    border-radius: 8px;
    opacity: 1;
  }

  .skillDiv {
    min-width: 72px;
    height: 100%;
    padding: 5px 12px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    background: #ffffff;
  }

  span {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #1d222c;
  }
}

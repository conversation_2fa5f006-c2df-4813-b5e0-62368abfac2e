import React from 'react';
import { Button, Dropdown, Menu } from '@ht/sprite-ui';
import type { Skill } from '../type';
import './style.less';

export interface SkillsProps {
  skills: Skill[];
}

export default function Skills(props: SkillsProps) {
  const { skills } = props;

  const onSkillClick = (skill: Skill) => {
    if (skill.onClick && !skill.disabled) {
      skill.onClick(skill);
    }
  };

  const skillRender = (skill: Skill, isRoot = true) => {
    if (skill.customRender) {
      return skill.customRender;
    }
    return isRoot ? (
      <Button onClick={() => onSkillClick(skill)} className="skillButton">
        {skill.icon}
        {skill.label}
        {skill.children && skill.children.length > 0 && skill.expandIcon}
      </Button>
    ) : (
      <span className="skillDiv">
        {skill.label}
        {skill.children && skill.children.length > 0 && skill.expandIcon}
      </span>
    );
  };

  const skillMenu = (skill: Skill) => {
    const getItem = (item: Skill): any => {
      return {
        key: item.key,
        label: skillRender(item, false),
        icon: item.icon,
        disabled: item.disabled,
        children:
          item.children && item.children.length > 0
            ? item.children?.map((subItem) => getItem(subItem))
            : undefined,
        onClick: () => onSkillClick(item),
      };
    };

    return (
      <Menu
        style={{
          backgroundColor: '#ffffff',
          border: 'border: 0.5px solid #DDDEE0',
          borderRadius: '12px',
          padding: '6px',
        }}
        items={
          (skill.children &&
            skill.children.length > 0 &&
            skill.children.map((item) => getItem(item))) ||
          []
        }
      />
    );
  };

  return (
    <div className="skills">
      {skills?.map((skill) => {
        return skill.children && skill.children.length > 0 ? (
          <Dropdown
            key={skill.key}
            disabled={skill.disabled}
            trigger={['click']}
            placement={'topLeft'}
            overlay={skillMenu(skill)}
          >
            {skillRender(skill)}
          </Dropdown>
        ) : (
          skillRender(skill)
        );
      })}
    </div>
  );
}

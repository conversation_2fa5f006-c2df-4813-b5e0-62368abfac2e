type SpecialParameters = StringParameter | ObjectParameter | ObjectArrayParameter;
interface BaseParameter extends AbstractParameter {
    type?: Exclude<AbstractParameter["type"], SpecialParameters["type"]>;
}
export type Parameter = BaseParameter | SpecialParameters;


export type FrontendActionAvailability = "disabled" | "enabled" | "remote" | "frontend";

export type FrontendAction<
  T extends Parameter[] | [] = [],
  N extends string = string,
> = Action<T> & {
  name: Exclude<N, "*">;
  /**
   * @deprecated Use `available` instead.
   */
  disabled?: boolean;
  available?: FrontendActionAvailability;
  pairedAction?: string;
  followUp?: boolean;
} & (
    | {
        render?:
          | string
          | (T extends []
              ? (props: ActionRenderPropsNoArgs<T>) => string | React.ReactElement
              : (props: ActionRenderProps<T>) => string | React.ReactElement);
        /** @deprecated use renderAndWaitForResponse instead */
        renderAndWait?: never;
        renderAndWaitForResponse?: never;
      }
    | {
        render?: never;
        /** @deprecated use renderAndWaitForResponse instead */
        renderAndWait?: T extends []
          ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement
          : (props: ActionRenderPropsWait<T>) => React.ReactElement;
        renderAndWaitForResponse?: T extends []
          ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement
          : (props: ActionRenderPropsWait<T>) => React.ReactElement;
        handler?: never;
      }
  );

export type CatchAllFrontendAction = {
  name: "*";
  render: (props: CatchAllActionRenderProps<any>) => React.ReactElement;
};

export type ActionRenderProps<T extends Parameter[] | [] = []> =
  | CompleteState<T>
  | ExecutingState<T>
  | InProgressState<T>;

export type ActionRenderPropsNoArgs<T extends Parameter[] | [] = []> =
  | CompleteStateNoArgs<T>
  | ExecutingStateNoArgs<T>
  | InProgressStateNoArgs<T>;

export type ActionRenderPropsWait<T extends Parameter[] | [] = []> =
  | CompleteStateWait<T>
  | ExecutingStateWait<T>
  | InProgressStateWait<T>;

export type ActionRenderPropsNoArgsWait<T extends Parameter[] | [] = []> =
  | CompleteStateNoArgsWait<T>
  | ExecutingStateNoArgsWait<T>
  | InProgressStateNoArgsWait<T>;

export type CatchAllActionRenderProps<T extends Parameter[] | [] = []> =
  | (CompleteState<T> & {
      name: string;
    })
  | (ExecutingState<T> & {
      name: string;
    })
  | (InProgressState<T> & {
      name: string;
    });


export interface FunctionCallHandlerArguments {
    messages: any[];
    name: string;
    args: any;
}
export type FunctionCallHandler = (args: FunctionCallHandlerArguments) => Promise<any>;
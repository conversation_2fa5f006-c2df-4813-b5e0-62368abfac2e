.Tag {
  display: inline-block;
  position: relative;
  margin: @tag-margin;
  padding: @tag-padding;
  border: 1px solid @brand-1;
  border-radius: @tag-border-radius;
  color: @tag-color;
  font-size: @tag-font-size;
  line-height: 1.25;
  white-space: nowrap;

  &--primary {
    border-color: transparent;
    color: @orange;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: -1px;
      border-radius: inherit;
      background: currentColor;
      opacity: 0.14;
    }
  }
  &--success {
    border-color: @green;
    background: @green;
    color: #fff;
  }
  &--danger {
    border-color: @red;
    background: @red;
    color: #fff;
  }
  &--warning {
    border-color: @orange;
    background: @orange;
    color: #fff;
  }
}

/* eslint-disable import/no-extraneous-dependencies */
import React, { useCallback } from 'react';
// import { log } from '@ht/xlog';
// import  {API_PREFIX} from '@lowcode/lc-render';
// import LCRender from '@lowcode/lc-render';
// import LogoPng from './images/deepseekChat.png';

import AiChat from '../../../src';

//需要注入魔方渲染组件全部变量提供给组件内部引用
// window.LCRender = LCRender;

// 魔方卡片数据示例
// let data = {

//   "cardBizType": "447e0542ca8f79b1cbbc",
//   "cardCategory": "lowcode-card",
//   "context": {
//     "tradingCode": "000001"
//   },
//   "conversationId": "ec80fa0c2c6e44869fce1373d44097ff",
//   "createTime": 1731651751569,
//   "lowCodeDetailMeta": {
//     "appId": "bd908367baee85cf3e7f"
//   },
//   "messageId": "efaec0798f2a4006a1373bc22bb93f05",
//   "snapshotId": "b2e82c3271ca4b08a1da5fde6af00443",
//   "view": {
//     "annReturnTot": 7.85722786,
//     "companyQuality": {
//       "managerName": "基金管理人", "rankPercent": 69.0
//     },
//     "exchangeCode": "OF",
//     "existing": true,
//     "htTypeCodeii": "002002",
//     "htTypeNameii": "灵活配置基金",
//     "opinionDate": 1726761600000,
//     "secuabbr": "华夏成长混合A",
//     "secucode": 53110,
//     "simpleEvaluateResults": [{
//       "conclusion": "历史收益能力一般", "rankPercent": 34.0
//     },
//     {
//       "conclusion": "风险控制能力一般", "rankPercent": 41.0
//     },
//     {
//       "conclusion": "择券配置能力较差", "rankPercent": 14.0
//     },
//     {
//       "conclusion": "投资独立性优秀", "rankPercent": 100.0
//     }
//     ],
//     "totalScoreScript": {
//       "highlightKeyWords": ["33.42", "较差"], "lowlightKeyWords": [], "script": "本基金近1年综合得分为33.42，超过12%的同类基金，总体表现较差"
//     },
//     "tradingCode": "000001"
//   }
// };


// const initialMessages = [
//   {
//     type: 'text',
//     content: { text: 'Hi，我是你的专属智能助理，有问题请随时找我哦~' },
//     createdAt: Date.now(),
//     hasTime: true,
//   },
//   {
//     type: 'markdown',
//     content : {
//       text: `### 基础语法

// 1.**标题**

// # 标题 1
// ## 标题 2
// ### 标题 3
// #### 标题 4
// ##### 标题 5
// ###### 标题 6

// 2.**段落和换行**

// 段落之间留空行
// 可以在行尾加两个空格来强制换行。

// 3.**列表**

// - 无序列表

// - 项目 1
// - 项目 2
//   - 子项目 2.1
//   - 子项目 2.2
// - 项目 3

// - 有序列表

// 1. 项目 1
// 2. 项目 2
//    1. 子项目 2.1
//    2. 子项目 2.2
// 3. 项目 3
//     },
//   },
//   //魔方卡片
//   {
//     type: 'card',
//     content: {
//       url: 'http://lowcode.fe.htsc/app/447e0542ca8f79b1cbbc/editor',
//       data: data
//     },
//   },
//   {
//     type: 'image',
//     content: {
//       picUrl: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg',
//     },
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是一个word文件这是一个word文件.docx",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/外籍股权激励账户开立.docx",
//       "size": 102400,
//     }
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是第二个word文件.docx",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E5%A4%96%E7%B1%8D%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E8%B4%A6%E6%88%B7%E5%BC%80%E7%AB%8B.docx",
//       "size": 102400,
//     }
//   },

//   {
//     type: 'file',
//     content: {
//       "name": "这是一个PDF文件信诚新双盈分级债券型证券投资基金更新招募说明书摘要.pdf",
//       "url": "https://aorta.htzq.com.cn/pdf_finchina/FUND/2020/2020-5/2020-05-28/1922768.pdf",
//       "size": 102400,
//     }
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是一个PDF文件这是Tailor服务记录.pdf",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E4%BB%80%E4%B9%88%E6%98%AF%E6%9C%8D%E5%8A%A1%E8%AE%B0%E5%BD%95.pdf",
//       "size": 102400,
//     }
//   },

// ];


export default () => {

  //自定义卡片方法，renderMessageContent传入aichat
  //定义卡片的type及卡片UI,与后端约定卡片消息格式
  // function renderMessageContent(msg: any) {
  //     const { type, content } = msg;
  
  //     // 根据消息类型来渲染
  //     switch (type) {
  //       case 'txt': //自定义卡片类型
  //         return <p>{content?.text}</p>; //自定义卡片UI
  //       default:
  //         return null;
  //     }
  //   }
  
  // 公共全局配置
  const config = {
    //系统id：表示当前系统标识,各个系统自定义
    appId: 'system',

    //用户id：代表当前系统唯一用户id，动态获取
    userId: '002332',

    // //场景id
    // sceneId: 'scene',

    //可选，用于魔方环境判断，测试环境调试
    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },

    //本示例以标准接口协议为例子
    //接口请求参数， method默认post，
    requests: {

       /**
       * 基础URL，接口请求前缀，拼接在下述方法的url前缀，如聊他业务线的公共前缀'fspa' 
       * baseUrl支持传递字符串与函数
        //如果配置了前缀，对应配置转发代理说明，对应地址是后端服务的地址，假设配置了hiAgent
        // proxy: {
        // '/hiAgent': {
        //   target: 'http://*************:3000/llmpf/api/proxy/api/v1/',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hiAgent/, '')
        // },
      */
      baseUrl: '',

      //初始引导接口参数配置,其他接口可选参数参考init
      init: {
        // type: 'http',//可选，下同，请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
        // action:'27006',//可选，，下同，移动端如果走tcp，需要传接口请求action号
        // paramsKey：'MS__REQUEST__PAYLOAD'，//可选，，下同，移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选，下同
        //可选，下同，请求入参转换回调函数，支持业务线入参转换
          
        // requestTransfer: (input: object) => { //同步写法
        requestTransfer:async (input: object) => { //支持异步写法
          const parsedInput = {
            ...input,
            customerInput1: '123',
          }
          return parsedInput;
        },
        //可选，请求出参转换回调函数，支持业务线出参转换
        responseTransfer: (output: object) => {
          const parsedOutput = {
            ...output,
            customeroutput1: '123',
          }
          return parsedOutput;
        },
      },

      //问答接口
      send: {
        url: '/aorta/operation/api/ai/desktop/HiAgentAIChat',
        // url: '/webassist/chat/workflow/chrome',
        stream: true,//表示流式输出
        // isAibag: true,
        messageInterval:50,//可选，控制数据帧在页面显示的间隔，单位毫秒，默认30ms
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //查询历史消息详情接口
      history: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        // pageSize: 6,//支持分页，默认100，希望一次性尽量展示所有，6表示3个问答对，一个问题1条，一个答案1条
      },

      //点赞点踩接口
      score: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        method: 'GET',
      },

      //停止生成接口，可选,如果配置了，会话过程页面会出现停止按钮
      stop: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

      // 历史会话列表
      historyConversation: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // // 敏感词校验,可选
      // sensitive: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // // 点踩反馈标签列表，不传则不展示点踩原因输入弹框，可选
      // feedbackTagList: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryAllAiTag',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

    },
    // 支持使用时传入指定方法并替换内部行为
    bridge: {
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },

    // 水印，可选，如果传了会话页面背景展示使用信息
    // waterMark: {
    //   show: true,
    //   text: `张三       002332`,
    // },
 
    //全局logo替换，涉及欢迎页，导航，会话页面
    // robot: {
    //   logo: LogoPng
    // }
  }

  // 导航栏参数,导航栏默认在pc和app有一套自己的样式，可以通过下述参数进行自定义
  const navbarProps = {
    // open?: boolean,//是否展示navbar，默认true

    // 返回按钮设置项
    // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
    // returnButtonIcon?: string;//返回按钮图标路径
    // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

    // 标题区域设置
    showLogo: false,//是否展示logo,默认为true            
    // logo: LogoPng,// logo图标的地址 
    title: 'AI智能助手', //头部标题文案，展示于logo右侧,默认为空字符串
    // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

    // 历史会话按钮设置项
    // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
    // historyButtonIcon?: string,//历史会话按钮图标路径
    // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
    // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

    // 新建会话按钮设置项
    // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
    // newButtonIcon?: string,//新建会话按钮图标路径
    // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

    // 关闭按钮设置项
    // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
    // closeButtonIcon?: string;//关闭按钮图标路径
    // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数
    // wrapstyle: {
    //   paddingTop: '20px',
    // }
    // logo: LogoPng,
  }

  // 支持自定义欢迎页面
  // const renderWelcome = () => {
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: '100%' }}>这是欢迎页</div>
  //   )
  // }
  // const renderQuickReplies = () => {
  //   return (
  //     <div style={{ marginLeft: 20, marginBottom: 10 }}>11111</div>
  //   )
  // }

// 埋点方法
  const onReportLog = useCallback((params: any) => {
    console.log(params);
    // const { id, btn_title } = params;
    // const { btn_label, ...res } = btn_title;
    // logCommon({
    //   type: id === 'button_click' ? 'Click' : '',
    //   payload: {
    //     name: btn_label,
    //     ...res,
    //   },
    // });
  }, []);

  // // 自定义会话历史brand区域，footer区域
  // const RenderHistoryConversationFooter = () => {
  //   return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的底部自定义模块</div>
  // }
  // const RenderHistoryConversationBrand = () => {
  //   return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的品牌区域自定义模块</div>
  // }

  const skillsMock = [
    {
      key: '',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '智能翻译',// 文案
      question: '翻译上面文字',// 发送时的命令
      agentId: '1',//发送时可指定智能体ID
      children: [
        {
          key: 'translate',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: 'AI翻译',// 文案
          question: 'AI翻译',// 发送时的命令
          agentId: 'translate',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
        },
        {
          key: 'translate-page',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '翻译此页面',// 文案
          question: '翻译此页面',// 发送时的命令
          agentId: 'translate-page',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log('翻译此页面'),// 技能点击回调 -》 由AiChat统一写入Composer
        }
      ],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'summary',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '网页总结',// 文案
      question: '网页总结',// 发送时的命令
      agentId: 'summary',//发送时可指定智能体ID
      // children?: Skill[],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => {
        console.log('点击网页总结');
      },// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'more',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '更多',// 文案
      question: '翻译上面文字',// 发送时的命令
      agentId: '1',//发送时可指定智能体ID
      children: [
        {
          key: 'default',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '图片文字抓取',// 文案
          question: '翻译上面文字',// 发送时的命令
          agentId: 'default',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
        },
        {
          key: 'text-condenser',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '缩写',// 文案
          question: '缩写',// 发送时的命令
          agentId: 'text-condenser',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
        },
        {
          key: 'text-expander',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '扩写',// 文案
          question: '扩写',// 发送时的命令
          agentId: 'text-expander',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
        },
        {
          key: 'text-polisher',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '润色',// 文案
          question: '润色',// 发送时的命令
          agentId: 'text-polisher',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
        },
        {
          key: 'grammar-corrector',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '修正拼写和语法',// 文案
          question: '修正拼写和语法',// 发送时的命令
          agentId: 'grammar-corrector',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
        },
      ],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    }
  ];

  const quoteOperationsMocks = {
    text: [
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'translate',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '总结',// 文案
        question: '总结',// 发送时的命令 --》 拼装
        agentId: 'summary',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '扩写',// 文案
        question: '扩写',// 发送时的命令 --》 拼装
        agentId: 'text-expander',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '缩写',// 文案
        question: '缩写',// 发送时的命令 --》 拼装
        agentId: 'text-condenser',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '润色',// 文案
        question: '润色',// 发送时的命令 --》 拼装
        agentId: 'text-polisher',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '修正拼写与语法',// 文案
        question: '修正拼写与语法',// 发送时的命令 --》 拼装
        agentId: 'grammar-corrector',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }
    ],
    web: [
      {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '总结',// 文案
      question: '总结',// 发送时的命令 --》 拼装
      agentId: 'summary',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
      },
      {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '澄清关键概念',// 文案
      question: '澄清关键概念',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '澄清关键概念',// 文案
        question: '澄清关键概念',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '识别假设',// 文案
        question: '识别假设',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '分析对比',// 文案
        question: '分析对比',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '了解作者',// 文案
        question: '了解作者',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '查找相关新闻',// 文案
        question: '查找相关新闻',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }
    ],
    image: [
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '图片解释',// 文案
        question: '图片解释',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '图片文字提取',// 文案
        question: '图片文字提取',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }
    ],
    file: [
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '总结文档内容',// 文案
        question: '总结文档内容',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '生成简短摘要',// 文案
        question: '生成简短摘要',// 发送时的命令 --》 拼装
        agentId: 'default',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }
    ],
  };

  return (
    <div style={{display: 'flex', alignItems: 'center', flexDirection:'column', background: 'grey'}}>
      <div style={{ height: 'calc(100vh - 200px)', width: 430, marginTop: 20, marginBottom: 20 }} id="chat-container">
        <AiChat
          navbar={navbarProps} //标题栏参数配置
          // renderNavbar={() => null} //支持自定义的navbar
          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={initialMessages} //初始化消息
          // renderMessageContent={renderMessageContent} //自定义卡片渲染
          config={config}
          onReportLog={onReportLog}
          // quickReplies={[{
          //   title: 'string',//页面展示的快捷问题
          //   content: 'realString',//点击后实际发送的问题
          //   url: '',//如果配置，点击后跳转链接
          //   isHighlight: true,
          //   isNew: true,
          //   img: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg'
          // }]}

          //历史会话参数配置
          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75
            title: '历史会话列表', //历史会话列表标题
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”
            // renderBrand: RenderHistoryConversationBrand, //支持传入自定义brand区域
            // renderFooter: RenderHistoryConversationFooter, //支持传入自定义底部区域
            showSearch: false,
          }}

          // 欢迎页参数配置
          welcome={{
            // open?: boolean //是否展示欢迎页，默认true
            riskTip: "内容由AI大模型生成，请谨慎识别", //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别"
            title: 'Hi～我是 AI助手',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            // showsubTitle: false,
            // logo: '',
            // openMessages 表示欢迎页初始问题，可选
            // openMessages: [{type: 'text', content: '欢迎使用问TA' }, {type:'list', content: '如何做好投资？'}],
            openMessages: [{type: 'list', content: '如何投资ETF？' }, {type:'list', content: '如何开通两融账户？'}],
            // 欢迎页标题配置，配置和上面的navbarProps相同，优先在欢迎页生效
            // navbar: {
              // 配置和上面的navbarProps相同，优先在欢迎页生效
              // title: '欢迎页标题',
              // wrapstyle: {
              //   paddingTop: '20px',
              // },
            // },
            // 支持自定义navbar
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}

          // 输入框参数配置
          composerConfig={{
            // text:'初始化问题',
            // showInternetSearch: false,
            // aboveNode:(<div >I am aboveNode~</div>),//reactNode
            // belowNode:(<div >I am belowNode~</div>),//reactNode
            // uploadConfig: {
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // action: '/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=11111111&Expire=720h',
              // fileList: fileList,
              // onChange: onChange,
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // fileList: fileList,
              // onChange: onChange,
              // headers: {
              //   'X-Content-Sha256': 'c975c2049dfe87d8d3c6002de962426ca96e504c7402abd01d21cb8fedf028f5',
              //   'Content-Type': 'application/json',
              // },
              // beforeUpload,
              // headers: uploadHeaders // 动态headers
              // customRequest,
              // popoverConfig: {
              //   content: popoverContent,
              // },
              // padUrlPrefix: (path:any) => `http://10.102.80.243/llmpf/api/proxy/down?Action=Download&Version=2022-01-01&IsAnonymous=true&Path=${encodeURIComponent(path)}`,
            // }
              skill: skillsMock,
              quoteOperations: quoteOperationsMocks,
          }}

          // 是否展示反馈弹窗
          // showFeedbackModal={true}
          // feedbackModalConfig={{
          //   title: 'string',
          //   inputPlaceholder: 'string',
          //   showLabels: true,
          // }}
          // 是否展示大模型消耗的tokens数量，默认true，如果是对接hiagent，则一般需要
          showToken={false}
          // 是否在答案结束后展示合规话术‘内容有Ai生产，balabala...’,默认true
          showHallucination={true}
        />
      </div>
    </div>


  );
};

import React from 'react';
import { default as RcSelect } from 'rc-select';
import { SelectProps as RcSelectProps } from 'rc-select';
import clsx from 'clsx';

export type SelectProps = RcSelectProps;

const Select: React.FC<SelectProps> = (props) => {
  const { className: customClassName } = props;
  return <RcSelect {...props} className={clsx(customClassName, 'WideComposer-select-outlined')} />;
};

// 从rc-select中导出所有组件
export * from 'rc-select';
export default Select;

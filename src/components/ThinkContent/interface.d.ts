// 思维链
export interface ThinkContentProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 思考中
   */
  isThinking?: boolean;
  /**
   * 思考内容
   */
  thinkContent: string;
  /**
   * 自定义深度思考样式
   */
  customDeepThinkingStyle?: React.ReactNode;
  
  /**
   * 自定义深度思考完成样式
   */
  customThinkingCompleteStyle?: React.ComponentType<{thinkTime: number}>;

  /**
   * 思考结束是否自动折叠深度思考
   */
  autoThinkCollapsed?: boolean;

  /**
   * 思考时间
   */
  thinkTime?: number;
}

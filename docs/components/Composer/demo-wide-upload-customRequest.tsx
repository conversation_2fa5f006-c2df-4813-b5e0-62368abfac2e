import React from 'react';
import {
  Composer,
  ComposerUploadFile,
  ComposerUploadChangeParam,
  ComposerCustomRequestOptions,
} from '@ht/chatui';

export default () => {
  const onSend = async (type: string, content: string, payload: object) => {
    return new Promise<boolean>((resolve) => {
      console.log('type', type, 'content', content, 'payload', payload);
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
  };

  const [fileList, setFileList] = React.useState<ComposerUploadFile[]>([]);
  const onChange = (evt: ComposerUploadChangeParam) => {
    console.log('fileList', evt);
    setFileList(evt.fileList);
  };

  const customRequest = async (option: ComposerCustomRequestOptions) => {
    const { file, onSuccess, onError } = option;
    const fakeRequest = new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('开始自定义上传文件', file);
        if (Math.random() > 0.3) {
          resolve({ code: 0, message: 'success', data: { test: 'test' } });
        } else {
          reject(new Error('fetch error'));
        }
      }, 1000);
    });
    try {
      const res = await fakeRequest;
      onSuccess(res);
    } catch (error) {
      onError(error);
    }
  };

  return (
    <Composer
      isWide
      text="ceshi"
      inputType="text"
      onSend={onSend}
      uploadConfig={{
        action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
        fileList: fileList,
        onChange: onChange,
        customRequest,
      }}
    ></Composer>
  );
};

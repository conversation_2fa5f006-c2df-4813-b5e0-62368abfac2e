.dumi-site-features {
  margin-bottom: 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding:20px;
    align-items: center;
    width: calc((100% - 96px) / 5);
    height: 186px;
    margin-bottom: 50px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    

    img {
      width: 88px;
      height: 88px;
      background-color: red;
    }
    .title {
      font-size: 20px;
      color: #000;
    }
    .desc {
      display: block;
      max-width: 272px;
      color: #8c8c8c;
      font-size: 16px;
      text-align: center;
    }
  }
  .item:hover {
    border-color: transparent;
    box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
  }

}

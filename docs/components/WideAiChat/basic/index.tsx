
import React, { useCallback } from 'react';
import { WideAiChat, AiChatHandle, Button, ComposerCustomRequestOptions } from '@ht/chatui';
import { sha256 } from 'js-sha256';

// import { log } from '@ht/xlog';
// import Welcome from './Welcome';
import '@ht/chatui/es/styles/index.less';

export default () => {
    // 消息列表
  // const { messages, appendMsg, updateMsg, prependMsgs, setTyping, getTyping, getMessages } = useMessages(initialMessages);
  // const { quickReplies } = useQuickReplies(defaultQuickReplies);
  // const msgRef = React.useRef(null);
  //操作调用chat组件内部函数引用
  // const ref = React.useRef<AiChatHandle>(null);

  // window.appendMsg = appendMsg;
  // window.updateMsg = updateMsg;
  // window.prependMsgs = prependMsgs;
  // window.setTyping = setTyping;
  // window.getTyping = getTyping;
  // window.getMessages = getMessages;
  // window.msgRef = msgRef;

  // 通用全局配置
  const config = {
    //租户id：表示当前系统
    appId: 'hiAgent',

    //用户id：代表当前系统唯一用户id
    userId: '002332',

    // 是否测试环境，用于魔方测试环境验证
    // isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },
    onFeedback: (...params: any) => {
      console.log('点赞接口', params);
    },
    //接口请求 method默认post，本示例参数以对接泰为智能体接口为例
    requests: {
      /**
       * 基础URL，接口请求前缀，拼接在下述方法的url前缀，如聊他业务线的公共前缀'fspa'
       * baseUrl支持传递字符串与函数
        //如果配置了前缀，对应配置转发代理说明，对应地址是智能体的实际发布的地址
        // proxy: {
        // '/hiAgent': {
        //   target: 'http://*************:3000/llmpf/api/proxy/api/v1/',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hiAgent/, '')
        // },
      */
     // 泰为智能提接口地址：
     //测试环境转发到- http://*************:3000/llmpf/api/proxy/api/v1/
     //生成环境转发到- http://*************:3000/llmpf/api/proxy/api/v1/
     //生成环境服务在dmz区域，需要开墙
     //代理配置示例：
      // '/hiAgent': {
      //   target: 'http://*************:3000/llmpf/api/proxy/api/v1/',
      //   changeOrigin: true,
      //   pathRewrite: { '^/hiAgent': '' },
      //   onProxyRes: function (proxyRes) {
      //     proxyRes.headers['cache-control'] = 'no-cache, no-transform';
      //   },
      // },
      baseUrl: '/hiAgent',//不同业务线可以自定义前缀
      // 如果对接泰为hiagent接口，需要设置platform='hiAgent'
      platform: 'hiAgent',
      // 对应泰为智能体的apikey，联系泰为平台获取
      appKey: 'd0e4lcgim975aeu7usa0',
      // appKey: 'd07gpo0im975aeu764t0',

      // //初始引导接口
      // init: {
      //   url: '',
      //   headers: { empid: '002332', token: 'token', deviceId: 'deviceId' },
      //   // requestTransfer: (input: object) => {
      //   //   return new Promise((resolve, reject) => {
      //   //     try {
      //   //       const parsedInput = {
      //   //         ...input,
      //   //         customerInput1: '123',
      //   //       };
      //   //       resolve(parsedInput);
      //   //     } catch (error) {
      //   //       reject(error);
      //   //     }
      //   //   });
      //   // },
      //   // responseTransfer: (output: object) => {
      //   //   return new Promise((resolve, reject) => {
      //   //     const parsedOutput = {
      //   //       ...output,
      //   //       customeroutput1: '123',
      //   //     };
      //   //     try {
      //   //       resolve(parsedOutput);
      //   //     } catch (error) {
      //   //       reject(error);
      //   //     }
      //   //   });
      //   // },
      // },

      //问答接口
      send: {
        url: '/chat_query',
        isAibag: true, //是否走的aibag链路
        needUpdateConversation : true,//是否需要调用会话更新接口，比如hiagent场景需要调用则传true
        stream: true,
        // requestTransfer(params:any) {
        //   if (true) {
        //     params.inputs = {'key':'htmlContent'}
        //   }
        //   return params
        // },
        // messageInterval:30, //前端展示数据帧间隔,默认30
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', deviceId: 'deviceId' },
      },

      //查询历史详情接口
      history: {
        url: '/get_conversation_messages',
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', deviceId: 'deviceId' },
        pageSize: 100,
      },
      //点赞点踩接口
      score: {
        url: '/feedback',
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', deviceId: 'deviceId' },
      },

      //停止生成接口，不配置则不展示停止生成功能
      stop: {
        url: '/stop_message',
        // headers: { empid: '002332', 'iv-user': '002332', token: 'token', deviceId: 'deviceId' },
      },

      // 历史会话列表，不配置则不展示历史会话列表
      historyConversation: {
        url: '/get_conversation_list',
        // headers: { empid: '002332', 'iv-user': '002332', token: 'token', deviceId: 'deviceId' },
      },
     
    },
    bridge: {
      // 原生bridge
      // 打开新页面
      openWebPage: (url: any) => {
        console.log('打开新页面', url);
      },
      // 调用原生方法打开页面预览文件支持word\pdf
      openFileViews: (url: any) => {
        console.log('预览文件', url);
      },
    },
    
    robot: {
      // logo: 'http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/ailogo.9262eff2.svg',
      showMsgLogo:true // 是否在消息答案左侧展示logo头像
    },
  //泰为智能体的前置输入变量，可选，如果涉及需要获取后传入
    // VariableConfigs:
    //   [
    //     {
    //       Key: "key",
    //       Name: "命中",
    //       Required: true,
    //       VariableType: "Enum",
    //       EnumValues: [
    //         "枚举1",
    //         "枚举2",
    //         "最大可以10"
    //       ],
    //       TextMaxLength: 32
    //     },
    //     {
    //       Key: "productCodes",
    //       Name: "productCodes",
    //       Required: true,
    //       VariableType: "Text",
    //       TextMaxLength: 32
    //     }, {
    //       Key: "code",
    //       Name: "文本枚举值",
    //       Required: true,
    //       VariableType: "Paragraph"
    //     }
    //   ]
  };

  // // 导航栏参数
  // const navbarProps = {

  //   // open?: boolean,//是否展示navbar，默认true  

  //   // 返回按钮设置项
  //   // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
  //   // returnButtonIcon?: string;//返回按钮图标路径
  //   // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

  //   // 标题区域设置
  //   showLogo: false,//是否展示logo,默认为true            
  //   // logo: LogoPng,// logo图标的地址 
  //   title: 'AI智能助手', //头部标题文案，展示于logo右侧,默认为空字符串
  //   // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

  //   // 历史会话按钮设置项
  //   // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
  //   // historyButtonIcon?: string,//历史会话按钮图标路径
  //   // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
  //   // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

  //   //新建会话按钮设置项
  //   // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
  //   // newButtonIcon?: string,//新建会话按钮图标路径               
  //   // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

  //   // 关闭按钮设置项
  //   // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
  //   // closeButtonIcon?: string;//关闭按钮图标路径
  //   // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数 
  //   // wrapstyle: {
  //   //   paddingTop: '20px',
  //   // }
  // }


  const renderFooterVersion = () => {
    return  <div className="ChatFooter-Version">内容由 AI 生成，无法确保信息的真实准确，仅供参考</div>;
  } 

  // 自定义历史会话品牌区
  // const renderBrand = () => {
  //   const handleNewConversation = () => {
  //     ref.current?.chatContext?.handleNewConversation?.();
  //     console.log('创建新会话');
  //   }
  //   return <div className="quickNav">
  //   <Button
  //     onClick={handleNewConversation}
  //     className="new_session_btn"
  //   >
  //     <div className="new_session_btn_wrap">
  //       <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/addchat.09d0d5dd.svg"></img>
  //       <span>开始新会话</span>
  //     </div>
  //   </Button>
  // </div>;
  // };

  // const renderFooter = () => {
  //   return <div
  //   className="feedback"
  //   onClick={() => {
  //     alert('反馈')
  //   }}
  // >
  //   <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/feedback.e05ace4f.svg"></img> <span>问题反馈</span>
  // </div>;
  // }


  const calculateHash = async (file: File): Promise<string> => {
    try {
      const buffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(buffer);
      return sha256(uint8Array);
    } catch (error) {
      console.error('计算哈希出错:', error);
      throw error;
    }
  };


    // 自定义上传实现（使用 XMLHttpRequest）
    const customRequest = async ({ file, onSuccess, onError, onProgress }: ComposerCustomRequestOptions) => {
      // setLoading(true);
      // setProgress(0);
  
      try {
        // 计算文件哈希
        const fileHash = await calculateHash(file);
  
       // 创建 XMLHttpRequest
        const xhr = new XMLHttpRequest();
  
        // 监听上传进度
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            // setProgress(percent);
            onProgress({ percent }, file);
          }
        };
  
        // 处理完成事件
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            let response;
            try {
              response = JSON.parse(xhr.responseText);
            } catch {
              response = xhr.responseText;
            }
            onSuccess(response, file);
          } else {
            onError(new Error(`上传失败: ${xhr.statusText}`), file);
          }
          // setLoading(false);
        };
  
        xhr.onerror = () => {
          onError(new Error('上传过程中发生错误'), file);
          // setLoading(false);
        };
  
        xhr.ontimeout = () => {
          onError(new Error('请求超时'), file);
          // setLoading(false);
        };
  
        // 5. 打开连接
        xhr.open('POST', `/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=${fileHash}&Expire=720h`, true);
  
        // 设置请求头
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-Content-Sha256', fileHash);
  
        // 6. 发送请求
        xhr.send(file);
  
      } catch (error) {
        // setLoading(false);
        onError(error as Error, file);
      }
    };

  const popoverContent = (
    <div>
      <div>最多支持10个大小不超过10MiB 的文档/图片</div>
      <div>
        · 文档格式支持：pdf、doc、docx、ppt、pptx、txt、csv、xls、xlsx、json
      </div>
      <div>· 图片格式支持：png、jpg、jpeg</div>
      {/* <div>· 音频格式支持：raw、wav、ogg、mp3</div> */}
    </div>
  );

    // 埋点方法
    const onReportLog = useCallback((params: any) => {
      // log(params);
      console.log(params);
    }, []);


  return (
      <div
        className="wide-screen-demo"
        style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }}
        id="chat-container"
      >
        <WideAiChat
          // ref={ref}
          // navbar={navbarProps} //标题栏参数配置
          renderNavbar={() => null} //自定义navbar
          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={[]}
          config={config}// 通用全局配置
          onReportLog={onReportLog} // 自定义埋点方法
          renderQuickReplies={() => null}
          renderFooterVersion={renderFooterVersion}//自定义页面底部
          showPushHistory={false} // 不需要在AiChat内推出历史会话面板了

        //   renderComposer={WideComposer}
          // renderWelcome={Welcome} //自定义欢迎页面
          welcome={{
            title: 'AI智能助手',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            showsubTitle: false,
            riskTip: ' ',
            // showRiskTip: false,
            openMessages: [
              { type: 'text', content: '您好！我是文档助理，专注于技术文档支持，可以协助您进行技术文档的编写与修订，以及技术方案的评审。您可以上传PDF格式的文档进行交流。' },
              { type: 'list', content: '如何撰写一份高质量的技术文档？' },
              { type: 'list', content: '在技术方案评审中需要注意那些关键点？' },
              { type: 'list', content: '能否提供一些基础平台技术文档的模板？' },
            ],
            // navbar: {
            // 配置和上面的navbarProps相同，优先在欢迎页生效
            // title: '欢迎页标题',
            // wrapstyle: {
            //   paddingTop: '20px',
            // },
            // },
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}
          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推              
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75          
            showLogo: true,
            title: 'AI智能助手',
            showCloseButton: false,
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域   
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”
            // renderBrand, //支持传入自定义brand区域
            // renderFooter, //支持传入自定义底部区域  
            showSearch: false, // 是否展示搜索栏
            // renderListItem(props:any){ //自定义历史列表元素区域
            //     console.log('props = ' + props);
            //     return <div>
            //       {props.question}test
            //     </div>
            //  }
          }}
          composerConfig={{
            // text:'初始化问题',
            showInternetSearch: false,
            // aboveNode:(<div >I am aboveNode~</div>),//reactNode，输入框上方插槽
            // belowNode:(<div >I am belowNode~</div>),//reactNode，输入框下方插槽
            uploadConfig: {
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // action: '/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=11111111&Expire=720h',
              // fileList: fileList,
              // onChange: onChange,
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // fileList: fileList,
              // onChange: onChange,
              // headers: {
              //   'X-Content-Sha256': 'c975c2049dfe87d8d3c6002de962426ca96e504c7402abd01d21cb8fedf028f5',
              //   'Content-Type': 'application/json',
              // },
              // beforeUpload,
              // headers: uploadHeaders // 动态headers
              customRequest,
              accept:
                '.pdf,.doc,.docx,.ppt,.pptx,.txt,.csv,.xls,.xlsx,.json,.png,.jpg,.jpeg',
              popoverConfig: {
                content: popoverContent,
              },
              beforeUpload: (file: { name: string; size: number; }) => {
                return new Promise((resolve, reject) => {
                  const temp = file.name.split('.');
                  const fileExt = temp[temp.length - 1];
                  if (
                    ![
                      'pdf',
                      'doc',
                      'docx',
                      'ppt',
                      'pptx',
                      'txt',
                      'csv',
                      'xls',
                      'xlsx',
                      'json',
                      'png',
                      'jpg',
                      'jpeg',
                    ].includes(fileExt)
                  ) {
                    console.error('上传文件类型不支持！');
                    reject('不支持该文件类型！');
                  }
                  if (file.size > 10 * 1024 * 1024) {
                    console.error('上传文件大小不支持！');
                    reject('只支持大小在 10 Mb 以内的文件！');
                  }
                  resolve(true);
                });
              },
              maxCount: 10,
              padUrlPrefix: (path: string | number | boolean) =>
                `http://10.102.80.243:3000/llmpf/api/proxy/down?Action=Download&Version=2022-01-01&Path=${encodeURIComponent(
                  path
                )}&IsAnonymous=true`,
              // padUrlPrefix: (path:any) => `http://10.102.80.243/llmpf/api/proxy/down?Action=Download&Version=2022-01-01&IsAnonymous=true&Path=${encodeURIComponent(path)}`,
            }
          }}

          // 是否展示大模型消耗的tokens数量，默认true，如果是hiagent则需要
          showToken={true}
          // 是否在答案结束后展示合规话术‘内容有Ai生产，balabala...’,默认true
          showHallucination={true}
          // 点赞点踩是否展示反馈区域
          showFeedbackModal={true}
          // 点赞点踩反馈区域配置
          feedbackModalConfig={{
            title: '反馈',
            inputPlaceholder: '请输入回答不满意的原因',
            showLabels: false,
          }}
        />
      </div>
  );
};
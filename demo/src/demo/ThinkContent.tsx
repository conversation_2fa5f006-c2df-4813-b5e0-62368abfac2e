import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { ThinkContent } from '../../../src';

const testThinkText = `### 这是思考内容，支持markdown，也可以传普通文本

1.**标题**

# 标题 1
## 标题 2
### 标题 3
#### 标题 4
##### 标题 5
###### 标题 6`;

export default () => (
  <DemoPage>
    <DemoSection title="思维链-思考完成，允许折叠">
      <ThinkContent thinkContent={testThinkText} />
    </DemoSection>
    <DemoSection title="思维链-思考中，不允许折叠">
      <ThinkContent thinkContent={testThinkText} isThinking/>
    </DemoSection>
  </DemoPage>
);

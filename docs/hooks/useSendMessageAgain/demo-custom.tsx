/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useSendMessageAgain } from '@ht/chatui';

export default () => {
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useSendMessageAgain({
    requestConfig: {
      platform: 'custom',
      url: '',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '016645',
      conversationId: 'cvhm0igldcehfetohfgg',
      messageId: '01JS162KBMTHYQ0Y267BERSMEA',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>重新生成回复</Button>
      <div>
        {conversationData?.resultData?.list?.map((item: any, index: number) => (
          <span key={index}>{item.content.text}</span>
        ))}
      </div>
    </div>
    )
}

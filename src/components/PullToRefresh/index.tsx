import React, { useState, useEffect, useRef, useCallback, useImperativeHandle } from 'react';
// import clsx from 'clsx';
import { setTransform } from '../../utils/style';
import { Flex } from '../Flex';
import canUse from '../../utils/canUse';
import smoothScroll from '../../utils/smoothScroll';
import { LoadingSpinner } from '../LoadingSpinner';
import PullIcon from './images/pull.svg'

const canPassive = canUse('passiveListener');
const listenerOpts = canPassive ? { passive: true } : false;
const listenerOptsWithoutPassive = canPassive ? { passive: false } : false;

type PullToRefreshStatus = 'pending' | 'pull' | 'active' | 'loading';

export interface PullToRefreshProps {
  distance?: number;
  loadingDistance?: number;
  distanceRatio?: number;
  loadMoreText?: string;
  maxDistance?: number;
  onRefresh?: () => Promise<any>;
  onScroll?: (event: React.UIEvent<HTMLDivElement, UIEvent>) => void;
  renderIndicator?: (status: PullToRefreshStatus, distance: number) => React.ReactNode;
  children: React.ReactNode;
}

export interface ScrollToEndOptions {
  animated?: boolean;
  force?: boolean;
}

interface PTRScrollToOptions extends ScrollToEndOptions {
  y: number | '100%';
}

export interface PullToRefreshHandle {
  scrollTo: (opts: PTRScrollToOptions) => void;
  scrollToEnd: (opts?: ScrollToEndOptions) => void;
  wrapperRef: React.RefObject<HTMLDivElement>;
}

export const PullToRefresh = React.forwardRef<PullToRefreshHandle, PullToRefreshProps>(
  (props, ref) => {
    const {
      distance: oDistance = 30,
      loadingDistance = 30,
      maxDistance,
      distanceRatio = 2,
      loadMoreText = '查看历史消息',
      children,
      onScroll,
      onRefresh,
      renderIndicator,
    } = props;

    const wrapperRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);

    const [distance, setDistance] = useState(0);
    const [status, setStatus] = useState<PullToRefreshStatus>('pending');
    // const [dropped, setDropped] = useState(false);
    const [disabled, setDisabled] = useState(!props.onRefresh);
    const sharedRef = useRef<any>({});
    const statusRef = useRef<PullToRefreshStatus>(status);
    const timer1 = useRef<ReturnType<typeof setTimeout>>();
    const timer2 = useRef<ReturnType<typeof setTimeout>>();
    // 历史消息接口正在调用中
    const historyPending = useRef(false);

    // 用来区分是不是手机端能touch的，手机端为false
    const useFallback = !canUse('touch');
    // 判断滚动是否还没有结束
    const isScrollingRef = useRef(false);
    const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    // 因为onRefresh是根据接口返回是否还有历史记录来传的，所以需要判断一下
    useEffect(() => {
      setDisabled(!props.onRefresh);
    }, [props.onRefresh])

    const handleIndicator = (pullStatus: PullToRefreshStatus, pullDistance: number) => {
      if (renderIndicator) {
        return () => renderIndicator(pullStatus, pullDistance);
      }

      // 初始什么都没操作的时候pending，手指下拉为pull，下拉没动为active，松手调接口时为loading
      if (!disabled && !useFallback && pullStatus === 'active') {
        return (
          <div className="PullToRefresh-nonFallback">
            下拉查看历史消息
            <img src={PullIcon} alt='pull' className="PullToRefresh-nonFallback-icon" />
          </div>
        );
      }

      if (pullStatus === 'loading') {
        return <LoadingSpinner />;
      }

      return null;
    }

    useEffect(() => {
      statusRef.current = status;
    }, [status]);

    const setContentStyle = (y: number) => {
      const content = contentRef.current;
      if (content) {
        setTransform(content, `translate3d(0px,${y}px,0)`);
      }
    };

    const scrollTo = ({ y, animated = true }: PTRScrollToOptions) => {
      const scroller = wrapperRef.current;

      if (!scroller) return;

      const offsetTop = y === '100%' ? scroller.scrollHeight - scroller.offsetHeight : y;

      if (animated) {
        smoothScroll({
          el: scroller,
          to: offsetTop,
          x: false,
        });
      } else {
        scroller.scrollTop = offsetTop;
      }
    };

    const scrollToEnd = useCallback(() => {
      // 判断滚动是否结束了（IOStouchend以后还会再趋势滚动一段，此时滚动还没结束），没有结束不做任何处理
      if (isScrollingRef.current === true) {
        return;
      }
      const scroller = wrapperRef.current;
      if (!scroller) return;
      scrollTo({ y: '100%', animated: false});
      // scrollTo({ y: scroller.scrollHeight - scroller.offsetHeight - 1 });
      // setTimeout(() => {
      //   scrollTo({ y: '100%' });
      // }, 50);
    }, []);

    const reset = useCallback(() => {
      setDistance(0);
      setStatus('pending');
      setContentStyle(0);
    }, []);

    const handleLoadMore = useCallback(() => {
      const scroller = wrapperRef.current;

      if (!scroller) return;
      setStatus('loading');
      // 避免已经是loading的状态又触发下拉刷新调接口
      if (historyPending.current === true) return;


      try {
        const sh = scroller.scrollHeight;
        historyPending.current = true;
        setTimeout(() => {
          onRefresh!().then((res) => {
            historyPending.current = false;
            const handleOffset = () => {
              scrollTo({
                y: scroller.scrollHeight - sh - 50,
                animated: false,
              });
            };

            clearTimeout(timer1.current);
            clearTimeout(timer2.current);
            handleOffset();
            timer1.current = setTimeout(handleOffset, 150);
            timer2.current = setTimeout(handleOffset, 250);

            reset();

            if (res && res.noMore) {
              setDisabled(true);
            }
          });
        }, 2000);
      } catch (ex) {
        // eslint-disable-next-line no-console
        console.error(ex);
        reset();
      }
    }, [onRefresh, reset]);

    const touchStart = useCallback((e: TouchEvent) => {
      sharedRef.current.startY = e.touches[0].clientY;
      sharedRef.current.canPull = wrapperRef.current && wrapperRef.current.scrollTop <= 0;

      if (sharedRef.current.canPull) {
        setStatus('pull');
        // setDropped(false);
      }
    }, []);

    const touchMove = useCallback(
      (e: TouchEvent) => {
        const currentY = e.touches[0].clientY;

        const { canPull, startY } = sharedRef.current;

        if (!canPull || currentY < startY || statusRef.current === 'loading') return;

        let dist = (currentY - startY) / distanceRatio;
        if (maxDistance && dist > maxDistance) {
          dist = maxDistance;
        }

        if (dist > 0) {
          if (e.cancelable) {
            e.preventDefault();
          }
          e.stopPropagation();

          setContentStyle(dist);
          setDistance(dist);
          setStatus(dist >= oDistance ? 'active' : 'pull');
        }
      },
      [distanceRatio, maxDistance, oDistance],
    );

    const touchEnd = useCallback(() => {
      // setDropped(true);

      if (statusRef.current === 'active') {
        handleLoadMore();
      } else {
        reset();
      }
    }, [handleLoadMore, reset]);

    const handleScroll = (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
      // 滚动正在进行，更新状态
      isScrollingRef.current = true;
      // 如果之前已经设置了定时器，则清除它
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // 设置一个新的定时器，用于检测滚动是否停止
      scrollTimeoutRef.current = setTimeout(() => {
        // 定时器到期，没有新的滚动事件发生，认为滚动已经停止
        isScrollingRef.current = false;
      }, 150);

      if (onScroll) {
        onScroll(e);
      }
    };

    useEffect(() => {
      const wrapper = wrapperRef.current;

      if (!wrapper || useFallback) return;

      if (disabled) {
        wrapper.removeEventListener('touchstart', touchStart);
        wrapper.removeEventListener('touchmove', touchMove);
        // wrapper.removeEventListener('touchend', touchEnd);
        // wrapper.removeEventListener('touchcancel', touchEnd);
        wrapper.ontouchend = null; // 用这种方式只监听一次，重复监听多次会导致第二次监听到以后下拉没有loading状态
        wrapper.ontouchcancel = null; // 用这种方式只监听一次，重复监听多次会导致第二次监听到以后下拉没有loading状态
      } else {
        wrapper.addEventListener('touchstart', touchStart, listenerOpts);
        wrapper.addEventListener('touchmove', touchMove, listenerOptsWithoutPassive);
        // wrapper.addEventListener('touchend', touchEnd);
        // wrapper.addEventListener('touchcancel', touchEnd);
        wrapper.ontouchend = touchEnd;
        wrapper.ontouchcancel = touchEnd;
      }
    }, [disabled, touchEnd, touchStart, touchMove, useFallback]);

    useEffect(() => {
      if (status === 'loading' && !useFallback) {
        setContentStyle(loadingDistance);
      }
    }, [loadingDistance, status, useFallback]);

    useImperativeHandle(
      ref,
      () => ({
        scrollTo,
        scrollToEnd,
        wrapperRef,
      }),
      [scrollToEnd],
    );

    return (
      <div className="PullToRefresh" ref={wrapperRef} onScroll={handleScroll}>
        <div className="PullToRefresh-inner">
          <div
            // className={clsx('PullToRefresh-content', {
            //   'PullToRefresh-transition': dropped,
            // })}
            ref={contentRef}
          >
            <div className="PullToRefresh-indicator">{handleIndicator(status, distance)}</div>
            {!disabled && useFallback && status !== 'loading' && (
              <Flex className="PullToRefresh-fallback" center>
                {/* {handleIndicator(status, oDistance)} */}
                <div onClick={handleLoadMore} className="PullToRefresh-loadMore">{loadMoreText}</div>
              </Flex>
            )}
            {React.Children.only(children)}
          </div>
        </div>
      </div>
    );
  },
);

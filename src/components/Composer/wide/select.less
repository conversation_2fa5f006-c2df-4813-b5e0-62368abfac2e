.WideComposer-select.WideComposer-select-in-form-item {
  width: 100%;
}
.WideComposer-select {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #000;
  font-size: 14px;
  line-height: 1.5714;
  list-style: none;
  position: relative;
  display: inline-flex;
  cursor: pointer;
}
.WideComposer-select:not(.WideComposer-select-customize-input) .WideComposer-select-selector {
  position: relative;
  transition: all 0.2s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}
.WideComposer-select:not(.WideComposer-select-customize-input) .WideComposer-select-selector input {
  cursor: pointer;
}
.WideComposer-select-show-search.WideComposer-select:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  cursor: text;
}
.WideComposer-select-show-search.WideComposer-select:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector
  input {
  cursor: auto;
  color: inherit;
  height: 100%;
}
.WideComposer-select-disabled.WideComposer-select:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  cursor: not-allowed;
}
.WideComposer-select-disabled.WideComposer-select:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector
  input {
  cursor: not-allowed;
}
.WideComposer-select:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector
  .WideComposer-select-selection-search-input {
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  outline: none;
  appearance: none;
  font-family: inherit;
}
.WideComposer-select:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector
  .WideComposer-select-selection-search-input::-webkit-search-cancel-button {
  display: none;
  appearance: none;
}
.WideComposer-select .WideComposer-select-selection-item {
  flex: 1;
  font-weight: normal;
  position: relative;
  user-select: none;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.WideComposer-select .WideComposer-select-selection-item > .WideComposer-typography {
  display: inline;
}
.WideComposer-select .WideComposer-select-selection-placeholder {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}
.WideComposer-select .WideComposer-select-arrow {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.25);
  font-style: normal;
  line-height: 1;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 50%;
  inset-inline-start: auto;
  inset-inline-end: calc(12px - 1px);
  height: 12px;
  margin-top: calc(12px * -1 / 2);
  font-size: 12px;
  pointer-events: none;
  transition: opacity 0.3s ease;
}
.WideComposer-select .WideComposer-select-arrow > * {
  line-height: 1;
}
.WideComposer-select .WideComposer-select-arrow svg {
  display: inline-block;
}
.WideComposer-select .WideComposer-select-arrow .WideComposericon {
  vertical-align: top;
  transition: transform 0.3s;
}
.WideComposer-select .WideComposer-select-arrow .WideComposericon > svg {
  vertical-align: top;
}
.WideComposer-select .WideComposer-select-arrow .WideComposericon:not(.WideComposer-select-suffix) {
  pointer-events: auto;
}
.WideComposer-select-disabled .WideComposer-select .WideComposer-select-arrow {
  cursor: not-allowed;
}
.WideComposer-select .WideComposer-select-arrow > *:not(:last-child) {
  margin-inline-end: 8px;
}
.WideComposer-select .WideComposer-select-selection-wrap {
  display: flex;
  width: 100%;
  position: relative;
  min-width: 0;
}
.WideComposer-select .WideComposer-select-selection-wrap:after {
  content: '\a0';
  width: 0;
  overflow: hidden;
}
.WideComposer-select .WideComposer-select-prefix {
  flex: none;
  margin-inline-end: 4px;
}
.WideComposer-select .WideComposer-select-clear {
  position: absolute;
  top: 50%;
  inset-inline-start: auto;
  inset-inline-end: calc(12px - 1px);
  z-index: 1;
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-top: calc(12px * -1 / 2);
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  font-style: normal;
  line-height: 1;
  text-align: center;
  text-transform: none;
  cursor: pointer;
  opacity: 0;
  transition: color 0.2s ease, opacity 0.3s ease;
  text-rendering: auto;
}
.WideComposer-select .WideComposer-select-clear:before {
  display: block;
}
.WideComposer-select .WideComposer-select-clear:hover {
  color: rgba(0, 0, 0, 0.45);
}
.WideComposer-select:hover .WideComposer-select-clear {
  opacity: 1;
  background: white;
  border-radius: 50%;
}
.WideComposer-select-status-error.WideComposer-select-has-feedback .WideComposer-select-clear,
.WideComposer-select-status-warning.WideComposer-select-has-feedback .WideComposer-select-clear,
.WideComposer-select-status-success.WideComposer-select-has-feedback .WideComposer-select-clear,
.WideComposer-select-status-validating.WideComposer-select-has-feedback .WideComposer-select-clear {
  inset-inline-end: calc(calc(12px - 1px) + 14px + 8px);
}
.WideComposer-select-single {
  font-size: 14px;
  height: @composer-wide-llm-height;
}
.WideComposer-select-single .WideComposer-select-selector {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: @composer-wide-llm-color;
  font-size: 14px;
  line-height: 1.5714;
  list-style: none;
  font-family: inherit;
  display: flex;
  border-radius: 2px;
  flex: 1 1 auto;
}
.WideComposer-select-single
  .WideComposer-select-selector
  .WideComposer-select-selection-wrap:after {
  line-height: calc(@composer-wide-llm-height - 1px * 2);
}
.WideComposer-select-single .WideComposer-select-selector .WideComposer-select-selection-search {
  position: absolute;
  inset: 0;
  width: 100%;
}
.WideComposer-select-single
  .WideComposer-select-selector
  .WideComposer-select-selection-search-input {
  width: 100%;
  -webkit-appearance: textfield;
}
.WideComposer-select-single .WideComposer-select-selector .WideComposer-select-selection-item,
.WideComposer-select-single
  .WideComposer-select-selector
  .WideComposer-select-selection-placeholder {
  display: block;
  padding: 0;
  line-height: calc(@composer-wide-llm-height - 1px * 2);
  transition: all 0.3s, visibility 0s;
  align-self: center;
}
.WideComposer-select-single
  .WideComposer-select-selector
  .WideComposer-select-selection-placeholder {
  transition: none;
  pointer-events: none;
}
.WideComposer-select-single .WideComposer-select-selector:after,
.WideComposer-select-single
  .WideComposer-select-selector
  .WideComposer-select-selection-item:empty:after,
.WideComposer-select-single
  .WideComposer-select-selector
  .WideComposer-select-selection-placeholder:empty:after {
  display: inline-block;
  width: 0;
  visibility: hidden;
  content: '\a0';
}
.WideComposer-select-single.WideComposer-select-show-arrow .WideComposer-select-selection-item,
.WideComposer-select-single.WideComposer-select-show-arrow .WideComposer-select-selection-search,
.WideComposer-select-single.WideComposer-select-show-arrow
  .WideComposer-select-selection-placeholder {
  padding-inline-end: 18px;
}
.WideComposer-select-single.WideComposer-select-open .WideComposer-select-selection-item {
  color: rgba(0, 0, 0, 0.25);
}
.WideComposer-select-single:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  width: 100%;
  height: 100%;
  align-items: center;
  padding: 0 calc(12px - 1px);
}
.WideComposer-select-single:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector
  .WideComposer-select-selection-search-input {
  height: calc(@composer-wide-llm-height - 1px * 2);
  font-size: 14px;
}
.WideComposer-select-single:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector:after {
  line-height: calc(@composer-wide-llm-height - 1px * 2);
}
.WideComposer-select-single.WideComposer-select-customize-input
  .WideComposer-select-selector:after {
  display: none;
}
.WideComposer-select-single.WideComposer-select-customize-input
  .WideComposer-select-selector
  .WideComposer-select-selection-search {
  position: static;
  width: 100%;
}
.WideComposer-select-single.WideComposer-select-customize-input
  .WideComposer-select-selector
  .WideComposer-select-selection-placeholder {
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  padding: 0 calc(12px - 1px);
}
.WideComposer-select-single.WideComposer-select-customize-input
  .WideComposer-select-selector
  .WideComposer-select-selection-placeholder:after {
  display: none;
}
.WideComposer-select-dropdown {
  box-sizing: border-box;
  margin: 0;
  padding: 4px;
  color: #000;
  font-size: 14px;
  line-height: 1.5714;
  list-style: none;
  position: absolute;
  top: -9999px;
  z-index: 1050;
  overflow: hidden;
  font-variant: initial;
  background-color: white;
  border-radius: 4px;
  outline: none;
  box-shadow: 0px 1px 10px 0px #f0f1f3;
  border-radius: 4px;
  border: 1px solid #dee8f5;
}
// .WideComposer-select-dropdown.WideComposer-slide-up-enter.WideComposer-slide-up-enter-active.WideComposer-select-dropdown-placement-bottomLeft,
// .WideComposer-select-dropdown.WideComposer-slide-up-appear.WideComposer-slide-up-appear-active.WideComposer-select-dropdown-placement-bottomLeft {
//   animation-name: antSlideUpIn;
// }
// .WideComposer-select-dropdown.WideComposer-slide-up-enter.WideComposer-slide-up-enter-active.WideComposer-select-dropdown-placement-topLeft,
// .WideComposer-select-dropdown.WideComposer-slide-up-appear.WideComposer-slide-up-appear-active.WideComposer-select-dropdown-placement-topLeft,
// .WideComposer-select-dropdown.WideComposer-slide-up-enter.WideComposer-slide-up-enter-active.WideComposer-select-dropdown-placement-topRight,
// .WideComposer-select-dropdown.WideComposer-slide-up-appear.WideComposer-slide-up-appear-active.WideComposer-select-dropdown-placement-topRight {
//   animation-name: antSlideDownIn;
// }
// .WideComposer-select-dropdown.WideComposer-slide-up-leave.WideComposer-slide-up-leave-active.WideComposer-select-dropdown-placement-bottomLeft {
//   animation-name: antSlideUpOut;
// }
// .WideComposer-select-dropdown.WideComposer-slide-up-leave.WideComposer-slide-up-leave-active.WideComposer-select-dropdown-placement-topLeft,
// .WideComposer-select-dropdown.WideComposer-slide-up-leave.WideComposer-slide-up-leave-active.WideComposer-select-dropdown-placement-topRight {
//   animation-name: antSlideDownOut;
// }
.WideComposer-select-dropdown-hidden {
  display: none;
}
.WideComposer-select-dropdown .WideComposer-select-item {
  position: relative;
  display: block;
  min-height: @composer-wide-llm-height;
  padding: 5px 8px;
  color: @composer-wide-llm-option-color;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.51742;
  box-sizing: border-box;
  cursor: pointer;
  transition: background 0.3s ease;
  border-radius: 2px;
}

.WideComposer-select-dropdown .WideComposer-select-item-option {
  display: flex;
}
.WideComposer-select-dropdown .WideComposer-select-item-option-content {
  flex: auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.WideComposer-select-dropdown .WideComposer-select-item-option-state {
  flex: none;
  display: flex;
  align-items: center;
}
.WideComposer-select-dropdown
  .WideComposer-select-item-option-active:not(.WideComposer-select-item-option-disabled) {
  background-color: @composer-wide-llm-option-selected-bg;
}
.WideComposer-select-dropdown
  .WideComposer-select-item-option-selected:not(.WideComposer-select-item-option-disabled) {
  color: @composer-wide-llm-option-selected-color;
  font-weight: 600;
  background-color: @composer-wide-llm-option-selected-bg;
}
.WideComposer-select-dropdown
  .WideComposer-select-item-option-selected:not(.WideComposer-select-item-option-disabled)
  .WideComposer-select-item-option-state {
  color: @composer-wide-llm-option-selected-bg;
}
.WideComposer-select-dropdown .WideComposer-select-item-option-disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}
.WideComposer-select-dropdown
  .WideComposer-select-item-option-disabled.WideComposer-select-item-option-selected {
  background-color: rgba(0, 0, 0, 0.04);
}

.WideComposer-select-dropdown .WideComposer-select-item-empty {
  position: relative;
  display: block;
  min-height: @composer-wide-llm-height;
  padding: 5px 12px;
  color: rgba(0, 0, 0, 0.25);
  font-weight: normal;
  font-size: 14px;
  line-height: 1.51742;
  box-sizing: border-box;
}
.WideComposer-select-dropdown
  .WideComposer-select-item-option-selected:has(+ .WideComposer-select-item-option-selected) {
  border-end-start-radius: 0;
  border-end-end-radius: 0;
}
.WideComposer-select-dropdown
  .WideComposer-select-item-option-selected:has(+ .WideComposer-select-item-option-selected)
  + .WideComposer-select-item-option-selected {
  border-start-start-radius: 0;
  border-start-end-radius: 0;
}
.WideComposer-select-dropdown-rtl {
  direction: rtl;
}
.WideComposer-slide-up-enter,
.WideComposer-slide-up-appear {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-slide-up-leave {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
// .WideComposer-slide-up-enter.WideComposer-slide-up-enter-active,
// .WideComposer-slide-up-appear.WideComposer-slide-up-appear-active {
//   animation-name: antSlideUpIn;
//   animation-play-state: running;
// }
// .WideComposer-slide-up-leave.WideComposer-slide-up-leave-active {
//   animation-name: antSlideUpOut;
//   animation-play-state: running;
//   pointer-events: none;
// }
// .WideComposer-slide-up-enter,
// .WideComposer-slide-up-appear {
//   transform: scale(0);
//   transform-origin: 0% 0%;
//   opacity: 0;
//   animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
// }
.WideComposer-slide-up-enter-prepare,
.WideComposer-slide-up-appear-prepare {
  transform: scale(1);
}
.WideComposer-slide-up-leave {
  animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
}
.WideComposer-slide-down-enter,
.WideComposer-slide-down-appear {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-slide-down-leave {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-slide-down-enter.WideComposer-slide-down-enter-active,
.WideComposer-slide-down-appear.WideComposer-slide-down-appear-active {
  animation-name: antSlideDownIn;
  animation-play-state: running;
}
.WideComposer-slide-down-leave.WideComposer-slide-down-leave-active {
  animation-name: antSlideDownOut;
  animation-play-state: running;
  pointer-events: none;
}
.WideComposer-slide-down-enter,
.WideComposer-slide-down-appear {
  transform: scale(0);
  transform-origin: 0% 0%;
  opacity: 0;
  animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}
.WideComposer-slide-down-enter-prepare,
.WideComposer-slide-down-appear-prepare {
  transform: scale(1);
}
.WideComposer-slide-down-leave {
  animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
}
.WideComposer-move-up-enter,
.WideComposer-move-up-appear {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-move-up-leave {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-move-up-enter.WideComposer-move-up-enter-active,
.WideComposer-move-up-appear.WideComposer-move-up-appear-active {
  animation-name: antMoveUpIn;
  animation-play-state: running;
}
.WideComposer-move-up-leave.WideComposer-move-up-leave-active {
  animation-name: antMoveUpOut;
  animation-play-state: running;
  pointer-events: none;
}
.WideComposer-move-up-enter,
.WideComposer-move-up-appear {
  opacity: 0;
  animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
}
.WideComposer-move-up-leave {
  animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
}
.WideComposer-move-down-enter,
.WideComposer-move-down-appear {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-move-down-leave {
  animation-duration: 0.2s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.WideComposer-move-down-enter.WideComposer-move-down-enter-active,
.WideComposer-move-down-appear.WideComposer-move-down-appear-active {
  animation-name: antMoveDownIn;
  animation-play-state: running;
}
.WideComposer-move-down-leave.WideComposer-move-down-leave-active {
  animation-name: antMoveDownOut;
  animation-play-state: running;
  pointer-events: none;
}
.WideComposer-move-down-enter,
.WideComposer-move-down-appear {
  opacity: 0;
  animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
}
.WideComposer-move-down-leave {
  animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
}
.WideComposer-select-rtl {
  direction: rtl;
}
.WideComposer-select-compact-item:not(.WideComposer-select-compact-last-item) {
  margin-inline-end: calc(1px * -1);
}
.WideComposer-select-compact-item:hover > *,
.WideComposer-select-compact-item:active > * {
  z-index: 2;
}
.WideComposer-select-compact-item.WideComposer-select-focused {
  z-index: 2;
}
.WideComposer-select-compact-item[disabled] > * {
  z-index: 0;
}
.WideComposer-select-compact-item:not(.WideComposer-select-compact-first-item):not(
    .WideComposer-select-compact-last-item
  )
  > .WideComposer-select-selector {
  border-radius: 0;
}
.WideComposer-select-compact-item:not(
    .WideComposer-select-compact-last-item
  ).WideComposer-select-compact-first-item
  > .WideComposer-select-selector,
.WideComposer-select-compact-item:not(
    .WideComposer-select-compact-last-item
  ).WideComposer-select-compact-first-item.WideComposer-select-sm
  > .WideComposer-select-selector,
.WideComposer-select-compact-item:not(
    .WideComposer-select-compact-last-item
  ).WideComposer-select-compact-first-item.WideComposer-select-lg
  > .WideComposer-select-selector {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}
.WideComposer-select-compact-item:not(
    .WideComposer-select-compact-first-item
  ).WideComposer-select-compact-last-item
  > .WideComposer-select-selector,
.WideComposer-select-compact-item:not(
    .WideComposer-select-compact-first-item
  ).WideComposer-select-compact-last-item.WideComposer-select-sm
  > .WideComposer-select-selector,
.WideComposer-select-compact-item:not(
    .WideComposer-select-compact-first-item
  ).WideComposer-select-compact-last-item.WideComposer-select-lg
  > .WideComposer-select-selector {
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}
.WideComposer-select-outlined:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  border: @composer-wide-llm-border;
  background: @composer-wide-llm-bg;
}
.WideComposer-select-outlined:not(.WideComposer-select-disabled):not(
    .WideComposer-select-customize-input
  ):not(.WideComposer-pagination-size-changer):hover
  .WideComposer-select-selector {
  border-color: @composer-wide-llm-border-hover;
}
.WideComposer-select-focused.WideComposer-select-outlined:not(.WideComposer-select-disabled):not(
    .WideComposer-select-customize-input
  ):not(.WideComposer-pagination-size-changer)
  .WideComposer-select-selector {
  border-color: @composer-wide-llm-border-active;
  outline: 0;
}
.WideComposer-select-outlined:not(.WideComposer-select-disabled):not(
    .WideComposer-select-customize-input
  ):not(.WideComposer-pagination-size-changer)
  .WideComposer-select-prefix {
  color: #000;
}
.WideComposer-select-outlined.WideComposer-select-disabled:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  background: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.25);
}

.WideComposer-select-filled:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid transparent;
  color: #000;
}
.WideComposer-select-filled:not(.WideComposer-select-disabled):not(
    .WideComposer-select-customize-input
  ):not(.WideComposer-pagination-size-changer):hover
  .WideComposer-select-selector {
  background: rgba(0, 0, 0, 0.06);
}
.WideComposer-select-focused.WideComposer-select-filled:not(.WideComposer-select-disabled):not(
    .WideComposer-select-customize-input
  ):not(.WideComposer-pagination-size-changer)
  .WideComposer-select-selector {
  background: white;
  border-color: @brand-1;
  outline: 0;
}
.WideComposer-select-filled.WideComposer-select-disabled:not(.WideComposer-select-customize-input)
  .WideComposer-select-selector {
  border-color: #d9d9d9;
  background: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.25);
}

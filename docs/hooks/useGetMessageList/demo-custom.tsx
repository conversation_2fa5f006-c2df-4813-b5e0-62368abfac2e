/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useGetMessageList } from '@ht/chatui';

export default () => {
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useGetMessageList({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '002332',
      conversationId: '16ba2e40-4f4f-4dbb-b690-d1440b6b99d0',
      pageNum: 1,
      pageSize: 10,
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>获取消息列表</Button>
      <div>
        {conversationData?.resultData?.list?.map((item: any, index: number) => (
          <div key={index}>
            {item.list.map((subItem: any, subIndex: number) => {
              return (
                <div key={subIndex}>
                  {subItem?.content?.text}
                </div>
              )
            })}
          </div>
        ))}
      </div>
    </div>
    )
}

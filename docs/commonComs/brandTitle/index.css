.dumi-site-brand-title {
  margin-bottom: 50px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .title {
    margin: 0 0 32px;
    display: inline-block;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, sans-serif;
    color: #83cdf8;
    font-size: 180px;
    line-height: 1;

    .highlight {
      color: transparent;
      text-shadow: 0 10px 20px rgba(22, 119, 255, .15);
      background: linear-gradient(30deg, #90d5ff 30%, #65a5ff);
      -webkit-background-clip: text;
      background-clip: text;
    }
    .normal {
      color: #83cdf8;
      font-size:90px;
      font-weight: bold;
    }
    
  }

  .subTitle {
    margin: 32px;
    color: #4f5866;
    font-size: 20px;
  }

  .actions {
    /* margin-top: 10px; */
    display: flex;
    justify-content: center;
    align-items: center;
    
    .btn:nth-child(even) {
      border-radius: 6px;
      background: #90d5ff;
      color: #fff;
      margin-right: 16px;
      padding: 8px 16px;
      text-decoration:none;
    }
    .btn:nth-child(odd){
      border-radius: 6px;
      background: #1677ff;
      color: #fff;
      margin-right: 16px;
      padding: 8px 16px;
      text-decoration:none;
    }
  }

}

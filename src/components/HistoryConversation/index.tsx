/* eslint-disable import/no-extraneous-dependencies */
import React, { useState, useEffect } from 'react';
import GroupTitle from './GroupTitle';
import SearchBar from '../SearchBar';
import ConversationsItemComponent from './ItemCom';
import { Navbar } from '../Navbar';
import { groupByTimeRange } from './utils';
import { HistoryConversationProps, HistoryConversationCustomerProps, ConversationItemProps, GroupedItem } from './interface';

export const HistoryConversation = (props: HistoryConversationProps & HistoryConversationCustomerProps) => {
  const {
    style,
    title="历史会话记录",
    standaloneMode = true, // 默认独立使用，在集成对话组件中standaloneMode=false
    navbar,
    renderTitle,
    renderBrand,
    showSearch=true,
    searchPlaceholder="请输入搜索关键字",
    list,
    activeConversationId,
    onConversationClick,
    onConversationRename,
    onConversationDelete,
    renderFooter,
    renderListItem,
    showDeleteConversation = false,
    showRename = false,
  } = props;

  const [groupedConversationItems, setGroupedConversationItems] = useState<GroupedItem[]>([]);

  // 列表初始化
  useEffect(() => {
    setGroupedConversationItems(groupByTimeRange(list));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(list)])

  const onSearch = (keyword: string) => {
    const newList = list?.filter((item: any) => item.question.includes(keyword));
    setGroupedConversationItems(groupByTimeRange(newList));
  }

  const onClear = () => {
    setGroupedConversationItems(groupByTimeRange(list));
  }

  return (
    <div className='conversationWrap' style={{...style}}>
      {/* 标题区域 */}
      {renderTitle ? renderTitle() :  !standaloneMode && (
        <Navbar
          {...navbar}
          title={navbar?.title ?? title} // 兼容历史版本直接传入title
          // 其他历史会话相关navbar默认参数变更至PreprocessNavBarConfig
          // showReturnButton={false}
          // showLogo={true}
          // showHistoryButton={false}
          // logoAndTitlePosition="left"
        />
      )}
      {/* 品牌区域 */}
      {!standaloneMode && renderBrand && renderBrand()}
      {/* 搜索区域 */}
      <div className='searchBarWrap'>
      {showSearch && (
        <SearchBar
          onInput={onSearch}
          onSearch={onSearch}
          onClear={onClear} 
          searchPlaceholder={searchPlaceholder}      
        />
      )}
      </div> 
      {/* 列表区域 */}       
      <div className='contentWrap'>
        {groupedConversationItems?.map((groupedItem: GroupedItem, index) => {
          return (
            <div key={index} className='conversation'>
              <div className='title'>
                <GroupTitle title={groupedItem.group} />
              </div>
              {groupedItem?.items?.map((conversation: ConversationItemProps) => {
                return (
                  <div className='item' key={conversation.conversationId}>
                    {renderListItem ? renderListItem({
                      ...conversation,
                      activeConversationId
                    }) : 
                    <ConversationsItemComponent                     
                      {...conversation} 
                      activeConversationId={activeConversationId} 
                      onConversationClick={onConversationClick}
                      onConversationRename={onConversationRename}
                      onConversationDelete={onConversationDelete}     
                      showDeleteConversation={showDeleteConversation}
                      showRename={showRename}
                    />}
                  </div>
                )
              })}
            </div>
          );
        })}
      </div>     
      {/* 底部区域 */}
      {renderFooter && renderFooter()}
    </div>
  );
};

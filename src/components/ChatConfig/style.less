.ChatConfig {
    width: 100%;
    margin-top: 43px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    padding-left: 12px;
    padding-right: 12px;

    &-isNew {
        height: 100%;
    }

    &-editing {
        position: absolute;
        // top: 48px;
        width: 100%;
        padding-left: inherit;
        padding-right: inherit;
    }

    &-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 12;
    }

    &-Container {
        position: relative;
        z-index: 13;
        width: 100%;
        display: grid;
        background: #fff;
    }

    &-Title {
        width: 100%;
        height: 54px;
        background: linear-gradient(50deg, rgba(100, 195, 255, 0.06) 0%, rgba(52, 70, 255, 0.06) 100%);
        border-radius: 6px 6px 0px 0px;
        border: 1px solid rgba(31, 90, 210, 0.12);

        display: flex;
        align-items: center;
        padding-left: 24px;
        font-size: 16px;
        font-weight: 600;
        color: #222222;
        line-height: 22px;
    }

    &-Body {
        width: 100%;
        // background: rgba(255, 255, 255, 0.97);
        background: #f4f6ff;
        border-radius: 0px 0px 6px 6px;
        border: 1px solid #CFDCF9;
        padding: 24px;
        border-top: transparent;
    }

    &-Item {
        margin-top: 9px;
        display: flex;
        align-items: center;
    }

    &-ItemTitle {
        width: 125px;
        display: flex;
        align-items: center;
    }

    &-TitleText {
        width: 103px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    &-ItemLabel {
        max-width: 99px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    &-ErrorInstru {
        margin-left: 135px;
        font-size: 12px;
        color: red;
    }

    &-Instru {
        margin-left: 8px;
    }

    &-InstruIcon {
        width: 14px;
        height: 14px;

    }

    &-input {
        width: 73%;
        margin-left: 10px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #BBBBBB;
    }

    &-inputErr {
        width: 73%;
        margin-left: 10px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid red;
    }

    &-textArea {
        width: 73%;
        margin-left: 10px;
        height: 48px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #BBBBBB;
    }

    &-textAreaErr {
        width: 73%;
        margin-left: 10px;
        height: 48px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid red;
    }

    &-Required {
        width: 8px;
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FF4D4D;
        line-height: 14px;
    }

    &-Footer {
        float: right;
        display: flex;
        margin-top: 24px;
    }

    &-CancelButton {
        width: 60px;
        height: 32px;
        border-radius: 3px;
        border: 1px solid #BBBBBB;
        display: flex;
        align-items: center;
        padding-left: 16px;
        // padding-right: 16px;
        margin-right: 16px;
        cursor: pointer;

        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 16px;
    }

    &-OkButton {
        // width: 60px;
        height: 32px;
        background: #3B68B7;
        border-radius: 3px;
        display: flex;
        align-items: center;
        padding-left: 16px;
        padding-right: 16px;
        cursor: pointer;

        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 16px;
    }

    &-show {
        // width: 100%;
        width: 648px;
        height: 54px;
        background: linear-gradient(50deg, rgba(100, 195, 255, 0.06) 0%, rgba(52, 70, 255, 0.06) 100%);
        border-radius: 6px;
        border: 1px solid rgba(31, 90, 210, 0.12);

        display: flex;
        align-items: center;
        justify-content: space-between;
        // margin-top: 48px;
        padding-left: 24px;
        padding-right: 24px;
        font-size: 16px;
        font-weight: 500;
        color: #222222;
        line-height: 22px;
    }

    &-ShowMsg {
        display: flex;
        align-items: center;
    }

    &-MsgIcon {
        width: 22px;
        height: 22px;
        margin-right: 20px;
    }

    &-EditBtn {
        width: 78px;
        height: 29px;
        background: #FFFFFF;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.14);
        border-radius: 2px;
        display: flex;
        align-items: center;
        padding-left: 12px;
        cursor: pointer;
        font-size: 14px;
    }

    &-EditIcon {
        width: 14px;
        height: 16px;
        margin-right: 11px;
    }
}

/**
 * navbar形态有以下几种：
 * 1. pc端：右：历史会话图标 + 发起新会话图标 + 关闭图标
 * 2. pc端历史会话：左：logo图标 + 标题；右：发起新会话图标 + 关闭图标
 * 3. app端新会话：左： 返回图标；右：历史会话图标 + 发起新会话图标
 * 4. app端会话中：左： 返回图标；中：logo图标 ；右：历史会话图标 + 发起新会话图标
 * 5. app端历史会话：左：logo图标 + 标题
 */
import React from 'react';
import canUse from '../../utils/canUse';
import { Tooltip } from '../Tooltip';
import deepseekChat from './images/deepseekChat.png';
import AppBack from './images/appBack.png';
import HistoryIcon from './images/historyIcon.svg';
import NewChatIcon from './images/newChatIcon.svg';
import closeIcon from './images/closeIcon.svg';

export type NavbarProps = {
  /**
  * 正常需要定义关闭页面的方法，针对未传关闭方法的情景表示不需要，不展示关闭图标
  */
  onClose?: () => void; // 针对未传关闭方法的情景表示不需要，不展示关闭图标
  onNew?: () => void; // 针对未传开启新绘画方法的情景表示不需要，不展示新绘画图标
  onHistory?: () => void; // 针对未传历史会话方法的情景表示不需要，不展示历史会话图标
  showLogo?: boolean; // 是否展示logo
  logoSrc?: string, // 表示传入logo图标的地址
  title?: string; // 头部标题，未传不展示标题
  isNewConversation?: boolean; // 当前是否为新会话
};

export const Navbar: React.FC<NavbarProps> = (props) => {
  // 用来区分手机端、pc端
  const isMobile = canUse('touch');
  // const isMobile = false;
  const { onHistory, onNew, onClose, showLogo, title, logoSrc, isNewConversation = false } = props;

  const handleNewClick = () => {
    if (onNew) {
      onNew();
    }
  }


  if (isMobile) {
    return (
      <div className="NavBarApp">
        <div className="NavBar">
          <div className='NavbarTitle'>
            {title ?
              <>{showLogo && <img src={logoSrc || deepseekChat} className="Logo" style={{ marginRight: 14 }} />}<div>{title}</div></>
              :
              <img src={AppBack} className="appBack" onClick={onClose} />
            }
          </div>
          <div className="CenterLogo">{showLogo && !title && <img src={logoSrc || deepseekChat} className="Logo" />}</div>
          <div>
            {onHistory && <img src={HistoryIcon} className="history" onClick={onHistory} />}
            {onNew && <img src={NewChatIcon} className="new" onClick={handleNewClick} />}
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="NavBarPc">
      <div className="NavBar">
        <div className='NavbarTitle'>
          {title ?
            <>{showLogo && <img src={logoSrc || deepseekChat} className="Logo" style={{ marginRight: 14 }} />}<div>{title || '历史会话记录'}</div></>
            :
            ''
          }
        </div>
        <div>
          <Tooltip content='查看历史会话' placement='bottomLeft' >
            {onHistory && <img src={HistoryIcon} className="history" onClick={onHistory} />}
          </Tooltip>

          <Tooltip content={isNewConversation ? '当前已是新对话' : '开启新对话'} placement='bottomRight' >
            {onNew && <img src={NewChatIcon} className="new" onClick={handleNewClick} />}
          </Tooltip>

          {onClose && <img src={closeIcon} className="close" onClick={onClose} />}
        </div>
      </div>
    </div>
  );
};

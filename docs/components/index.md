---
nav:
  title: 组件库
  order: -1
group:
  title: 集成对话组件
  order: 1
order: 1
---

# AiChat 窄屏对话组件

## 示例:对接标准协议

<code src="./basic/index.tsx" ></code>

<!-- ```tsx
import { AiChat } from './AiChat.tsx';

export default () => <AiChat />; -->

## 参数参考

### AiChatProps

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 通用全局配置，接口请求，logo，bridge方法等 | [ConfigProps](/components#configprops) | - |
| navbar | 头部导航配置 | [NavbarProps](/components#navbarprops) | - |
| renderNavbar | 自定义渲染导航栏方法 | `() => ReactJSX.Element \| React.Node` | - |
| welcome | 欢迎页配置 | [WelcomeProps](/components#welcomeprops)  | - |
| renderWelcome | 自定义渲染欢迎页方法 | `() => ReactJSX.Element \| React.Node` | - |
| loadMoreText | 下拉刷新加载文本 | `string` | `查看历史消息` |
| initialMessages | 初始化消息数组，展示于新开会话页面的初始化消息，元素是多种消息的组合 | `[{type：any,content:string}]` | - |
| renderMessageContent | 自定义消息内容渲染函数，用于自定义卡片消息 | `() => ReactJSX.Element \| React.Node` | - |
| messageContainerConfig | 消息区域配置项 | [MessageContainerConfig](/components#messagecontainerconfig) | - |
| quickReplies | 初始化快捷问题区域，数组格式,位于输入框上方，默认不配置 | [QuickRepliesProps](/components#quickrepliesprops) | - |
| renderQuickReplies | 自定义渲染快捷问题区域 | `() => ReactJSX.Element \| React.Node` | - |
| onQuickReplyClick | 快捷问题点击回调 | `(item: QuickReplyItemProps, index: number) => void` | - |
| onQuickReplyScroll | 快捷问题滚动回调 | `() => void` | - |
| renderComposer | 自定义渲染输入框区域 | `() => ReactJSX.Element \| React.Node` | - |
| composerConfig | 输入框区域配置项 | [ComposerConfig](/components#composerconfigprops) | - |
| backBottomButton | 回到底部按钮 | `{icon: string}` | - |
| historyConversation | 历史会话区域 | [HistoryConversationProps](/components#historyconversationprops) | - |
| renderHistoryConversation | 自定义渲染历史会话区域 | `() => ReactJSX.Element \| React.Node` | - |
| renderFooterVersion | 自定义渲染输入框底部区域,默认不展示 | `() => ReactJSX.Element \| React.Node` | - |
| onReportLog | 埋点上报方法 | `(payload:` [ILogParams](/guide/log-intro)`) => void` | - |
| showPushHistory | 是否呈现历史会话列表推出的状态 | `boolean` | `true` |
| showToken | 是否在会话结束展示大模型消耗token及时间区域 | `boolean` | `true` |
| showHallucination | 是否在会话结束展示合规话术 | `boolean` | `true` |
| showFeedbackModal | 点赞点踩是否展示反馈区域 | `boolean` | `true` |
| feedbackModalConfig | 点赞点踩反馈区域配置，配置相关文案等 | `object` | - |
| renderStopAnswer | 自定义停止生成区域 | `(message: MessageProps) => ReactJSX.Element` | `已停止生成` |
| customThinkingStyle | 支持自定义“正在思考中”的样式 | `ReactNode` | - |
| customDeepThinkingStyle | 支持自定义“深度思考中”的样式 |`ReactNode` | - | 
| hallucinationText | 支持自定义“幻觉标识文案” |`string` | - | 

### ConfigProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| appId | 系统id：表示当前系统名称 | `string` | - |
| userId | 用户id：代表当前系统唯一用户id | `string` | - |
| isDev | 是否开发环境，用于魔方测试环境验证，可选 | `boolean` | - |
| lowCode | 魔方卡片配置，可选 | [LowCodeProps](/components#lowcodeprops) | - |
| requests | 接口请求配置 | [RequestsProps](/components#requestsprops) | - |
| bridge | 支持春入打开页面的方法和文件预览方法，可选 | [BridgeProps](/components#bridgeprops) | - |
| waterMark | 会话页面底部水印配置项，默认不展示，可选 | [WaterMarkProps](/components#watermarkprops) | - |
| robot | 全局logo替换，可选 | [RobotProps](/components#robotprops) | - |

#### LowCodeProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| rootValue | 移动端rem适配参数，如'37.5'，可选 | `string` | - |
| devicePixelRatio | 用于魔方echarts图表适配，转换系数，移动端根据屏幕分辨率计算，可选 | `string` | - |
| requestMobile | 走tcp请求，针对页面搭建里配置的数据源接口，不同app传入不同枚举值，可选 | `string` | - |
| requestHandlerType | 走tcp请求，针对魔方平台getAppById和getSchemaById方法，可选 | `string` | - |
| host | 魔方卡片配置，系统来源，可选 | `string` | - |

#### RequestsProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| platform | 仅走内置hiagent才需要传'hiagent' | `string` | - |
| baseUrl | 基础URL前缀 | `string` | - |
| init | 初始引导创建会话接口 | [UrlProps](/components#urlprops) | - |
| send | 问答接口 | [UrlProps](/components/urlprops) | - |
| historyDetail | 查询历史详情接口 | [UrlProps](/components/urlprops) | - |
| historyList | 历史会话列表接口 | [UrlProps](/components/urlprops) | - |
| score | 点赞点踩接口 | [UrlProps](/components/urlprops) | - |

##### UrlProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| type | 请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http | `string` | http |
| action | 移动端如果走tcp，需要传接口请求action号 | `string` | - |
| url | 接口路径 | `string` | - |
| useHttp | 是否走http请求 | `boolean` | - |
| method | - | `string` | post |
| requestTransfer | 请求转换函数回调,支持同步和异步方法 | `(e) => void(e)` | - |
| responseTransfer | 响应转换函数回调 | `(e) => void(e)` | - |
| stream | 是否sse流式输出，问答接口需传该字段 | `boolean` | - |
| isAibag | 可选，是否走aibag链路，问答接口涉及该字段，默认false | `boolean` | - |
| needUpdateConversation | 可选，是否需要调用会话更新接口，比如hiAgent场景需要调用则传true，默认false | `boolean` | - |
| updateConversationUrl | 可选，更新会话接口的url，如果是直接使用hiAgent接口则可以不传 | `string` | - |


#### BridgeProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| openWebPage | 打开页面方法 | `(e) => void(e)` | - |
| openNativeViews | 文件预览方法 | `(e) => void(e)` | - |
| copyText | 复制文本方法 | `(e) => void(e)` | - |

#### WaterMarkProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| show | 是否展示水印 | `boolean` | - |
| text | 水印内容 | `string` | - |

#### RobotProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| logo | logo图标地址 | `string` | - |
| showMsgLogo | 是否展示模型回答消息logo | `boolean` | - |

### NavbarProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| open | 是否展示navbar | `boolean` | true |
| showLogo | 是否展示logo | `boolean` | true |
| logo | logo图标的地址 | `string` | - |
| title | 头部标题，未传不展示标题 | `string` | - |
| onReturnButtonClick | 点击返回按钮响应处理函数 | `() => void` | - |
| onCloseButtonClick | 关闭按钮点击响应处理函数 | `() => void` | - |

### WelcomeProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| open | 是否展示欢迎页 | `boolean` | true |
| logo | logo图标的地址 | `string` | - |
| title | 欢迎页主标题 | `string` | 智能助手为您服务~ |
| subtitle | 欢迎页副标题 | `string` | 作为您的智能助手，我可以为您答疑解惑 |
| riskTip | 欢迎页底部风险提示 | `string` | 内容由AI大模型生成，请谨慎识别 |

### MessageContainerConfig
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| needFeedback | 是否展示反馈区域 | `boolean` | true |
| robotAvatar | 助手消息头像，如果配置则在助手回复的消息左侧展示头像 | `string` | - |
| userAvatar | 用户消息头像，如果配置则在用户问题消息右侧展示头像 | `string` | - |
| showThinking | 是否展示深度思考过程效果 | `boolean` | - |
| thinkingText | 思考中文案 | `string` | - |
| thinkingFinishText | 已完成思考文案 | `string` | - |
| feedbackBadReason | 是否展示点踩原因，如果true则会弹框支持输入原因 | `boolean` | - |
| showMessageBottomTip | 是否展示消息底部提示文案 | `boolean` | - |
| messageBottomTip | 消息底部提示文案配置 | `string` | - |
| renderMessageBottomTip | 自定义消息底部提示组件 | `() => ReactJSX.Element \| React.Node` | - |
| renderFeedback | 自定义消息反馈区域 | `() => ReactJSX.Element \| React.Node` | - |

### QuickRepliesProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| title | 页面展示的快捷问题 | `string` | - |
| content | 点击后实际发送的问题 | `string` | - |
| url | 如果配置，点击后跳转链接 | `string` | - |

### ComposerConfigProps
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| uploadConfig | 上传配置项 | `uploadConfigProps` | - |
| showInternetSearch | 是否展示联网搜索 | `boolean` | - |
| text | 输入框默认值 | `string` | - |
| placeholder | 输入框默认placeholder文案 | `string` | - |
| aboveNode | 输入框上方插槽 | `() => ReactJSX.Element \| React.Node` | - |
| belowNode | 输入框下方插槽 | `() => ReactJSX.Element \| React.Node` | - |

### HistoryConversationProps
⚠️ 注意：由于集成对话组件内部对历史会话组件的点击事件等属性做了封装，在AiChat与WideAiChat组件中可配置的参数与独立使用该组件时不同。独立使用时，参数配置请查看：[HistoryConversationProps](/components/history-conversation#historyconversationprops)。


| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| pushPosition | 推入方向（左/右） | `'left' \| 'right'` | right |
| pushPercent | 推入宽度百分比（50-100） | `number` | 75 |
| title | 历史会话标题 | `string` | - |
| logo | 历史会话图标 | `string` | - |
| renderTitle | 支持自定义标题区域 | `() => ReactJSX.Element \| React.Node` | - |
| showSearch | 是否展示搜索区域 | `boolean` | `true` |
| renderBrand | 支持传入自定义brand区域 | `() => ReactJSX.Element \| React.Node` | - |
| renderBottom | 支持传入自定义底部区域 | `() => ReactJSX.Element \| React.Node` | - |

---

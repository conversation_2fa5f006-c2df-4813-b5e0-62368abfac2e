/*
 * @Author: 020446 <EMAIL>
 * @Date: 2025-08-11 15:53:58
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-08-13 10:57:10
 */
import React, { useRef } from 'react';
import deleteIcon from './images/delete.svg';
import editIcon from './images/edit.svg';
import { DeleteModal } from '../Modal';
import { ConversationItemProps } from './interface';

interface DropdownMenuProps {
  isOpen: boolean;
  conversationItem: ConversationItemProps;
  showDeleteOption: boolean;
  showRenameOption: boolean;
  onClose: () => void;
  onRename?: (conversationItem: ConversationItemProps) => void;
  onDelete?: (conversationItem: ConversationItemProps) => void;
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({
  isOpen,
  conversationItem,
  onClose,
  showRenameOption = true,
  showDeleteOption = true,
  onRename,
  onDelete
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  // const [showRenameModal, setShowRenameModal] = React.useState(false);

  // useEffect(() => {
    // const handleClickOutside = (event: MouseEvent) => {
    //   if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
    //     onClose();
    //   }
    // };

    // if (isOpen) {
    //   document.addEventListener('mousedown', handleClickOutside);
    // }

    // return () => {
    //   document.removeEventListener('mousedown', handleClickOutside);
    // };
  // }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleRename = () => {
    console.log('重命名操作');
    return onRename?.(conversationItem);
  };

  const handleDelete = () => {
   if (onDelete) {
      console.log('调用 onDelete 回调函数');
      onDelete(conversationItem);
    } else {
      console.error('onDelete 回调函数不存在');
    }
    
    // 关闭菜单
    return onClose();
  };

  return (
    <div>
      <div
        ref={menuRef}
        className="menuItemPopover"
      >
        {showRenameOption && <div
          onClick={handleRename}
          className="menuItem"
        >
          <img src={editIcon} className="menuItemIcon" />
          重命名
        </div>}
        {showDeleteOption &&<div
          onClick={() => setShowDeleteModal(true)}
          className="menuItem menuItemRed"
        >
          <img src={deleteIcon} className="menuItemIcon" />
          删除
        </div>}
      </div>
      {showDeleteOption && (
        <DeleteModal
          visible={showDeleteModal}
          title="确认删除？"
          content={'该对话内容江北删除无法恢复，若您之前主动分享过该对话，分享链接也将一并被删除'}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleDelete}
        />
      )}
    </div>
   
  );
};

export default DropdownMenu;



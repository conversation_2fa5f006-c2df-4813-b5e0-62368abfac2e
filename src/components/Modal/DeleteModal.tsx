/*
 * @Author: 020446 <EMAIL>
 * @Date: 2025-08-12 15:38:11
 * @LastEditors: 020446 <EMAIL>
 * @LastEditTime: 2025-08-12 16:12:57
 */
import React from 'react';
import { Modal } from './Modal';
import ErrorIcon from './images/error.svg';
import { Button } from '../Button';

interface DeleteModalProps {
  visible: boolean;
  title?: string | React.ReactNode;
  content: string | React.ReactNode;
  onClose: () => void;
  onConfirm: () => void;
};

export const DeleteModal = (props: DeleteModalProps) => {
  return (
    <Modal
      active={props?.visible}
      title={
        <div className="Delete-modal-title">
          <img src={ErrorIcon} className="Delete-modal-title-icon" />
          <span>{props.title ?? '确认删除'}</span>
        </div>
      }
      showClose={true}
      onClose={props?.onClose}
      className="Delete-modal"
    >
      <div className="Delete-modal-content">
        {props.content ?? ''}
      </div>
      <div className="Delete-modal-footer">
        <Button onClick={props?.onClose}>取消</Button>
        <Button color="primary" onClick={props?.onConfirm}>确认删除</Button>
      </div>
    </Modal>
  );
}
/* eslint-disable no-underscore-dangle */
import { useState, useMemo, useRef, useCallback } from 'react';
import { getRandomString } from '../utils';
import { MessageProps, MessageId, MessageWithoutId } from '../components/Message';

type Messages = MessageProps[];

const TIME_GAP = 5 * 60 * 1000;
let lastTs = 0;
// let historyMsgTs = 0;

const makeMsg = (msg: MessageWithoutId, id?: MessageId) => {
  const ts = msg.createdAt || Date.now();
  //  ts - lastTs < 0标识是历史消息
  const hasTime = msg.hasTime || ts - lastTs < 0  || ts - lastTs > TIME_GAP;
  
  // 当前消息的时间戳大于上次记录的时间才更新lastTs，避免对历史消息做点赞点踩操作时更新为旧消息时间
  if (hasTime && ts - lastTs > 0) {
    lastTs = ts;
  }

  return {
    ...msg,
    _id: msg._id || id || getRandomString(),
    createdAt: ts,
    position: msg.position || 'left',
    hasTime,
  };
};

// 历史消息是否展示时间，根据上一条消息的时间戳和当前消息的时间戳差值是否大于5分钟来决定
// const makeHistoryMsg = (msg: MessageWithoutId, prevMsgs?: Messages) => {
//   const lastMsgTs = prevMsgs?.[prevMsgs?.length -1]?.createdAt || Date.now();
//   const ts = msg?.createdAt || Date.now();
//   const hasTime = msg.hasTime || ts - (historyMsgTs || lastMsgTs || 0)  < -TIME_GAP;

//   if (hasTime) {
//     historyMsgTs = ts;
//   }

//   return {
//     ...msg,
//     _id: msg._id || getRandomString(),
//     createdAt: ts,
//     position: msg.position || 'left',
//     hasTime,
//   };
// };

const TYPING_ID = '_TYPING_';

export default function useMessages(initialState: MessageWithoutId[] = []) {
  const initialMsgs: Messages = useMemo(() => initialState.map((t) => makeMsg(t)), [initialState]);
  const [messages, setMessages] = useState(initialMsgs);
  const isTypingRef = useRef(false);
  const messagesRef = useRef(initialMsgs); // 使用ref再存一下是为了在异步接口里面也能拿到最新的messages

  const prependMsgs = useCallback((msgs: Messages) => {
    setMessages((prev: Messages) => {
      const newMsgs = [...msgs, ...prev];
      messagesRef.current = newMsgs;
      return newMsgs;
    });
  }, []);

  const updateMsg = useCallback((id: MessageId, msg: MessageWithoutId) => {
    setMessages((prev) => {
      const newMsgs = prev.map((t) => (t._id === id ? makeMsg({...t, ...msg}, id) : t))
      messagesRef.current = newMsgs;
      return newMsgs;
    });
  }, []);

  const appendMsg = useCallback(
    (msg: MessageWithoutId) => {
      const newMsg = makeMsg(msg);
      if (isTypingRef.current) {
        isTypingRef.current = false;
        updateMsg(TYPING_ID, newMsg);
      } else {
        setMessages((prev) => {
          const newMsgs = [...prev, newMsg];
          messagesRef.current = newMsgs;
          return newMsgs;
        });
      }
    },
    [updateMsg],
  );

  const deleteMsg = useCallback((id: MessageId) => {
    setMessages((prev) => {
      const newMsgs = prev.filter((t) => t._id !== id)
      messagesRef.current = newMsgs;
      return newMsgs;
    });
  }, []);

  const resetList = useCallback((list = []) => {
    messagesRef.current = list;
    setMessages(list);
    // 如果是清空所有消息，则正在输入的状态也应结束
    if (list.length === 0) {
      isTypingRef.current = false;
    }
  }, []);

  const setTyping = useCallback(
    (typing: boolean) => {
      if (typing === isTypingRef.current) return;

      if (typing) {
        appendMsg({
          _id: TYPING_ID,
          type: 'typing',
        });
      } else {
        deleteMsg(TYPING_ID);
      }
      isTypingRef.current = typing;
    },
    [appendMsg, deleteMsg],
  );

  const getTyping = useCallback(() => isTypingRef.current, []);

  const getMessages = useCallback(() => messagesRef.current, []);

  return {
    messages,
    prependMsgs,
    appendMsg,
    updateMsg,
    deleteMsg,
    resetList,
    setTyping,
    getTyping,
    getMessages,
  };
}

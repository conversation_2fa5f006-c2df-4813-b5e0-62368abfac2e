.<PERSON><PERSON><PERSON>iew {
  overflow: hidden;

  &-scroller {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: none; // IE/Edge
    scrollbar-width: none; // FF

    &::-webkit-scrollbar {
      display: none;
    }
  }
  &--fullWidth {
    margin: 0 calc(@gutter * -1);
  }
  &--fullWidth:not(&--hasControls) &-inner {
    padding: 0 @gutter;
  }
}

.ScrollView--x {
  .ScrollView-scroller {
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    margin-bottom: -18px;
    padding-bottom: 18px;
  }
  .ScrollView-inner {
    display: flex;
  }
  .ScrollView-item {
    flex: 0 0 auto;
    margin-left: @scroll-view-spacing-x;

    &:first-child {
      margin-left: 0;
    }
  }
}

.ScrollView--hasControls {
  display: flex;
  align-items: center;

  .ScrollView-scroller {
    flex: 1;
  }
}

.ScrollView-control {
  padding: 6px;
  color: @gray-3;
  font-size: @font-size-md;
  line-height: 16px;
  border-radius: 3px;

  img {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
  
  &:not(:disabled):hover {
    color: #3e74f7;
  }
}

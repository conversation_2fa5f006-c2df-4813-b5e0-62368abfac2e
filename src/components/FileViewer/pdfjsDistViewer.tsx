/* 这是用pdfjs-dist插件预览的 */
/* eslint-disable import/extensions */
import React, { useEffect, useRef, useState } from "react";
// import * as pdf from 'pdfjs-dist';
// import pdfWorker from 'pdfjs-dist/build/pdf.worker.js?url';
import CloseIcon from '../Navbar/images/closeIcon.svg';

interface PdfjsDistViewerProps {
  fileUrl: string;
  handleClose: () => void;
}

// pdf.GlobalWorkerOptions.workerSrc = pdfWorker;
// pdf.GlobalWorkerOptions.workerSrc = '/pdf.worker.js'

const PdfjsDistViewer: React.FC<PdfjsDistViewerProps> = (props) => {

  const usePDFData = (options: { src: string, scale?: number }) => {
    const previewUrls = useRef<string[]>([])
    const urls = useRef<string[]>([])
    const [loading, setLoading] = useState(true)

    useEffect(() => {
      urls.current = []
      setLoading(true);
      // (async () => {
      //   // 这里千万别解构，会导致 this 指向错误
      //   try {
      //     const pdfDocument = await pdf.getDocument(options.src).promise
      //     const task = new Array(pdfDocument.numPages).fill(null)
      //     await Promise.all(task.map(async (_, i) => {
      //       const page = await pdfDocument.getPage(i + 1)
      //       const viewport = page.getViewport({ scale: options.scale || 2 })
      //       const canvas = document.createElement('canvas')

      //       canvas.width = viewport.width
      //       canvas.height = viewport.height
      //       const ctx = canvas.getContext("2d") as CanvasRenderingContext2D
      //       const renderTask = page.render({
      //         canvasContext: ctx,
      //         viewport,
      //       });
      //       await renderTask.promise;
      //       // 分别获取不同尺寸的图片，一个用来预览一个用来展示
      //       urls.current[i] = canvas.toDataURL('image/jpeg', 2)
      //       previewUrls.current[i] = canvas.toDataURL('image/jpeg', 0.5)
      //     }))
      //     setLoading(false)
      //   } catch (error) {
      //     console.error("加载 PDF 文件失败:", error);
      //   }

      // })()
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [options.src])

    return {
      loading,
      urls: urls.current,
      previewUrls: previewUrls.current,
    }
  }

  const { fileUrl, handleClose } = props;
  const { loading, urls } = usePDFData({
    src: fileUrl
  })

  if (loading) {
    return <div className="WordViewWrap">
      <div className='closeWrap'>
        <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
      </div>
      <div >loading...</div>
    </div>
  }
  return (
    <div className="WordViewWrap">
      <div className='closeWrap'>
        <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
      </div>
      <div className="PdfjsDistViewerContent" >
        {urls.map((item, i) => (
          <React.Fragment key={item}>
            <img
              src={item}
              style={{
                marginTop: 20,
                width: '100%',
                cursor: 'pointer',
              }}
            />
            <span style={{
              background: 'transparent',
              fontSize: 14,
              backgroundColor: '#fff'
            }}>{i + 1}</span>
          </React.Fragment>
        ))}
      </div>
    </div>

  )
}

export default PdfjsDistViewer

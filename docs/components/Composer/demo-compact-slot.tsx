import React from 'react';
import { Composer } from '@ht/chatui';

export default () => {
  return (
    <Composer
      inputType="text"
      aboveNode={
        <div
          style={{
            background: '#FCEBEB',
            color: '#E33C39',
            borderRadius: '20px',
            padding: '7px 10px',
            fontSize: 12,
            display: 'inline-block',
          }}
        >
          您输入的内容包含敏感信息
        </div>
      }
      belowNode={
        <div
          style={{
            background: '#CFE2FF',
            color: '#3E74F7',
            borderRadius: '20px',
            padding: '7px 10px',
            fontSize: 12,
            display: 'inline-block',
          }}
        >
          您输入的内容包含敏感信息
        </div>
      }
    ></Composer>
  );
};

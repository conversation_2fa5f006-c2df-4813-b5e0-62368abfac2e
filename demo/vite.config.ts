/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-06-11 14:30:09
 */
/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  plugins: [
    react(),
    nodePolyfills({
      protocolImports: true, // 启用 `node:` 协议支持（可选）
    }),],
  resolve: {
    alias: {
      events: 'events',
    },
  },
  server: {
    proxy: {
      // '/hiAgent/chat_query': {
      //   // target: 'http://************:6789/api/v1',
      //   target: 'http://*************:9607/chat/llmpf/api/proxy/api/v1',
      //   // target: 'http://*************:8080',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/hiAgent/, '')
      // },
      '/aichrome/chat': {
        // target: 'http://************:6789/api/v1',
        // target: 'http://*************:3000/llmpf/api/proxy/api/v1/',
        // target: 'http://webassist.sit.saas.htsc/',
        target: 'http://*************:9607/',
        // target: 'http://*************:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/aichrome/, '')
      },
      '/aichrome': {
        // target: 'http://************:6789/api/v1',
        // target: 'http://*************:3000/llmpf/api/proxy/api/v1/',
        target: 'http://*************:9607/',
        // target: 'http://*************:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/aichrome/, '')
      },
      '/hiAgent': {
        // target: 'http://************:6789/api/v1',
        // target: 'http://*************:3000/llmpf/api/proxy/api/v1/',
        target: 'http://hiagenthub.sit.saas.htsc/llmpf/api/proxy/api/v1/',
        // target: 'http://*************:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/hiAgent/, '')
      },
      '/llmpf': {
        // target: 'http://************:6789/api/v1',
        target: 'http://*************:3000',
        // target: 'http://*************:8080',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/hiAgent/, '')
      },

      // '/api': {
      //   target: 'http://mock.htsc',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/api/, '')
      // },
      '/airobot/api/groovy/ai/adapter': {
        // target: 'http://168.63.127.233:8082',
        target: 'http://10.102.74.110:8082',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/airobot/, '')
      },
      '/goapi': {
        target: 'http://mock.htsc',
        changeOrigin: true,
      },
      '/mcrm/api': {
        target: 'http://168.61.125.127:9082',
        changeOrigin: true,
      },
      '/api': {
        target: 'http://168.61.90.156:8080',
        // target: 'http://aorta.saasuat.htsc.com.cn:8090',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/tec/fusion/api': {
        target: 'http://168.63.117.2:9011',
        // target: ' http://168.61.127.58:8082',
        changeOrigin: true,
      },
      '/aorta/operation/api': {
        // 染色环境
        // target: 'http://10.102.76.215:8080',
        // 61环境
        // target: 'http://168.61.90.113:8080',
        // 63环境
        target: 'http://*************:8080',
        changeOrigin: true,
      },
      '/qingyunAi': {
        target: 'http://168.63.25.3',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/qingyunAi/, '')
      },
      // '/webassist': {
      //   target: 'http://webassist.sit.sass.htsc',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/webassist/, '')
      // },
      '/webassist': {
        target: 'http://168.64.33.184:80',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/webassist/, '/web-assistant')
      },
    }
  },
  build: {
    outDir: '../dist/demo',
    rollupOptions: {
      output: {
        entryFileNames: `[name].js`,
        chunkFileNames: `[name].js`,
        assetFileNames: `[name].[ext]`,
      },
    },
  },
});

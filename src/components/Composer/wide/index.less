.WideComposer {
  display: flex;
  padding: 12px 16px;
  position: relative;
  background-color: #fff;
  border-radius: @composer-wide-border-radius;
  border-style: solid;
  border-width: @composer-wide-border-width;
  border-color: @composer-wide-border-color;
  flex-direction: column;

  &-inputWrap {
  }

  &-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    column-gap: 12px;
  }
  &-left {
    display: flex;
  }

  &-sendWrap {
    display: flex;
    align-items: center;
    margin-left: 10px;

    span {
      line-height: 1;
      vertical-align: middle;
    }
  }

  &-SendButton {
    width: @composer-wide-send-size;
    height: @composer-wide-send-size;
    background: @composer-wide-send-bg;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 15px;

    &.disabled {
      background: @composer-wide-send-disable-bg;
      border-radius: 2px;
      border: @composer-wide-send-disable-border;
    }
  }
  &-StopAnswer {
    &-inner {
      width: 10px;
      height: 10px;
      background: #ffffff;
      border-radius: 2px;
    }
  }

  &-InternetSearch {
    cursor: pointer;
    height: @composer-wide-net-search-height;
    border-radius: @composer-wide-net-search-border-radius;
    border: @composer-wide-net-search-border;
    padding: 0 10px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: @composer-wide-net-search-color;
    cursor: pointer;

    img {
      width: 16px;
      height: 16px;
      margin-right: 3px;
    }
  }

  &-InternetSearch-active {
    border: 1px solid @composer-wide-net-search-active-color;
    color: @composer-wide-net-search-active-color;
  }

  &-attachments {
    &-container {
      margin-bottom: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      &-empty {
        margin-bottom: 0;
      }
    }

    &-filecard {
      display: inline-block;
      // height: 44px;
      display: inline-flex;
      align-items: center;
      background: #F6F9FF;
      border-radius: 2px;
      gap: 14px;
      padding: 12px 10px;
      &.error {
        background: rgba(255,62,62,0.1);;
        color: #FF3E3E;
      }
      &.error > &-remove {
        color: #FF3E3E;
      }
      &.error > &-name {
        color: #FF3E3E;
      }
    }
    &-filecard-remove {
      width: 14px;
      height: 14px;
      color: #7c8db5;
      cursor: pointer;
    }
    &-filecard-name {
      font-size: 14px;
      font-weight: 400;
      color: #25396f;
      cursor: pointer;
    }
  }
}

@import './select.less';
@import './popover.less';

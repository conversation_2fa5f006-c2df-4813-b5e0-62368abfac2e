/*
 * @Author: 016645
 * @Description: 获取会话列表
 * @Date: 2025-04-02 18:58:50
 */
import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button } from '../../../src';
import { useGetConversationList } from '../../../src/hooks/aiAgentChat/index';

export default () => {
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useGetConversationList({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      appId: 'aorta',
      userId: '002332',
    },
  });

  // const { data: conversationData2, loading: conversationLoading2, run: conversationRun2 } = useGetConversationList({
  //   platform: 'hiAgent',
  //   baseUrl: '/hiAgent',
  //   userId: '016645',
  //   manual: true,
  //   type: 'http',
  // });

  return (
    <DemoPage>
      <DemoSection title="自定义服务">
        <Button onClick={() => conversationRun()} loading={conversationLoading}>获取会话列表</Button>
        <div>
          {conversationData?.resultData?.list?.map((item, index) => (
            <div key={index}>{item.conversationId}: {item.question}</div>
          ))}
        </div>
      </DemoSection>

      {/* <DemoSection title="HiAgent">
        <Button onClick={() => conversationRun2()} loading={conversationLoading2}>获取会话列表</Button>
      </DemoSection>
      <div>
        <span>{conversationData2?.resultData?.conversationId}</span>
      </div> */}
    </DemoPage>
  )
};

---
nav:
  title: 组件库
  order: -1
group:
  title: 基础卡片组件
  order: 10
order: 1
---

# Video 音视频卡片

## 代码演示

### 基本

<code src="./demo-basic.tsx" ></code>


## 参数

### Video

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| className | 类名 | `string` | - |
| src | 音视频地址（必填） | `string` | - |
| duration | 时长（必填） | `string \| number` | - |
| cover | 封面地址 | `string` | - |
| style | 样式 | `object` | - |
| videoRef | ref | `React.RefObject<HTMLVideoElement>` | - |
| onClick | 点击回调 | `(paused: boolean, event: React.MouseEvent) => void` | - |
| onCoverLoad | 封面加载完成后回调 | `(event: React.SyntheticEvent<HTMLImageElement, Event>) => void` | - |
---

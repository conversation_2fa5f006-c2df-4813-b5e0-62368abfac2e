import React, { useState, useRef, useEffect, useImperativeHandle, useCallback } from 'react';
import clsx from 'clsx';
import Select, { Option as SelectOption } from './wide/Select';
import { Recorder } from '../Recorder';
import { Toolbar, ToolbarItemProps } from '../Toolbar';
import { AccessoryWrap } from './AccessoryWrap';

// import { Popover } from '../Popover';
// import { ToolbarItem } from './ToolbarItem';
import { ComposerInput } from './ComposerInput';
import { SendButton } from './compact/SendButton';
import { SendButton as WideSendButton } from './wide/SendButton';
import { Action } from './Action';
import toggleClass from '../../utils/toggleClass';
import { isMobile } from '../../utils/canUse';
import NetSearchIcon from './NetSearchIcon';
import AttachmentIcon from './AttachmentIcon';
import { Icon } from '../Icon';
import NewChatIcon from '../Navbar/images/newChatIcon.svg';
import HistoryIcon from '../Navbar/images/historyIcon.svg';
import Upload, { UploadFile } from './wide/Upload';
import Popover from './wide/Popover';
import { LogPointConfigMap } from '../../LogPointConfigMap';

import type { InputType as InputCardType, InputContent, Operation, ComposerType, ComposerCustomProps, InputCard, InputFile} from "./components/type"

import ThinkButton from './components/ThinkButton';
import Skills from './components/Skills';
import TextComposer from './components/Composer/TextComposer';
import WebComposer from './components/Composer/WebComposer';
import TranslateComposer from './components/Composer/TranslateComposer';
import InputCardList from "./components/InputCardList"
import { ComposerHandleProps, ComposerProps } from './interface';
export const CLASS_NAME_FOCUSING = 'S--focusing';

export * from './wide/Upload';

const transFileType = (type: string) => type?.split('/')?.[0] === 'image' ? 'image' : 'file';
  
const ComposerC = React.forwardRef<ComposerHandleProps, ComposerProps>((props, ref) => {
  const {
    text: initialText = '',
    inputType: initialInputType = 'text',
    // wideBreakpoint,
    placeholder = '请输入您的问题',
    recorder = {},
    onInputTypeChange,
    onFocus,
    onBlur,
    onChange,
    onSend,
    onImageSend,
    onAccessoryToggle,
    toolbar = [],
    onToolbarClick,
    extraAction,
    inputOptions,
    showStopAnswer,
    onStopAnswer,
    aboveNode,
    quickNewConversation = false,
    quickOpenHistory = false,
    handleNewConversation,
    toggleHistory,
    belowNode,
    llmConfig,
    uploadConfig,
    showInternetSearch = false,
    enableInternetSearch = true,
    toggleEnableInternetSearch,
    isWide = false,
    onConsolidateReports,
    skills,
    operationConfig,
    showThinking,
  } = props;

  const [text, setText] = useState(initialText);
  const [inputType, setInputType] = useState(initialInputType || 'text');
  const [isAccessoryOpen, setAccessoryOpen] = useState(false);
  const [accessoryContent, setAccessoryContent] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null!);
  const focused = useRef(false);
  const blurTimer = useRef<any>();
  const popoverTarget = useRef<any>();
  const isMountRef = useRef(false);

  const showLLMSwitch = !!llmConfig;
  const showUploadAttachment = !!uploadConfig;
  const isShowExtraAction = showInternetSearch || showLLMSwitch;

  const [fileList, setFileList] = useState<UploadFile[]>([]); //文件列表

  const prefixCls = 'WideComposer';


  const [isReasoningLLM, setIsReasoningLLM] = useState<boolean>(llmConfig?.llmOptions && llmConfig?.llmOptions[0]?.reasoning || false );
  const [composerType, setComposerType] = useState<ComposerType>('common');
  const [composerCustomProps, setComposerCustomProps] = useState<ComposerCustomProps>({ title:''})
  const [inputCardList, setInputCardList] = useState<InputCard[]>([]);

  const setCite = (type: InputCardType, content: InputContent)=>{
    setInputCardList(old=>old.concat([{
      type: type,
      isCite: true,
      content: content,
    }]))
  }

  const setComposer = (type: ComposerType, composerProps: ComposerCustomProps) => {
    setComposerType(type);
    setComposerCustomProps(composerProps);
    setText('');
    setInputCardList([])
  }
 
  const onCustomComposerClose = ()=>{
    setComposerType('common');
    setComposerCustomProps({ title: '' });
    setText('');
    setInputCardList([])
  }

  useEffect(()=>{
    setFileList(inputCardList.filter(item=>item.isCite === false && item.type==='file' || item?.type === 'image').map(item=>(item.content as UploadFile)));
  },[inputCardList])

  useEffect(() => {
    toggleClass('S--wide', isWide);
    if (!isWide) {
      setAccessoryContent('');
    }
  }, [isWide]);

  useEffect(() => {
    if (isMountRef.current && onAccessoryToggle) {
      onAccessoryToggle(isAccessoryOpen);
    }
  }, [isAccessoryOpen, onAccessoryToggle]);

  useEffect(() => {
    isMountRef.current = true;
  }, []);

  useImperativeHandle(ref, () => ({
    setText,
    setComposer,
    setCite,
  }));

  const handleInputTypeChange = useCallback(() => {
    const isVoice = inputType === 'voice';
    const nextType = isVoice ? 'text' : 'voice';
    setInputType(nextType);

    if (isVoice) {
      const input = inputRef.current;
      input.focus();
      // eslint-disable-next-line no-multi-assign
      input.selectionStart = input.selectionEnd = input.value.length;
    }
    if (onInputTypeChange) {
      onInputTypeChange(nextType);
    }
  }, [inputType, onInputTypeChange]);

  const handleInputFocus = useCallback(
    (e: React.FocusEvent<HTMLTextAreaElement>) => {
      clearTimeout(blurTimer.current);
      if (isMobile) {
        toggleClass(CLASS_NAME_FOCUSING, true);
      }

      focused.current = true;

      if (onFocus) {
        onFocus(e);
      }
    },
    [onFocus],
  );

  const handleInputBlur = useCallback(
    (e: React.FocusEvent<HTMLTextAreaElement>) => {
      blurTimer.current = setTimeout(() => {
        if (isMobile) {
          toggleClass(CLASS_NAME_FOCUSING, false);
        }
        focused.current = false;
      }, 0);

      if (onBlur) {
        onBlur(e);
      }
    },
    [onBlur],
  );

  const innerSend = useCallback((otherOperations?: object) => {
    if (onSend) {
      // 使用自定义转换处理文件列表
      const responseTransform = props.uploadConfig?.responseTransform;

      // const transformedFiles = fileList.map((file) => {
      //   return {
      //     ...file,
      //     path: responseTransform?.(file.response, file) || file.response?.resultData?.Path,
      //   };
      // });

      // todo:这里需要根据需要拍平数据，或者在AIChat组件内去拍平
      const transformedFiles = inputCardList?.map((item: InputCard)=>{
        if(item.type === 'file'){
          const file = item.content as UploadFile;
          return {
            ...item,
            content:{
              ...file,
              path: responseTransform?.(file.response, file) || file.response?.resultData?.Path,
            }
          }
        }
        return item;
      })
      const agentId = composerCustomProps?.agentId; // 如operation传入其他agentId，以operation为准
      
      // @ts-ignore
      onSend('text', text, { agentId, ...otherOperations }, transformedFiles).then((sendSuccess) => {
        // 发送成功才清空输入框
        if (sendSuccess) {
          setText('');
          setInputCardList([]);
        }
      });
    }
    if (focused.current) {
      inputRef.current.focus();
    }

     // 埋点上报
     const params = LogPointConfigMap.get('Composer.send');
     if(params){
      onConsolidateReports?.(params);
     }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onSend, text, fileList, props.uploadConfig?.responseTransform]);


  const opConfig = React.useMemo(()=>{
    Object.keys(operationConfig).forEach(key=>{
      // @ts-ignore
      const opList : Operation[] = operationConfig[key] ;
      opList.forEach(item=>{
        item.onClick = () => {
          innerSend(item);
        }
      })
      
    })

    return operationConfig;
  },[ operationConfig, innerSend ])


  const handleInputKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!e.shiftKey && e.keyCode === 13) {
        if (!showStopAnswer && text) {
          innerSend();
        }
        e.preventDefault();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [innerSend, showStopAnswer],
  );

    const handleCustomInputKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!e.shiftKey && e.keyCode === 13) {
        if (text) {
          innerSend();
        }
        e.preventDefault();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [innerSend],
  );

  const handleTextChange = useCallback(
    (value: string, e: React.ChangeEvent) => {
      setText(value);

      if (onChange) {
        onChange(value, e);
      }
    },
    [onChange],
  );

  const handleAccessoryToggle = useCallback(() => {
    setAccessoryOpen(!isAccessoryOpen);
  }, [isAccessoryOpen]);

  const handleAccessoryBlur = useCallback(() => {
    setTimeout(() => {
      setAccessoryOpen(false);
      setAccessoryContent('');
    });
  }, []);

  const handleToolbarClick = useCallback(
    (item: ToolbarItemProps, e: React.MouseEvent) => {
      if (onToolbarClick) {
        onToolbarClick(item, e);
      }
      if (item.render) {
        popoverTarget.current = e.currentTarget;
        setAccessoryContent(item.render);
      }
    },
    [onToolbarClick],
  );

  const onLlmChange = useCallback(
    (value: string) => {

      llmConfig?.llmOptions?.forEach(item=>{
        if(item.value === value){
          setIsReasoningLLM(item.reasoning===undefined ? false : item.reasoning)
        }
      })

      if (llmConfig?.onLLMSwitch) {
        llmConfig.onLLMSwitch(value);
     
      }
    },
    [llmConfig],
  );

  const isInputText = inputType === 'text';
  const inputTypeIcon = isInputText ? 'volume-circle' : 'keyboard-circle';
  const hasToolbar = toolbar.length > 0;

  const inputProps = {
    ...inputOptions,
    value: text,
    inputRef,
    placeholder: ` ${placeholder}`,//IOS下光标会覆盖文案，，所以在前面拼个空格，增加点间距
    onFocus: handleInputFocus,
    onBlur: handleInputBlur,
    onKeyDown: handleInputKeyDown,
    onChange: handleTextChange,
    onImageSend,
  };

    const customInputProps = {
    ...inputOptions,
    placeholder: ` ${placeholder}`,//IOS下光标会覆盖文案，，所以在前面拼个空格，增加点间距
    onKeyDown: handleCustomInputKeyDown,
  };


  const getDownloadUrl = (file: any, key: string = 'url') => {
    return file?.response?.Result?.[key] || file?.response?.resultData?.fileId && uploadConfig?.downloadAction && `${uploadConfig?.downloadAction}/${file?.response?.resultData?.fileId}` || '';
  };


  const onUploadChange = (changeInfo: any)=>{
    const { fileList: newList, file } = changeInfo;
    const oldFileList = inputCardList.filter(item => item.isCite === false && item.type === 'file' || item?.type === 'image').map(item => (item.content as InputFile).uid);
    // @ts-ignore
    const addFileList :InputCard[] = newList.filter(item =>!oldFileList.includes(item.uid)).map(item=>{
      const transType = transFileType(item?.type);
      return {
        type: transType,
        isCite: false,
        content: {
          ...(item as InputFile),
          path: getDownloadUrl(item, 'Path'),
          url: getDownloadUrl(item),
        }
      }
    })
    if(addFileList.length > 0){
      setInputCardList(old=>{
        return old.concat(addFileList)
      })
    } else {
      const newInputCardList = inputCardList?.map(item => ((item.content as InputFile)?.uid === file?.uid
        ? {
          ...item,
          content: {
            ...file,
            type: transFileType(item?.type),
            path: getDownloadUrl(file, 'Path'),
            url: getDownloadUrl(file),
          }
        } : item));
      setInputCardList(newInputCardList);
    }


    if(uploadConfig?.onChange){
      uploadConfig?.onChange(changeInfo)
    }
  }

  if(composerType === 'text'){
    return <TextComposer {...customInputProps} {...composerCustomProps} onSend={innerSend} onclose={onCustomComposerClose} text={text} setText={setText} />
  }

  if(composerType === 'web'){
    return <WebComposer {...customInputProps} {...composerCustomProps} onSend={innerSend} onclose={onCustomComposerClose} text={text} setText={setText} inputCardList={inputCardList} setInputCardList={setInputCardList} onAddWeb={()=>{
      setInputCardList((old) => old.concat([{
        
          type: 'web',
          isCite: false,
          content: {
            title: 'Baidu',
            url: 'https://www.baidu.com/',
            favicon: 'https://www.baidu.com/favicon.ico',
          },
        
      }]));
    }}/>
  }

  if(composerType === 'translate'){
    return <TranslateComposer {...customInputProps} {...composerCustomProps} onSend={innerSend} onclose={onCustomComposerClose} text={text} setText={setText} />
  }

  // 宽屏
  if (isWide) {
    return (
      <>
        {aboveNode}
        <div className="ComposerSwitchSlot">
          {skills && skills.length > 0 && <Skills skills={skills} />}
          <div className="ComposerQuickSlot">
            {quickOpenHistory && <img className="QuickIcon" src={HistoryIcon} onClick={toggleHistory}/> }
            {quickNewConversation && <img className="QuickIcon" src={NewChatIcon} onClick={handleNewConversation}/> }
          </div>
        </div>
        <div className="WideComposer" id="AiChatComposer">
          {/* <div ref={fileRenderPortal}></div> */}
         
          <div className="WideComposer-inputWrap">
            {inputCardList && inputCardList.length > 0 && (
              <div className="InputCardList">
                <InputCardList
                  inputCardList={inputCardList}
                  operationConfig={opConfig}
                  setInputCardList={setInputCardList}
                />
              </div>
            )}
            <ComposerInput invisible={false} {...inputProps} />
          </div>
            <div className="WideComposer-actions">
              <div className="WideComposer-left">
               
                  {showLLMSwitch && (
                    <Select
                      placeholder="请选择模型"
                      suffixIcon={<Icon type={'chevron-down'} />}
                      {...llmConfig?.selectProps}
                      prefixCls={`${prefixCls}-select`}
                      onChange={onLlmChange}
                    >
                      {llmConfig?.llmOptions?.map((option) => (
                        <SelectOption key={option.value} value={option.value}>
                          {option.label}
                        </SelectOption>
                      ))}
                    </Select>
                  )}
                  {showThinking && <ThinkButton isReasoningLLM={isReasoningLLM} />}
                  {showInternetSearch && (
                    <div
                      className={`WideComposer-InternetSearch ${enableInternetSearch ? 'WideComposer-InternetSearch-active' : ''
                        }`}
                      onClick={() => toggleEnableInternetSearch?.(!enableInternetSearch)}
                    >
                      <NetSearchIcon />
                      联网
                    </div>
                  )}
                {extraAction}
              </div>
              <div className="WideComposer-sendWrap">
                {showUploadAttachment && (
                  <Upload  
                    prefixCls={`${prefixCls}-attachments`}
                    {...uploadConfig}
                    // fileRenderPortal={fileRenderPortal}
                    fileList={fileList}
                    onChange={onUploadChange}
                    onConsolidateReports={onConsolidateReports}
                  >
                    {uploadConfig.popoverConfig ? (
                      <Popover prefixCls={`${prefixCls}-tooltip`} {...uploadConfig.popoverConfig}>
                        <span>
                          <AttachmentIcon />
                        </span>
                      </Popover>
                    ) : (
                      <AttachmentIcon />
                    )}
                  </Upload>
                )}
                <WideSendButton
                  onSendClick={(e: React.MouseEvent<HTMLDivElement>) => {
                    e.preventDefault();
                    e.stopPropagation();
                    innerSend();
                  }}
                  onStopClick={onStopAnswer}
                  showStop={showStopAnswer}
                  disabled={!text}
                />
              </div>
            </div>
        </div>
        {belowNode}
      </>
    );
  }

  // 窄屏
  return (
    <>
      {aboveNode}
      <div className="ComposerSwitchSlot">
        {skills && skills.length > 0 && <Skills skills={skills} />}
        <div className="ComposerQuickSlot">
          {quickOpenHistory && <img className="QuickIcon" src={HistoryIcon} onClick={toggleHistory}/> }
          {quickNewConversation && <img className="QuickIcon" src={NewChatIcon} onClick={handleNewConversation}/> }
        </div>
      </div>
      <div
        className={`Composer ${isShowExtraAction ? 'Composer-col-layout' : null}`}
        id="AiChatComposer"
      >        
        <div className="Composer-line">
          {recorder.canRecord && (
            <Action
              className="Composer-inputTypeBtn"
              data-icon={inputTypeIcon}
              icon={inputTypeIcon}
              onClick={handleInputTypeChange}
              aria-label={isInputText ? '切换到语音输入' : '切换到键盘输入'}
            />
          )}
          <div className="Composer-inputWrap">
            {inputCardList && inputCardList.length > 0 && (
              <div className="InputCardList">
                <InputCardList    
                  inputCardList={inputCardList}
                  operationConfig={opConfig}
                  setInputCardList={setInputCardList}
                />
              </div>
            )}
            <ComposerInput invisible={!isInputText} {...inputProps} />
            {!isInputText && <Recorder {...recorder} />}
          </div>
        </div>
        <div className={`Composer-sendWrap ${isShowExtraAction ? 'Composer-up-pad' : ''}`}>
          {hasToolbar && (
            <Action
              className={clsx('Composer-toggleBtn', {
                active: isAccessoryOpen,
              })}
              icon="plus-circle"
              onClick={handleAccessoryToggle}
              aria-label={isAccessoryOpen ? '关闭工具栏' : '展开工具栏'}
            />
          )}
          {/* {showLLMSwitch && (
            <div className="LLMSwitch">
              <span>GPT</span>
              <span>GPT</span>
            </div>
          )} */}
          {showInternetSearch && (
            <div
              className={`InternetSearch ${enableInternetSearch ? 'InternetSearch-active' : ''}`}
              onClick={() => toggleEnableInternetSearch?.(!enableInternetSearch)}
            >
              <NetSearchIcon />
              联网
            </div>
          )}
          {extraAction}
          <div className="Composer-sendButtonWrap">
            {showUploadAttachment && (
              <Upload  
                prefixCls={`${prefixCls}-attachments`}
                {...uploadConfig}
                // fileRenderPortal={fileRenderPortal}
                fileList={fileList}
                onChange={onUploadChange}
                onConsolidateReports={onConsolidateReports}
                className="Composer-attachments"
              >
                {uploadConfig.popoverConfig ? (
                  <Popover prefixCls={`${prefixCls}-tooltip`} {...uploadConfig.popoverConfig}>
                    <span>
                      <AttachmentIcon />
                    </span>
                  </Popover>
                ) : (
                  <AttachmentIcon />
                )}
              </Upload>
            )}
            <SendButton
              onSendClick={(e: React.MouseEvent<HTMLDivElement>) => {
                console.log("onSendClick:",text)
                if(text) {
                  e.preventDefault();
                  e.stopPropagation();
                  innerSend();
                }
              
              }}
              onStopClick={onStopAnswer}
              showLoading={showStopAnswer}
              disabled={!text}
            />
          </div>
        </div>
      </div>
      {belowNode}
      {isAccessoryOpen && (
        <AccessoryWrap onClickOutside={handleAccessoryBlur}>
          {accessoryContent || <Toolbar items={toolbar} onClick={handleToolbarClick} />}
        </AccessoryWrap>
      )}
    </>
  );
});


// 使用 memo 包装这个组件以避免不必要的渲染
export const Composer = React.memo(ComposerC);

/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useStopMessage } from '@ht/chatui';

export default () => {

  const { loading: conversationLoading, run: conversationRun } = useStopMessage({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      appId: 'aorta',
      conversationId: '',
      messageId: '',
      userId: '002332',
      taskId: '01JS0NFWQJ3ZF2PBEMTSBTFGXG',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>停止响应</Button>
    </div>
    )
}
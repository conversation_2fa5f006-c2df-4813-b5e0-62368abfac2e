.ReferencesWrap {
  margin-top: 12px;
  background: rgba(217,217,217,0.2);
  border-radius: 2px;
  padding: 8px 16px;

  .ReferencesTitle {
    font-size: 14px;
    color: #25396F;
    line-height: 20px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .CollapsedIcon {
      margin-left: 8px;
      height: 14px;
      width: 14px;
    }

    > .CollapsedIconWrap {
      display: flex;
    }
  }

  .ReferencesContent {
    margin-top: 12px;
    margin-bottom: 8px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;


    .ReferenceItem {
      padding: 12px 12px 12px 12px;
      background: #FFFFFF;
      border-radius: 8px;
      border: 1px solid #E3E4E6;
      flex-basis: calc(50% - 4px);
      flex-grow: 1;
      flex-shrink: 0;
      margin-top: 0px;
      overflow: hidden;   
      cursor: pointer;
      width: 100%;
      // height: 84px;
      > .tooltip-wrapper {
        width: 100%;
      }
      .ReferenceItemTitle {
        font-size: 14px;
        font-weight: 400;
        color: #007AFF;
        line-height: 20px;
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      > .ReferenceItemContent {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
        white-space: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 12px;
        font-weight: 400;
        color: #7C8DB5;
        line-height: 17px;
        height: 34px;
      }

      > .ReferenceItemFooter {
        height: 18px;
        margin-top: 8px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        > .ReferenceItemFooterLeft {
          flex-grow: 1;

          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          margin-right: 16px;

          > .siteName {
            margin-left: 6px;
            font-size: 12px;
            font-weight: normal;
            line-height: 18px;
            color: #7C8DB5;
          }
        }
        > .ReferenceItemFooterRight {
          width: 36px;
          > .siteTime {
            font-size: 12px;
            font-weight: normal;
            line-height: 18px;
            text-align: right;
            color: #7C8DB5;
          }
        }
      }
    }
  }

  .ReferencesContent-compact {
    flex-direction: column;
  }
}

.ReferencesModalWrap {
  padding: 15px;
  padding-top: 2px;
  > .ReferencesModalTitle {
    height: 24px;
    font-size: 14px;
    color: #007AFF;
    line-height: 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow-x: hidden;
  }
  > .ReferencesModalContent {
    margin-top: 12px;
    padding: 12px;
    padding-bottom: 0px;
    font-size: 14px;
    font-weight: 400;
    color: #25396F;
    line-height: 20px;
    background: rgba(217,217,217,0.2);
    border-radius: 2px;
    max-height: 50%;
    max-height: 244px;
    overflow-y: auto;

    .download_mask {
      display: none;
    }
  }
}
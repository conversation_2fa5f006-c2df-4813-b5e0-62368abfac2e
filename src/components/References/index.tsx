import React, { useState } from 'react';
import moment from 'moment';
import clsx from 'clsx';
import { ReferencesProps, ReferenceProps } from './interface';
import { Tooltip } from '../Tooltip';
import { MarkDown } from '../MarkDown';
import { Modal } from '../Modal';

import upSrc from '../ThinkContent/images/up.svg';
import downSrc from '../ThinkContent/images/down.svg';
import siteIcon from './images/site_icon.svg';

export const ReferenceContent = (props: {
  preprocessContent?: (content: string) => string;
  data: { content: string; title: string; } | null;
  openPage?: (url: string) => void;
}) => {
  const { data, openPage, preprocessContent } = props;
  return (
    <div className="ReferencesModalWrap">
      <div className="ReferencesModalTitle">{data?.title}</div>
      <div className="ReferencesModalContent">
        {data?.content && (
          <MarkDown
            preprocessContent={preprocessContent}
            content={data?.content}
            openPage={openPage}
          />
        )}
      </div>
    </div>
  );
};

export const References = (props: ReferencesProps) => {
  const { references, openPage, referencesConfig, preprocessContent, isWide = false } = props;

  const { title, modalWidth } = referencesConfig || {};

  const [isReferencesCollapsed, setIsReferencesCollapsed] = useState(true);
  const [isModalActive, setIsModalActive] = useState<{ title: string; content: string } | null>(
    null,
  );

  const toggleReferencesCollapse = () => {
    setIsReferencesCollapsed(!isReferencesCollapsed);
  };

  const handleClick = (item: ReferenceProps, index: number) => {
    if (item?.source === 'doc') {
      // 文档类型，按照弹窗进行处理
      const sliceId = item.slice_id || index + 1;
      setIsModalActive({ title: `${sliceId}、${item?.title}`, content: item?.content });
    } else {
      // 默认按照链接进行处理
      if (!item?.url) {
        return;
      }
      if (openPage) {
        openPage(item?.url);
      } else {
        window.open(item?.url);
      }
    }
  };

  if (references?.length < 1) {
    return null;
  }
  const titleNode =
    typeof title === 'function' ? title(references) : title || `参考来源${references?.length}`;
  const renderRefercenCard = (item: ReferenceProps, index: number) => {
    const { source } = item;
    const sliceId = item.slice_id || index + 1;
    return (
      <div key={item?.title} className="ReferenceItem" onClick={() => handleClick(item, index)}>
        <Tooltip content={`${sliceId}、${item?.title}`}>
          <div className="ReferenceItemTitle">{`${sliceId}、${item?.title}`}</div>
        </Tooltip>
        {item?.content && <div className="ReferenceItemContent">{item?.content}</div>}
        {source === 'link' && (
          <div className="ReferenceItemFooter">
            <div className="ReferenceItemFooterLeft">
              <img width={12} height={12} src={siteIcon} />
              <span className="siteName">{item.site_name || ''}</span>
            </div>
            <div className="ReferenceItemFooterRight">
              <span className="siteTime">
                {item.publish_time ? moment(item.publish_time).format('MM-DD') : ''}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  };
  return (
    <div className="ReferencesWrap">
      <div className="ReferencesTitle" onClick={toggleReferencesCollapse}>
        {titleNode}
        <span className="CollapsedIconWrap">
          <span>{isReferencesCollapsed ? '展开详情' : '收起详情'}</span>
          {isReferencesCollapsed ? (
            <img src={upSrc} className="CollapsedIcon" />
          ) : (
            <img src={downSrc} className="CollapsedIcon" />
          )}
        </span>
      </div>
      {!isReferencesCollapsed && (
        <div className={clsx("ReferencesContent", { "ReferencesContent-compact": !isWide})}>{references?.map(renderRefercenCard)}</div>
      )}
      <Modal
        title={
          <div
            style={{
              textAlign: 'left',
              fontSize: '16px',
              fontWeight: 500,
              color: '#000000',
              lineHeight: '22px',
              paddingLeft: '10px',
            }}
          >
            回答来源
          </div>
        }
        showClose
        active={Boolean(isModalActive)}
        onClose={() => setIsModalActive(null)}
        width={modalWidth}
      >
        <ReferenceContent
          preprocessContent={preprocessContent}
          data={isModalActive}
          openPage={openPage}
        />
      </Modal>
    </div>
  );
};

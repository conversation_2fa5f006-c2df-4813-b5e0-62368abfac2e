## Install

```bash
npm install @ht/chatui --save
```

```bash
yarn add @ht/chatui
```

## 场景一、公共组件使用（使用内置接口）


### 前端组件集成示例，JSX文件
```jsx
import AiChat from '@ht/chatui';
import '@ht/chatui/dist/index.css';
import styles from './index.less';//自定义外壳样式，设置宽高，位置

export default function Chat() {
    const config = {
        // 租户id：表示当前系统，来自营销平台
        appId: 'aorta',

        // 用户id：代表当前系统唯一用户id，来自集成方业务系统，需要传入
        userId: '022650',

        // 场景id，来自营销平台
        sceneId: '0',

        // 标识业务平台，来自营销平台
        source: 'chat-component',

        // 接口请求在@ht/chatui已有默认的配置，允许不传每个接口的路径
        requests: {
            // 各业务系统按需配置接口前缀
            baseUrl: '/preifx',
            // 可选，pc和铃客端不涉及，app移动端集成时候如果需要走tcp请求（mobilestock），则需要传action号
            mobileAction：'27006'
        }
    };

    return (
        <div className={styles.chatwrap}>
            <AiChat
              config={config}
              navbar={{
                /**
                 * 如传入onClose，则会展示返回或者关闭按钮，hidePopup为页面返回或者关闭的方法回调
                 * 例如，铃客端有自己框架头部的返回，不需要传此方法
                 * PC端若是一个弹窗，hidePopup可以传弹窗关闭的逻辑
                 * App端若为单独页面，可以传返回上一页的方法
                */
                onClose: hidePopup,
              }}
              // 可以自定义头部导航组件，renderNavbar传了以后navbar传参失效
              renderNavbar={() => (<div>我是一个自定义的头部</div>)}
            />
        </div>
    );
}
```

### 接口转发配置
- 如果使用内置的通用接口路径为:/airobot/api/groovy/ai,转发到 /api/groovy/ai
- 如果参数加了前缀/prefix，则转发配置为/prefix/airobot/api/groovy/ai ,转发到/api/groovy/ai
- 接口地址说明：http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=375099711 ，联系人：张文进，侯党


### 样式覆盖
index.less

- 可采用global包裹进行样式直接进行复写

- 目前有相应的变量可复写，放:root中引入

```css
.chatwrap {
  width: 100%;
  height: 100%;
  // 采用global方式则针对想要修改的样式皆可自定义
  :global {
    .ChatApp {
        background: pink;
    }

    .NavBar {
        box-shadow: 0 0.4rem 0.4rem pink;
    }
  }
}

// 例如默认的消息字体大小为16px，用此变量则默认修改为14px
:root {
    --ai-kit-bubble-text-font-size: 14px;
}
```

## 场景二、接口自定义，如聊TA引用（问答采用TEC流式生成）
### JSX文件

```jsx
import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import LCRender from '@lowcode/lc-render';
import { emp } from '@aorta-pc/utils';
import '@ht/chatui/es/styles/index.less';
// 引入组件
import AiChat from '@ht/chatui';
// 引入样式
import '@ht/chatui/dist/index.css';

// 支持自定义修改的样式，比如默认的气泡字体大小为16px，需要修改可以改变量
import chatStyles from './chatui-theme.less';

// 注入全局魔方渲染组件变量，用于在chat-ui中渲染魔方图
window.LCRender = LCRender;

export default function HTChat(props) {
  /**
  * 需要给AI组件传一个关闭问答页面的方法，移动端的返回，PC端的关闭页面，关闭弹窗等
  * 不传则不会展示关闭或者返回按钮
  */

 // 判断附件是否是pdf格式
  const judgeIsPdf = (name: string) => {
    const pdfRegexp = /\.(pdf)+$/i;
    return pdfRegexp.test(name);
  };

  // 移动端如果需要原生预览附件，可参考，自己传pdf附件预览的方法
  const handleJumpPreview = (downloadURL: string) => {
    const param = {
      file_url: downloadURL,
      // 0-不可转载分享
      show_share: '0',
      title: '附件详情',
    };

    if (judgeIsPdf(downloadURL)) {
      window.openNativeViews('pdf', param);
    }
  };

  const { hidePopup } = props; 

  // 配置信息，必传，包含一些必要公共配置以及接口路径配置
  const config = {
    // 租户id：表示当前系统
    appId: 'aorta',

    // 用户id：代表当前系统唯一用户id
    userId: emp.getId(),

    // 场景id
    sceneId: '',

    // 接口请求 method默认post
    requests: {
      
    // 公共接口移动端tcp请求的action，tcp需要配置，本地起项目需注释
    mobileAction: '53421',

    /**
    * 基础URL
    */
      baseUrl: '/fspa',

      // 初始引导接口
      init: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryWelcomeInfo',
        // 鉴权信息等可以放headers
        headers: { 'iv-user': emp.getId() },
      },

      // 问答接口
      send: {
        url: '/aorta/operation/api/ai/desktop/TecAiAgentChat',
        stream: true,
        // headers: { 'iv-user': emp.getId() }
      },

      // 查询历史接口
      history: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryHistoryMessages',
        // headers: { 'iv-user': emp.getId() },
        pageSize: 6, // 如果不传，组件内置的默认值是3，表示查3对问答，具体传参看接口要求
      },

      // 快捷问题接口
      quickReply: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryQuickReplies',
        // headers: { 'iv-user': emp.getId() },
      },

      // 点赞点踩接口
      score: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentCmd/likeOrDislikeAMessage',
        // headers: { 'iv-user': emp.getId() },
      },

      // 停止生成接口，对于流式生成的接口，需要传
      stop: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentCmd/stopGeneratingAnswer',
        // headers: {
        //   'iv-user': emp.getId()
        // },
      },

      // 关联问题接口，流式生成接口关联问不在问答接口里面返回，需要传
      related: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryRelatedQuestions',
        // headers: {
        //   'iv-user': emp.getId()
        // },
      },
    },

    bridge: {
      openWebPage: (url: any) => {
        openWebpage(url, { shownavigationbar: false });
      },
      openNativeViews: handleJumpPreview,
    },
  };

  // 如果发现在移动端没有消息的情况下输入框focus时输入框上白屏看不全可以传此方法，PC不需要
  const handleInputFocus = useCallback(() => {
    if (msgRef?.current) {
      const { scrollToEnd } = msgRef.current;
      setTimeout(() => {
        scrollToEnd({ animated: false, force: true });
      }, 100);
    }
  }, []);

  return (
    <div className={chatStyles.chatWrap}>
      <AiChat
        navbar={{
          onClose: hidePopup,
        }}
        config={config}
        handleInputFocus={handleInputFocus}
      />
    </div>
  );
}

HTChat.propTypes = {
  hidePopup: PropTypes.func.isRequired,
};
```

### less文件（用来重写变量）
chatui-theme.less
```css
.chatWrap {
  height: 100%;
  border-radius: inherit;
}
:root {
  --ai-kit-bubble-text-font-size: 14px;
}

```

## 移动端REM样式适配，本组件样式按照UI 375稿
举例：在项目mpa-mobapp-host -> .unitoolConf.js

```js
// 针对 @ht/chatui 的规则
config.module
.rule('chatui-css')
.test(/\.css$/)
.include.add(resolveApp('node_modules/@ht/chatui'))
.end()
.use('postcss-loader')
.loader('postcss-loader')   
.options({
  postcssOptions: {
    plugins: [
      require('postcss-pxtorem')({
        rootValue: 37.5, // 仅针对 @ht/chatui 的文件设置 rootValue 为 37.5
        unitPrecision: 3,
        propList: ['*'],
        selectorBlackList: ['.ignore', '.hairlines'],
        replace: true,
        mediaQuery: false,
        minPixelValue: 0,
      }),
    ],
  },
})
.end();
```

举例：在项目mcrm-host -> .postcss.config.js
```js
module.exports = {
  plugins: [
    require('autoprefixer')(),
    require('postcss-write-svg')({
      utf8: false,
    }),
    require('postcss-pxtorem')({
      rootValue: ({ file }) => {
        let rootValue = 75;
        if (file && (/[\\/]@ht[\\/]chatui/.test(file))) {
          rootValue = 37.5;
        }
        return rootValue;
      },
      propWhiteList: [],
      selectorBlackList: [':root'],
    }),
  ]
};
```


## 组件入参类型实例

以下是 `AiChat` 组件的入参类型实例表格：

| 参数名 | 类型       | 必填 | 默认值 | 描述             |
| ------------- | ---------- | ---- | ------ | ------------------------ |
| `locale`      | `string`   | 否   | 无     | 当前语言 |
| `locales`     | `any`      | 否   | 无     | 多语言 |
| `navbar`      | `object`   | 否   | 无     | 导航栏配置 |
| `renderNavbar`| `function` | 是   | 无     | 导航栏渲染函数，会覆盖`navbar` |
| `messagesRef` | `object` | 否   | 无    | 消息列表 ref |
| `initialMessages`    | `object`  | 无   | `false`| 初始消息列表，加载组件默认会加载上的消息，可配置欢迎语等 |
| `quickRepliesVisible` | `boolean` | 否   | 无    | 快捷短语是否可见 |
| `onQuickReplyClick` | `function` | 否   | 无    | 快捷短语的点击回调 |
| `onQuickReplyScroll` | `function` | 否   | 无    | 快捷短语的滚动回调 |
| `renderQuickReplies` | `function` | 否   | 无    | 快捷短语渲染函数，会覆盖`initQuickReplies` |
| `composerRef` | `object` | 否   | 无    | 输入区 ref |
| `onInputFocus` | `function` | 否   | 无    | 输入框聚焦回调 |
| `onInputChange` | `function` | 否   | 无    | 输入框更新回调 |
| `onInputBlur` | `function` | 否   | 无    | 输入框失去焦点回调 |
| `Composer` | `node` | 否   | 无    | 输入组件 |
| `config` | `object` | 否   | 无    | 通用配置 |
| `renderFooterVersion` | `function` | 否   | 无    | 最底部渲染函数 |


### 重要参数详细说明

- `navbar`:
  - 类型: `object`
  - 描述: 导航栏配置。
  - 包含参数： 
    - onClose: 组件关闭回调， 类型：`function`，不传不会展示关闭或者返回按钮

- `config`:
  - 类型: `object`
  - 描述: 若接口非公共通用接口，则为必要传参，具体可见js举例传参。
- `bridge`:
  - 类型: `object`
  - 描述: 传入跳转新页面，预览附件等方法。
  
import React from 'react';
import { Button } from '@ht/ioned';
import { GuidePageProps } from '../../../src/index';
import './CustomWelcome.less';

export default function CustomWelcome(props: GuidePageProps) {
    const mockData = [{
        text: '国际大型汽车制造公司xx收购新能源电池企业',
        prefix: '• ',
        link: 'https://www.baidu.com',
    }, {
        text: '国际大型汽车制造公司xx收购新能源电池企业华为',
        prefix: '• ',
        link: 'https://www.baidu.com',
    }];
    const handleOpenLink = (value: string) => {
        window.open(value, '_blank');
    };
    const renderLink = (value: { text: string; prefix: string; link: string }) => {
        return <div key={value.text} className='item'>
            <Button onClick={() => handleOpenLink(value.link)} type='link'>{`${value.prefix}${value.text}`}</Button>
        </div>
    }
    return (
        <div className="GuidePagePC CustomWelcome">
            <div className="title">张华泰，您好！</div>
            <div className='card'>
                <div className='header'>
                    <div className='title'>2025.2.26资讯信息</div>
                    <div className='extra'>
                        <span>查看全部</span>
                    </div>
                </div>
                <div className='content'>
                        <div className='list'>
                            {mockData.map(renderLink)}
                        </div>
                    </div>
            </div>
        </div>
    );
}
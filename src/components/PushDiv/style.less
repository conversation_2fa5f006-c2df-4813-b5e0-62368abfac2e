/* 容器样式 */
.push-div-container {
  display: flex;
  height: 100%; /* 全屏高度 */
  overflow: hidden;
  position: relative;
  // background: #2c3e50; /* 与历史面板同色消除白边 */
}

/* 历史面板（默认隐藏） */
.history-hanel {
  width: 100%;
  height: 100%;
  background: #2c3e50;
  color: white;
  transition: transform 0.3s ease; /* 改用transform动画 */
  position: absolute;
  top: 0;
  // z-index: 12; /* 高于遮罩层 */
  overflow-y: auto;
}

// .HistoryPanel-left {
//   left: 0;
//   transform: translateX(-100%); /* 初始隐藏 */
//   min-width: 75%; /* 展开宽度 */
// }

// .HistoryPanelOpen {
//   transform: translateX(0); /* 展开时复位 */
// }

/* 对话面板（主内容区域） */
.chat-panel {
  flex: 1; /* 关键：启用flex伸缩 */
  height: 100%;
  background: #ecf0f1;
  transition: transform 0.3s ease;
  position: relative;
  min-width: 100%; /* 防止挤压变形 */
  background-color: pink;
}

// .chat-panel.pushed {
//   transform: translateX(75%); /* 向左推75% */
// }

/* 遮罩层 */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 11; /* 低于历史面板 */
  display: none; /* 默认隐藏 */
}

.overlay-show {
  display: block; /* 展开时显示 */
}
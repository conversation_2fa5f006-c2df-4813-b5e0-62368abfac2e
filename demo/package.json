{"name": "demo", "version": "0.1.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ht/ioned": "^1.0.36", "@ht/xlog": "^4.1.0", "@lowcode/lc-render": "^1.2.0-beta.4", "events": "^3.3.0", "moment": "^2.30.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^6.4.1", "remark-gfm": "^4.0.0", "vite-plugin-node-polyfills": "^0.22.0"}, "devDependencies": {"@types/react": "^17.0.50", "@types/react-dom": "^17.0.17", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^2.1.0", "typescript": "^4.8.4", "vite": "^3.1.3"}}
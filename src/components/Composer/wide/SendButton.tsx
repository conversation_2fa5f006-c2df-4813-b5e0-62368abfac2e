import React from 'react';
import clx from 'clsx';
import SendIcon from '../images/up.svg';
import SendDisableIcon from '../images/up-disable.svg';

interface SendButtonProps {
  disabled?: boolean;
  onSendClick: (e: React.MouseEvent<HTMLImageElement>) => void;
  showStop?: boolean;
  onStopClick?: () => void;
}

export const SendButton = ({ disabled, onSendClick, onStopClick, showStop }: SendButtonProps) => {
  return (
    <>
      {showStop ? (
        <div
          className={clx('WideComposer-SendButton', 'WideComposer-StopAnswer')}
          onClick={onStopClick}
        >
          <div className="WideComposer-StopAnswer-inner"></div>
        </div>
      ) : (
        <div
          className={clx('WideComposer-SendButton', disabled ? 'disabled' : null)}
          onClick={onSendClick}
        >
          <img src={disabled ? SendDisableIcon : SendIcon} alt="send" />
        </div>
      )}
    </>
  );
};

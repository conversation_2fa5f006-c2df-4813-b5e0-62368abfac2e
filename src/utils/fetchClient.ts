/* eslint-disable compat/compat */
// FetchClient.ts
type RequestMethod = 'GET' | 'POST';

interface RequestConfig {
  url: string;
  method?: RequestMethod;
  body?: any;
  headers?: HeadersInit;
}

class FetchClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl || '';
  }

  private createHeaders(headers: HeadersInit = {}): HeadersInit {
    const defaultHeaders = {
      'Content-Type':'application/json',
    }
    return new Headers({...defaultHeaders, ...headers});
  }

  public async get<T>(config: RequestConfig): Promise<T> {
    const { url, headers } = config;
    const response = await fetch(this.baseUrl + url, {
      method: 'GET',
      headers: this.createHeaders(headers),
    });

    if (!response.ok) {
      console.error(`GET request failed with status ${response.status}`);
      // throw new Error(`GET request failed with status ${response.status}`);
    }

    return response.json();
  }

  public async post<T>(config: RequestConfig): Promise<T> {
    const { url, body, headers } = config;
    const response = await fetch(this.baseUrl + url, {
      method: 'POST',
      headers: this.createHeaders(headers),
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error(`POST request failed with status ${response.status}`);
      // throw new Error(`POST request failed with status ${response.status}`);
    }

    return response.json();
  }
}

export default FetchClient;

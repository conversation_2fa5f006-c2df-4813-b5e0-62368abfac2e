---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 1
---

# 整体架构

SCK（Sprite Copilot Kit）是一个服务于AI应用助手建设的集成解决方案。包括对话式体验设计，基于React的AI组件库，接口请求Hooks和标准大模型场景通信协议，帮助业务线快速接入或搭建AI助手应用。


<img src="../../public/home/<USER>" alt="描述" width="100%" height="100%" style="margin: 0 auto;" />

## 1.体验设计
专属华泰的AI助手交互设计规范,人工智能对话式交互体验设计，提供统一、高效、沉浸式交互

## 2. AI UI组件库
提供用于AI场景交互的集成组件和前端组件库，帮助业务线快速接入或搭建对话界面。

### 组件类型
- **集成对话组件**
  - 窄屏 AiChat 组件，助手形式，接口协议需要适配
  - 宽屏 WideAiChat 组件，pc全屏形式，接口协议需要适配

- **核心对话组件**
  - Composer 输入框组件
  - Message 消息组件
  - HistoryConversation 历史会话组件

- **基础卡片组件**
  - FileCard 文件卡片组件
  - Image 图片卡片组件
  - MarkDown 卡片组件
  - Video 视频卡片组件
  - LcRenderCard 魔方自定义卡片组件


### 样式定制
- 主题定制
- 提供丰富的 less 变量支持样式覆盖


## 3. 接口请求Hooks
提供 Headless 方式的 hooks 调用，支持已有完整UI组件的接入场景。
Hooks 库解决了 AI 开发中 UI 与数据紧耦合、交互逻辑不统一、服务对接复杂的问题。它通过解耦展示层与服务层、标准化交互逻辑、提供独立服务接口，实现对不同 AI 平台数据规范的统一适配，并深度优化了请求性能、流式处理效率、丢帧控制及响应速度。

| API | 说明 |
|------|------|
| [useCreateConversation](./hooks/use-create-conversation) | 创建会话 |
| [useGetConversationList](./hooks/use-get-conversation-list) | 获取会话列表 |
| [useDeleteConversation](./hooks/use-delete-conversation)  | 删除会话 |
| [useFeedback](./hooks/use-feedback)  | 回答反馈评价 |
| [useGetMessageList](./hooks/use-get-message-list)  | 获取消息列表 |
| [useSendMessage](./hooks/use-send-message)  | 对话聊天 |
| [useSendMessageAgain](./hooks/use-send-message-again)  | 重新生成回复 |
| [useSensitive](./hooks/use-sensitive)  | 敏感词校验 |
| [useStopMessage](./hooks/use-stop-message)  | 停止响应 |

## 4. 标准消息通信协议
提供标准消息通信协议，业务线可通过实现接口服务快速对接Chat组件实现AI对话应用。
内部接口兼容了泰为智能体接口协议，如果是泰为智能体接口则不涉及接口开发，只需要在chat组件中进行相关配置即可

### Hiagent 泰为智能体接口对接方案说明
基于SCK chat组件实现的chat页面，对接hiagent服务，可以参考宽屏demo的代码示例
- 通过hiagent创建agent，获取对应appkey
- 基于chat组件实现chat页面， 参数参考宽屏demo的代码

## 5. 自定义卡片解决方案
支持业务线灵活自定义业务卡片，SCK提供了2种自定义消息卡片方案。
	1、使用魔方平台搭建卡片UI方案
  2、自定义卡片UI方案

## 6.可观测能力
用户端反馈可观测作为可观测能力重要一环，SCK提供快速接入LangFuse可观测平台能力

## 7.自定义埋点方案
提供了完善的埋点信息及自定义埋点方案

import React, { useState, useEffect, useRef } from 'react';
import clsx from 'clsx';
import { ImagePreview } from '../ImagePreview';
import useForwardRef from '../../hooks/useForwardRef';
import { ImageProps } from './interface';

export const AiImage = React.forwardRef<HTMLImageElement, ImageProps>((props, ref) => {
  const { className, src: oSrc, lazy, fluid, children, preview = true, onClick, ...other } = props;
  const [src, setSrc] = useState('');
  const imgRef = useForwardRef(ref);
  const savedSrc = useRef('');
  const lazyLoaded = useRef(false);
  // 图片预览窗口
  const [isImgPreview, setIsImgPreview] = useState(false);
  // 图片预览地址
  const [imgPreviewUrl, setImgPreviewUrl] = useState('');

  useEffect(() => {
    if (!lazy) return undefined;

    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setSrc(savedSrc.current);
        lazyLoaded.current = true;
        observer.unobserve(entry.target);
      }
    });

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [imgRef, lazy]);

  useEffect(() => {
    savedSrc.current = oSrc;
    setSrc(lazy && !lazyLoaded.current ? '' : oSrc);
  }, [lazy, oSrc]);

  const handleImageClick = (e: React.MouseEvent<HTMLImageElement, MouseEvent>) => {
    if (preview) {
      setIsImgPreview(true);
      setImgPreviewUrl(src);
    }
    if (onClick) {
      onClick(e);
    }
  }

  return (
    <>
      {/* 图片预览窗口 */}
      {isImgPreview && <ImagePreview imgUrl={imgPreviewUrl} hidePreview={() => setIsImgPreview(false)} />}
      <img
        className={clsx('Image', { 'Image--fluid': fluid }, className)}
        src={src}
        alt=""
        ref={imgRef}
        {...other}
        onClick={handleImageClick}
      />
    </>
  );
});

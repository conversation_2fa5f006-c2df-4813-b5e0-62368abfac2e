import React from 'react';
// import  {API_PREFIX} from '@lowcode/lc-render';
import LCRender from '@lowcode/lc-render';
import { DemoPage } from '../components';
import { message, Button } from '@ht/ioned';
import {
  // MessageProps,
  WideAiChat,
  // useMessages,
  ComposerCustomRequestOptions,
} from '../../../src/index';
import WideComposer from '../components/WideComposer';
import CustomWelcome from '../components/CustomWelcome';
import './WideAiChat.less';
import { AiChatHandle } from '../../../src/components/AiChat/interface';

//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;

export default () => {
  // 消息列表
  // const msgRef = React.useRef(null);
  // const ref = React.useRef<AiChatHandle>(null);
  // // 请求参数baseUrl支持传入函数
  // const getBaseUrl = function () {
  //   return 'hiAgent';
  // }

  const config = {
    //系统名称：表示当前系统
    appId: 'hiAgent',

    //用户id：代表当前系统唯一用户id
    userId: '002332',

   
    // 是否测试环境，用于魔方测试环境验证
    // isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },
    onFeedback: (...params) => {
      console.log('点赞接口', params);
    },
    //接口请求 method默认post
    requests: {
      /**
       * 基础URL，接口请求前缀，拼接在下述方法的url前缀，如业务线的公共前缀，支持传入函数或字符串
       */
      baseUrl: 'hiAgent', //getBaseUrl()
      // 如果对接泰为hiagent接口，需要设置platform='hiAgent'
      platform: 'hiAgent',
      // 对应泰为智能体的appkey，联系泰为平台获取
      appKey: 'd0e4lcgim975aeu7usa0',

      // //初始引导接口
      // init: {
      //   url: '',
      //   headers: { empid: '002332', token: 'token', deviceId: 'deviceId' },
      //   // requestTransfer: (input: object) => {
      //   //   return new Promise((resolve, reject) => {
      //   //     try {
      //   //       const parsedInput = {
      //   //         ...input,
      //   //         customerInput1: '123',
      //   //       };
      //   //       resolve(parsedInput);
      //   //     } catch (error) {
      //   //       reject(error);
      //   //     }
      //   //   });
      //   // },
      //   // responseTransfer: (output: object) => {
      //   //   return new Promise((resolve, reject) => {
      //   //     const parsedOutput = {
      //   //       ...output,
      //   //       customeroutput1: '123',
      //   //     };
      //   //     try {
      //   //       resolve(parsedOutput);
      //   //     } catch (error) {
      //   //       reject(error);
      //   //     }
      //   //   });
      //   // },
      // },

      //问答接口
      send: {
        url: '/chat_query',
        stream: true,
        // isAibag: true,
        // requestTransfer(params:any) {
        //     if (true) {
        //       params.inputs = {'key':'htmlContent'}
        //     }
        //     return params
        //   },

          // requestTransfer: (input: object) => {
          // return new Promise((resolve, reject) => {
          //   try {
          //     const parsedInput = {
          //       ...input,
          //       inputs: '123',
          //     };
          //     resolve(parsedInput);
          //   } catch (error) {
          //     reject(error);
          //   }
          // });
        // },
        // messageInterval:30, //前端展示数据帧间隔,默认30
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', deviceId: 'deviceId' },
      },

      //查询历史详情接口
      history: {
        url: '/get_conversation_messages',
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', deviceId: 'deviceId' },
        pageSize: 100,
      },
      //点赞点踩接口
      score: {
        url: '/feedback',
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', deviceId: 'deviceId' },
      },

      //停止生成接口
      stop: {
        url: '/stop_message',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', deviceId: 'deviceId' },
      },

      // 历史会话列表
      historyConversation: {
        url: '/get_conversation_list',
        // headers: { empid: '002332', 'iv-user': '002332', token: 'token', deviceId: 'deviceId' },
      },
     
    },
    bridge: {
      // 原生bridge
      // 打开新页面
      openWebPage: (url: any) => {
        console.log('打开新页面', url);
      },
      // 调用原生方法打开页面预览文件支持word\pdf
      openFileViews: (url: any) => {
        console.log('预览文件', url);
      },
    },
    
    // 全局logo配置
    robot: {
      logo: 'http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/ailogo.9262eff2.svg',
      showMsgLogo:true // 是否在消息答案左侧展示logo头像，默认不展示
    },
  //泰为智能体的前置输入变量，可选，如果涉及需要获取后传入
    // VariableConfigs:
    //   [
    //     {
    //       Key: "key",
    //       Name: "命中",
    //       Required: true,
    //       VariableType: "Enum",
    //       EnumValues: [
    //         "枚举1",
    //         "枚举2",
    //         "最大可以10"
    //       ],
    //       TextMaxLength: 32
    //     },
    //     {
    //       Key: "productCodes",
    //       Name: "productCodes",
    //       Required: true,
    //       VariableType: "Text",
    //       TextMaxLength: 32
    //     }, {
    //       Key: "code",
    //       Name: "文本枚举值",
    //       Required: true,
    //       VariableType: "Paragraph"
    //     }
    //   ]
  };

  const renderFooterVersion = () => {
    return  <div className="ChatFooter-Version">内容由 AI 生成，无法确保信息的真实准确，仅供参考</div>;
  } 


  const renderBrand = () => {
    const handleNewConversation = () => {
      // ref.current?.chatContext?.handleNewConversation?.();
      console.log('创建新会话');
    }
    return <div className="quickNav">
      <Button
        onClick={handleNewConversation}
        className="new_session_btn"
      >
        <div className="new_session_btn_wrap">
          <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/addchat.09d0d5dd.svg"></img>
          <span>开始新会话</span>
        </div>
      </Button>
    </div>;
  };

  const renderFooter = () => {
    return <div
      className="feedback"
      onClick={() => {
        message.info('反馈')
      }}
    >
      <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/feedback.e05ace4f.svg"></img> <span>问题反馈</span>
    </div>;
  }

  const renderTitle = () => {
    return '标题';
  }

  // const [uploadHeaders, setUploadHeaders] = useState({});
  // const [loading, setLoading] = useState(false);
  // const [progress, setProgress] = useState(0);

  // // 计算文件SHA-256哈希
  // const calculateFileHash = async (file: any) => {
  //   try {
  //     const buffer = await file.arrayBuffer();
  //     const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  //     const hashArray = Array.from(new Uint8Array(hashBuffer));
  //     return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  //   } catch (error) {
  //     console.error('计算哈希出错:', error);
  //     throw new Error('文件哈希计算失败');
  //   }
  // };

  // beforeUpload处理函数
  // const beforeUpload = async (file: any) => {
  //   try {
  //     // 计算文件哈希
  //     const fileHash = await calculateFileHash(file);

  //     // 设置上传headers
  //     setUploadHeaders({
  //       'X-Content-Sha256': fileHash,
  //       // 可以添加其他headers
  //       // 'Authorization': 'Bearer token123'
  //       'Content-Type': 'application/json',
  //     });

  //     // 返回true继续上传
  //     return true;
  //   } catch (error: any) {
  //     message.error(error.message);
  //     return false; // 阻止上传
  //   }
  // };

  // 计算 SHA-256 哈希
  const calculateHash = async (file: any): Promise<string> => {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  };


  // 自定义上传实现（使用 XMLHttpRequest）
  const customRequest = async ({ file, onSuccess, onError, onProgress }: ComposerCustomRequestOptions) => {
    // setLoading(true);
    // setProgress(0);

    try {
      // 计算文件哈希
      const fileHash = await calculateHash(file);

     // 创建 XMLHttpRequest
      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          // setProgress(percent);
          onProgress({ percent }, file);
        }
      };

      // 处理完成事件
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch {
            response = xhr.responseText;
          }
          onSuccess(response, file);
        } else {
          onError(new Error(`上传失败: ${xhr.statusText}`), file);
        }
        // setLoading(false);
      };

      xhr.onerror = () => {
        onError(new Error('上传过程中发生错误'), file);
        // setLoading(false);
      };

      xhr.ontimeout = () => {
        onError(new Error('请求超时'), file);
        // setLoading(false);
      };

      // 5. 打开连接
      xhr.open('POST', `/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=${fileHash}&Expire=720h`, true);

      // 设置请求头
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.setRequestHeader('X-Content-Sha256', fileHash);

      // 6. 发送请求
      xhr.send(file);

    } catch (error) {
      // setLoading(false);
      onError(error as Error, file);
    }
  };

  const popoverContent = (
    <div>
      <div>1. 最多支持10个大小不超过10MiB 的文档/图片/音频</div>
      <div>2. 上传文件仅支持以下格式：pdf, doc</div>
    </div>
  );

  return (
    <DemoPage>
      <div
        className="wide-screen-demo"
        style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }}
        id="chat-container"
      >
        <WideAiChat
          // ref={ref}
          navbar={{
            onClose: () => {
              console.log('这里写关闭当前页面的方法');
            },
          }}

          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          initialMessages={[]}
          config={config}
          renderNavbar={() => null}
          renderQuickReplies={() => null}
          showPushHistory={false} // 不需要在AiChat内推出历史会话面板了
          renderFooterVersion={renderFooterVersion}

          // renderComposer={WideComposer}
          // renderWelcome={CustomWelcome}
          welcome={{
            title: '基础平台技术文档助理',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            showsubTitle: false,
            riskTip: ' ',
            // showRiskTip: false,
            openMessages: [
              { type: 'text', content: '您好！我是基础平台技术文档助理，专注于数科基础平台领域的技术文档支持，可以协助您进行技术文档的编写与修订，以及技术方案的评审。您可以上传PDF格式的文档进行交流。' },
              { type: 'list', content: '如何撰写一份高质量的技术文档？' },
              { type: 'list', content: '在技术方案评审中需要注意那些关键点？' },
              { type: 'list', content: '能否提供一些基础平台技术文档的模板？' },
            ],
            // navbar: {
            // 配置和上面的navbarProps相同，优先在欢迎页生效
            // title: '欢迎页标题',
            // wrapstyle: {
            //   paddingTop: '20px',
            // },
            // },
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}

          historyConversation={{
            showLogo: true,
            title: '智能助理智能助理智能助理智能助理智能助理',
            showCloseButton: false,
            showSearch: false,
            // renderBrand,
            // renderFooter,
            // renderTitle,
          } as any}
          composerConfig={{
            // placeholder:'请输入问题',
            // text:'初始化问题',
            showInternetSearch: false,
            // aboveNode:(<div >I am aboveNode~</div>),//reactNode
            // belowNode:(<div >I am belowNode~</div>),//reactNode
            uploadConfig: {
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // action: '/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=11111111&Expire=720h',
              // fileList: fileList,
              // onChange: onChange,
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // fileList: fileList,
              // onChange: onChange,
              // headers: {
              //   'X-Content-Sha256': 'c975c2049dfe87d8d3c6002de962426ca96e504c7402abd01d21cb8fedf028f5',
              //   'Content-Type': 'application/json',
              // },
              // beforeUpload,
              // headers: uploadHeaders // 动态headers
              customRequest,
              popoverConfig: {
                content: popoverContent,
              },
              padUrlPrefix: (path) => `http://10.102.80.243/llmpf/api/proxy/down?Action=Download&Version=2022-01-01&IsAnonymous=true&Path=${encodeURIComponent(path)}`,
            },
           
            skill: [
              {
                key: 'translate',// 必传，唯一标识
                disabled: false,//是否禁用
                icon: '',// 图标
                label: '翻译2',// 文案
                question: '翻译上面文字',// 发送时的命令
                agentId: '1',//发送时可指定智能体ID
                // children?: Skill[],//折叠子项,有子项时，点击会展开子项
                // expandIcon?: string,// 折叠图标
                // customRender?: React.ReactNode,//自定义渲染
                onClick: () => {
                  console.log(1111);
                }// 技能点击回调 -》 由AiChat统一写入Composer
              }
            ],
            
          }}
          showToken={false} //是否展示大模型消耗token数量
          showHallucination={false}
        />
      </div>
    </DemoPage>
  );
};

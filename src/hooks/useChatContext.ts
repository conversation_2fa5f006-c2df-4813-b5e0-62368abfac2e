import React, { useState } from 'react';
import { ConversationItemProps } from '../components/HistoryConversation/interface';
import { UploadFile } from '../components/Composer/wide/Upload';
export type ChatContextState = {
  messageSending: boolean;
  isNewConversation: boolean; // 是否处于新建会话状态
};
export interface ChatContext {
  showStopAnswer: boolean;
  isNewConversation: boolean;
  setShowStopAnswer: React.Dispatch<React.SetStateAction<boolean>>;
  setIsNewConversation: React.Dispatch<React.SetStateAction<boolean>>;
  historyConversationList: ConversationItemProps[];
  conversationId: React.MutableRefObject<string>;
}

export interface ChatContextProps extends ChatContext {
  config?: any;
  onSend: (type: string, val: string, payload?: object, transformedFiles?: UploadFile[]) => boolean | undefined;
  handleStopAnswer: () => void;
  handleNewConversation: () => void;
  selectHistoryConversation: (id: string) => void;
  toggleHistory: () => void;
}

const defaultState: ChatContextState = {
  messageSending: false,
  isNewConversation: false, // 是否处于新建会话状态
};

export default function useChatContext(props: { initState: object }): any {
  const { initState } = props;
  const [state, setState] = useState({ ...defaultState, ...initState });
  const chatContext = {
    state,
    setState,
    eventBus: {
      dispath: window.document.dispatchEvent,
      addEventListener: window.document.addEventListener,
      removeEventListener: window.document.removeEventListener,
    },
  };
  return chatContext;
}

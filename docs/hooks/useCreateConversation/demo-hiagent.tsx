/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useCreateConversation } from '@ht/chatui';

export default () => {

  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useCreateConversation({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      manual: true,
      type: 'http',
    },
    requestParams: {
      appKey: 'd07gpo0im975aeu764t0',
      userId: '016645',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>创建会话</Button>
      {conversationData?.resultData?.conversationId ? (
        <span style={{ padding: '10px', color: 'green'}}>会话创建成功,会话id: {conversationData?.resultData?.conversationId}</span>
      ) : ''}
    </div>
    )
}

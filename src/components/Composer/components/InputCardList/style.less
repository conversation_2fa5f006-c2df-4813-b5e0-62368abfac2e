.inputCardList {
  width: 100%;
  // max-height: 120px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  //   overflow-y: hidden;
  //   overflow-x: auto;

  .listContainer {
    width: calc(100% - 36px);
    // max-height: 120px;
    padding: 12px;
    overflow-y: hidden;
    overflow-x: auto;
    .list {
      // height: 58px;
      display: flex;
      flex: 1;
      gap: 8px;
      flex-direction: row;
      justify-content: flex-start;
      align-items: flex-start;
    }
    .operations {
      margin-top: 12px;
    }
  }

  .scrollButton {
    width: 24px;
    height: 58px;
    cursor: pointer;
    position: absolute;
    top: 12px;
    right: 12px;
    border-radius: 8px 0 0 8px;
    opacity: 1;
    background: linear-gradient(
      270deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0%) 100%
    );
    // background-color: #ff0000;
    background: #f2f2f3;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

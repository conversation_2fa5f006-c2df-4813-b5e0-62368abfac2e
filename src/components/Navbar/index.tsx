/**
 * https://xcg1a1l6ku.feishu.cn/wiki/C5IHwFpLNiKA7qkScCzc1PIOnVf
 */
import React, { ReactNode, useEffect, useState } from 'react';
import { Tooltip } from '../Tooltip';
import deepseekChat from './images/deepseekChat.png';
import AppBack from './images/appBack.png';
import HistoryIcon from './images/historyIcon.svg';
import NewChatIcon from './images/newChatIcon.svg';
import CloseIcon from './images/closeIcon.svg';

export type NavbarProps = {
  open?: boolean,//是否展示navbar，默认true

  // 返回按钮设置项
  showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
  returnButtonIcon?: string;//返回按钮图标路径
  onReturnButtonClick?: () => void;//点击返回按钮响应处理函数

  // 标题区域设置
  showLogo?: boolean,//是否展示logo,默认为true
  logo?: string, // logo图标的地址
  title?: ReactNode |string; //头部标题文案，展示于logo右侧,默认为空字符串
  logoAndTitlePosition?: string;//标题区域的位置：pc端默认靠左边，移动端默认居中

  // 历史会话按钮设置项
  showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
  historyButtonIcon?: string,//历史会话按钮图标路径
  historyButtonPosition?: string,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
  onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数


  //新建会话按钮设置项
  showNewButton?: boolean;//是否显示新建会话按钮，默认为true
  newButtonIcon?: string,//新建会话按钮图标路径               
  onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

  // 关闭按钮设置项
  showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
  closeButtonIcon?: string; //关闭按钮图标路径
  onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数

  newConTooltip?: string;// 新建会话按钮提示语,pc必传值，app不传值
  wrapstyle?: React.CSSProperties; // CSS样式
};

const NavbarC: React.FC<NavbarProps> = (props) => {

  const {
    open = true,
    newConTooltip,
    // 返回按钮设置项
    showReturnButton,
    returnButtonIcon = AppBack,
    onReturnButtonClick,

    // 标题区域设置
    showLogo = true,
    logo = deepseekChat,
    title = '',
    logoAndTitlePosition = 'left',

    // 历史会话按钮设置项
    showHistoryButton = true,
    historyButtonIcon = HistoryIcon,
    historyButtonPosition = 'right',
    onHistoryButtonClick,


    //新建会话按钮设置项
    showNewButton = true,
    newButtonIcon = NewChatIcon,
    onNewButtonClick,

    // 关闭按钮设置项
    showCloseButton,
    closeButtonIcon = CloseIcon,
    onCloseButtonClick,
    wrapstyle,
  } = props;

  const [titleTooltipShow, setTitleTooltipShow] = useState(false);
  useEffect(() => {
    const titleEle = document.getElementById('navbarTitle');
    if (titleEle) {
      if (titleEle.scrollWidth > titleEle.clientWidth) {
        setTitleTooltipShow(true);
      } else {
        setTitleTooltipShow(false);
      }
    }
  }, []);

  const TooltipImg = (prop: any) => {
    if (newConTooltip) return (
      <Tooltip {...prop.tooltip} >
        <img {...prop} />
      </Tooltip>
    )
    return (
      <img {...prop} />
    )
  }

  const renderTitle = ()=>{
    if(typeof title==='string'){
      return titleTooltipShow ? <Tooltip content={title} placement={'bottom'}><span id='navbarTitle'>{title}</span></Tooltip> : <span id='navbarTitle'>{title}</span>
    } else {
      return title;
    }
  }

  if (!open) return null;
  return (
    <div style={wrapstyle}>
      <div className={newConTooltip ? 'PcNavBar' : 'MobileNavBar'}>
        {showReturnButton && <img src={returnButtonIcon} className="returnButtonIcon" onClick={onReturnButtonClick} />}
        {showHistoryButton && historyButtonPosition === 'left' && <TooltipImg src={historyButtonIcon} className="historyButtonIcon" onClick={onHistoryButtonClick} tooltip={{
          content: '查看历史会话',
          placement: 'bottomRight'
        }} />}
        <div className={logoAndTitlePosition === 'center' ? 'titleCenter' : 'titleLeft'}>
          <div className='title'>
            {showLogo && <img src={logo || deepseekChat} className="logoIcon" />}
            {renderTitle()}
          </div>
        </div>
        {showHistoryButton && historyButtonPosition === 'right' && <TooltipImg src={historyButtonIcon} className="historyButtonIcon" onClick={onHistoryButtonClick} tooltip={{
          content: '查看历史会话',
          placement: 'bottomLeft'
        }} />}
        {showNewButton && <TooltipImg src={newButtonIcon} className="newButtonIcon" onClick={onNewButtonClick} tooltip={{
          content: newConTooltip,
          placement: 'bottomRight'
        }} />}
        {showCloseButton && <img src={closeButtonIcon} className="closeButtonIcon" onClick={onCloseButtonClick} />}
      </div>
    </div>

  )
}

// 使用 memo 包装这个组件以避免不必要的渲染
export const Navbar = React.memo(NavbarC);

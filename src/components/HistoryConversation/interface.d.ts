/*
 * @Author: 020446 tianxia<PERSON><EMAIL>
 * @Date: 2025-06-20 16:29:03
 * @LastEditors: 020446 <EMAIL>
 * @LastEditTime: 2025-08-12 20:34:58
 */
export interface HistoryConversationProps {
    style?: any;
    standaloneMode?: boolean; // 为true时作为独立组件使用，为false时在集成对话组件中使用
    title?: string; // 默认值'历史会话记录',
    logo?: string, // 支持传入logo, 不传则不展示
    navbar?: any; // 兼容AiChat传入其他参数
    renderTitle?:() => React.JSX.Element | React.Node; // 自定义标题区域

    showSearch?: boolean; // 是否展示列表搜索,默认值为true
    searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”

    renderBrand?:() =>  React.JSX.Element | React.Node; // 自定义品牌区域
    renderFooter?:() =>  React.JSX.Element | React.Node; // 自定义底部区域
    renderListItem?: (any) => React.JSX.Element | React.Node; // 自定义item元素区域
    showDeleteConversation?: boolean; // 是否支持删除会话
    showRename?: boolean; // 是否支持重命名
}

// 历史组件在AiChat中使用的参数
export interface HistoryListInAiChatProps {
    pushPosition?: 'left' | 'right'; // 推的方向，左推还是右推
    pushPercent?: number; // 推多宽，50 表示半屏，100表示全屏，默认75   
    show?: boolean; // 是否显示消息历史 
}

// 历史组件独立使用的参数
export interface HistoryConversationCustomerProps {
    activeConversationId?: string; // 当前高亮消息
    onConversationClick: (conversationItem: ConversationItemProps) => void;  // 点击消息列表中消息后的回调
    onConversationRename?: (conversationItem: ConversationItemProps) => void; 
    onConversationDelete?: (conversationItem: ConversationItemProps) => void;
    list: ConversationItemProps[];   
}

export type ConversationItemProps = {
    conversationId: string;
    createTime: number; // TimeStamp
    question: string; // 历史消息标题
    scenario?: string; // 场景
    disabled?: boolean; // 是否禁用
    [key: string]: any; // 预留其他参数
}

export interface GroupedItem {
    group: string; // 分组名称（如 "今天"）
    items: ConversationItemProps[]; // 分组后的数据项
}
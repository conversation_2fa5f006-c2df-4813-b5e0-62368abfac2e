---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 4
---

# 埋点方案

## 集成组件埋点方案：
采用全埋点自动化采集用户行为，默认覆盖所有可交互元素的数据。实现方法为通过组件接收的埋点方法，在组件内所有可埋点位置全量调用，在回调中传入固定格式（基本参数+动态参数）的所有参数。如有上报数据筛选和分析需求，可在传入的自定义埋点方法中自行调整。

## 目标：
- 组件与业务解耦，仅接收一个埋点方法实现所有埋点数据抛出;
- 支持用户自定义埋点方法, 自行处理数据结构和数据结构；
- 默认上报结构为xlog格式，基本满足大部分运营业务需求；

## 适用组件
- AiChat 窄屏对话组件
- WideAiChat 宽屏对话组件

## 全量埋点参数说明

###  埋点参数类型说明


```ts
export interface ILogParams {
  id: string;
  page_id: string;
  page_title?: any;
  btn_title?: {
    [key: string]: any;
  };
  btn_id?: string;
}

```
| 参数       | 说明           |
|------------|----------------|
| id         | 组件唯一标识   |
| page_id    | 页面唯一标识   |
| page_title | 页面标题       |
| btn_title  | 实际操作相关参数，其中`btn_label`为操作名称，其他属性为动态参数       |
| btn_id     | 按钮唯一标识   |


### 埋点上报数据


| id | page_id | page_title| btn_id | btn_title |
| --- | --- | --- | --- | --- |
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_NEW_CONVERSATION` | `btn_label`: 智能助手-开启新对话 | 
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_UP` | `btn_label`: 智能助手-点赞文本 |  
|` button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_DOWN` | `btn_label`: 智能助手-点踩文本 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_UP` | `btn_label`: 智能助手-取消点赞 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_DOWN` | `btn_label`: 智能助手-取消点踩 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_ANSWER_COPY` | `btn_label`: 智能助手-复制文本 <br> `id`: 实际内容id |
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_RISK_OPEN` | `btn_label`: 智能助手-查看风险提示 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_RISK_CLOSE` | `btn_label`: 智能助手-关闭风险提示 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_RISK_AGREE` | `btn_label`: 智能助手-同意风险提示 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_INTERNE_SEARCH_OPEN` | `btn_label`: 智能助手-联网搜索 |  
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_INTERNE_SEARCH_CLOSE` | `btn_label`: 智能助手-关闭联网搜索 |  
| `button_click`| `AICHAT_HISTORY_LIST_PAGE` | 智能助手历史会话列表页 | `AICHAT_HISTORY_LIST_PAGE_OPEN` | `btn_label`: 智能助手-打开历史会话列表 |  
| `button_click`| `AICHAT_HISTORY_LIST_PAGE` | 智能助手历史会话列表页 | `AICHAT_HISTORY_LIST_PAGE_OPEN` | `btn_label`: 智能助手-关闭历史会话列表 |  
| `button_click`| `AICHAT_HISTORY_LIST_PAGE` | 智能助手历史会话列表页 | `AICHAT_HISTORY_LIST_PAGE_ITEM_OPEN` | `btn_label`: 智能助手-查看历史会话 <br> `id`: 实际会话id | 
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_COMPOSER_SEND` | `btn_label`: 输入框-发送 | 
| `button_click`| `AICHAT_CONVERSATION_PAGE` | 智能助手对话页 | `AICHAT_CONVERSATION_PAGE_COMPOSER_UPLOAD` | `btn_label`: 输入框-上传文件 | 



---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 2
---

# 接入说明

## 1. 全栈式解决方案：接入集成对话组件

**适用场景**
- 业务线从零开始需要快速实现AI助手功能  
- 希望最小化开发成本，快速上线基础对话功能  
- 前后端都需要完整解决方案  

**实现方案**
- **前端**：直接接入SCK提供的完整集成对话组件  
  - 窄屏AiChat组件：适合侧边栏、悬浮窗等场景
  - 宽屏WideAiChat组件：适合独立页面、全屏对话场景 
  - 支持样主题定制、组件内部分功能和样式定制  
- **后端**：
  - 按照SCK提供的消息通信协议标准封装,或者使用泰为智能体发布的hiagent接口
  - 实现消息收发、历史记录、上下文管理等接口  
  - 可选择性接入知识库检索、多轮对话等增强能力  

**优势**  
- ✅ 开箱即用，1天内可完成基础对接  
- ✅ 内置完善的消息展示、输入交互、异常处理等逻辑  
- ✅ 持续获得完整功能升级和优化  

**典型用户** 
- 业务线快速接入AI助手  


## 2. 模块化方案：页面部分自定义，接入核心对话组件和基础卡片组件组装
**适用场景**
- 已有基础页面框架，需要嵌入对话功能  
- 需要深度定制UI但保留核心交互逻辑  
- 业务有特殊的布局或功能组合需求  

**优势**
- 🛠️ 灵活拼装业务需要的界面组合
- 🎨 可深度定制单一样式
- 🔄 仍享受核心组件持续优化

**示例**
<code src="./basicAiChat/index.tsx"></code>

## 3. 轻量级方案：完全自定义页面，仅接入hooks接口逻辑

**适用场景**
- 已有完整对话界面
- 仅需对接后端AI能力
- 需要完全自主控制交互流程

**优势**
- ⚡ 零UI侵入，100%自主控制
- 🧩 按需选择功能模块
- 🔌 适合渐进式改造

## 场景对比矩阵

| 维度       | 全集成方案          | 组件化方案          | Hooks方案           |
|------------|-------------------|-------------------|-------------------|
| 开发速度   | ⚡️ 极快  | 🏃 较快 | 🐢 较慢  |
| UI自由度   | 低                | 高                | 100%自主          |
| 维护成本   | 低                | 中                | 高                |
| 适合阶段   | 从0到1            | 1到N迭代          | 现有系统改造       |


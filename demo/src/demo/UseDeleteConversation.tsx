/*
 * @Author: 016645
 * @Description: 删除会话
 * @Date: 2025-04-02 19:28:48
 */
import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button } from '../../../src';
import { useDeleteConversation } from '../../../src/hooks/aiAgentChat/index';

export default () => {
  const { loading: conversationLoading, run: conversationRun } = useDeleteConversation({
    requestConfig: {
      platform: 'custom',
      url: '', // 删除地址
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '002332',
      conversationId: '3038468f-b55a-48ea-a9ab-83405c0253f9',
    },
  });

  return (
    <DemoPage>
      <DemoSection title="自定义服务">
        <Button onClick={() => conversationRun()} loading={conversationLoading}>删除会话</Button>
      </DemoSection>

    </DemoPage>
  )
};
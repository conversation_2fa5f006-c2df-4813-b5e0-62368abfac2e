/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-02 19:29:12
 */
import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button } from '../../../src';
import { useGetMessageList } from '../../../src/hooks/aiAgentChat/index';

export default () => {
  const { loading: conversationLoading, run: conversationRun, data: conversationData } = useGetMessageList({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '002332',
      conversationId: '16ba2e40-4f4f-4dbb-b690-d1440b6b99d0',
      pageNum: 1,
      pageSize: 10,
    },
  });

  return (
    <DemoPage>
      <DemoSection title="自定义服务">
        <Button onClick={() => conversationRun()} loading={conversationLoading}>获取消息列表</Button>
      </DemoSection>
      <div>
        {conversationData?.resultData?.list?.map((item, index) => (
          <div key={index}>
            {item.list.map((subItem: any, subIndex: number) => {
              return (
                <div key={subIndex}>
                  {subItem?.content?.text}
                </div>
              )
            })}
          </div>
        ))}
      </div>
    </DemoPage>
  )
};
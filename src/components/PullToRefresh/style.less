.PullToRefresh {
  height: 100%;
  overflow-y: scroll;
  // -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    /* 轨道背景色 */
    // border-radius: 6px;
    /* 轨道圆角 */
  }

  &::-webkit-scrollbar-thumb {
    border: 3px solid #B9D1E9;
    width: 6px;
    height: 45px;
    background: #B9D1E9;
    border-radius: 3px;
  }

  &-fallback {
    padding-top: @gutter;
    text-align: center;
  }

  &-nonFallback {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 12px;
    color: #999;
    line-height: 17px;
    padding-bottom: 20px;

    &-icon {
      width: 10px;
      height: 16px;
      margin-top: 8px;
    }
  }

  &-loadMore {
    height: 28px;
    background: rgba(153, 153, 153, 0.2);
    border-radius: 14px;
    font-size: 12px;
    padding: 0 15px;
    color: #666;
    line-height: 28px;
    cursor: pointer;
  }
}

.PullToRefresh-inner {
  overflow: hidden;
  min-height: 100%;
}

.PullToRefresh-indicator {

  color: grey;
  text-align: center;

  display: flex;
  justify-content: center;
}

.PullToRefresh-spinner {
  color: @gray-3;
  font-size: 27px;
}

.PullToRefresh-transition {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  // transition: transform 0.3s;
  // -webkit-transform: translate3d(0, 0, 0);
  // transform: translate3d(0, 0, 0);
  // will-change: auto;
  // backface-visibility: hidden;
  // width: 100%;
  // height: 100%;
}
/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useStopMessage } from '@ht/chatui';

export default () => {

  const { loading: conversationLoading, run: conversationRun } = useStopMessage({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      manual: true,
      type: 'http',
    },
    requestParams: {
      userId: '016645',
      appKey: 'd07gpo0im975aeu764t0',
      taskId: '01JS0NFWQJ3ZF2PBEMTSBTFGXG',
    },
  });

  return (
    <div>
      <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>停止响应</Button>
    </div>
    </div>
    )
}

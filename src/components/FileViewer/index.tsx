/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import getExtName from '../../utils/getExtName';
// import WordPreviewByUrl from './wordViewer';
// import DocxViewer from './wordViewerTest';
import { PdfViewer } from '../PdfViewer';
// import PdfByFileViewer from './pdfViewer';
// import PdfjsDistViewer from './pdfjsDistViewer';

interface FileViewerProps {
    file: { url: string, name: string };
    handleClose: () => void;
}

const FileViewer: React.FC<FileViewerProps> = ({ file, handleClose }) => {
    const { url, name } = file;
    const ext = getExtName(name);
    return (
        <>
            {/* 这是通过https://aorta.htzq.com.cn/mobMain/static/public/pdf/web/viewer.html预览的 */}
            {ext === 'pdf' && <PdfViewer fileUrl={url} handleClose={handleClose} />}

            {/* 这是通过react-file-viewer预览的 */}
            {/* {ext === 'pdf' && <PdfByFileViewer fileUrl={url} handleClose={handleClose} fileType={ext} />} */}

            {/* 这是通过pdfjs-dist预览的 */}
            {/* {ext === 'pdf' && <PdfjsDistViewer fileUrl={url} handleClose={handleClose} disableVisibilityCheck={false} />} */}

            {/* {ext === 'docx' && <PdfByFileViewer fileUrl={url} handleClose={handleClose} fileType={ext} />} */}
            {/* {ext === 'docx' && <WordPreviewByUrl fileUrl={url} handleClose={handleClose} />} */}
            {ext === 'docx' && <PdfViewer fileUrl={url} handleClose={handleClose} />}
            {/* {ext === 'docx' && <DocxViewer fileUrl={url} handleClose={handleClose} />} */}
        </>
    )
};

export default FileViewer;

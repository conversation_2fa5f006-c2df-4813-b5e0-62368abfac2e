.ThinkContentWrap {
  font-size: @bubble-think-text-font-size;
  color: #888;
  line-height: 20px;

  .Thinking {
    display: flex;
    align-items: center;

    .ThinkingText {
      font-size: 14px;
      color: #888;
      line-height: 20px;
      margin-right: 6px;
    }
  }

  .ThinkingCompleted {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .CollapsedIcon {
      margin-left: 4px;
      height: 14px;
      width: 14px;
    }
  }

  .isThinkingCollapsedCompleted {
    margin-bottom: 0;
  }

  .ThinkContent {
    padding-left: 13px;
    position: relative;
    background: @bubble-think-bg;

    .markdown-body {
      color: @bubble-think-color;
      font-size: @bubble-think-text-font-size;
    }

    &::before {
      position: absolute;
      left: 0;
      content: '';
      width: 1px;
      height: 100%;
      background: #e1e1e1;
      border-radius: 2px;
    }
  }
}

.isExistThinkContent {
  margin-bottom: 12px;
}
.thinkButton {
  width: 68px;
  height: 32px;
  border-radius: 16px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 5px 10px;
  gap: 4px;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #1d222c;

  /* 中性色/0%-FFFFFF */
  background: #ffffff;
  box-sizing: border-box;

  /* 中性色/15%-DDDEE0 */
  border: 0.5px solid #dddee0;
  z-index: 1;
  cursor: default;
}

.thinkButton:hover {
  color: #1d222c;
  border: 0.5px solid #dddee0;
  background: #ffffff;
  cursor: default;
}

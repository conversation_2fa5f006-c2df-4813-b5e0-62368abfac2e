@import './wide/index.less';
@import './compact/index.less';
@import './components/InputCard/style.less';
@import './components/InputCardList/style.less';
@import './components/Operations/style.less';
@import './components/ThinkButton/style.less';
@import './components/Skills/style.less';
@import './components/Composer/components/SendButton/style.less';
@import './components/Composer/TextComposer/style.less';
@import './components/Composer/TranslateComposer/style.less';
@import './components/Composer/WebComposer/style.less';
@import './components/Composer/components/SendButton/style.less';

.Composer-net-search-icon {
  width: 16px;
  height: 16px;
  margin-right: 3px;
}

.WideComposer{
//   position: absolute;
// left: 1352px;
// top: 920px;
// width: 536px;
height: 118px;
overflow-x: hidden;
overflow-y: auto;
border-radius: 12px;
opacity: 1;
	
/* 自动布局 */
display: flex;
flex-direction: column;
justify-content: flex-start;
padding: 12px;
	
/* 中性色/0%-FFFFFF */
background: #FFFFFF;
	
box-sizing: border-box;
/* 中性色/15%-DDDEE0 */
border: 1px solid #DDDEE0;
	
/* 输入框投影 */
box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);

.WideComposer-inputWrap{
  flex:1;
 
  opacity: 1;

font-family: PingFang SC;
font-size: 16px;
font-weight: normal;
line-height: 24px;
letter-spacing: 0em;
}


.WideComposer-actions{
  position: relative;
  bottom: 0;
  margin-bottom: 0px;

  height: 32px;

  .WideComposer-left{
    -webkit-column-gap: 8px;
    column-gap: 8px;
  }
  .WideComposer-select-single {
    height: 32px;

.WideComposer-select-selector{
  background:#F6F6F7;
  border:none;
  border-radius: 16px;
  border: 0.5px solid #DDDEE0;
 
}
  }
  .WideComposer-InternetSearch {
    height: 32px;
    border: 0.5px solid #DDDEE0;
    border-radius: 16px;
  }
  .WideComposer-InternetSearch-active{
    color: #007aff;
    border: 0.5px solid #007aff;
  }
  .WideComposer-SendButton{
    width: 32px;
    border-radius: 16px;
  }
}
	
.WideComposer-attachments-filecard {
  // position: absolute;
  // left: 0px;
  // top: 0px;
  // width: 236px;
  // height: 58px;
  // /* 圆角 */
  // border-radius: 8px;
  // opacity: 1;
    
  // /* 自动布局 */
  // display: flex;
  // flex-direction: row;
  // align-items: center;
  // padding: 7px 12px;
  // gap: 8px;
    
  /* 中性色/6%-F6F6F7 */
  background: #F2F2F3;
    
}
}


.Composer{

border-radius: 12px;

padding: 12px;
	
/* 中性色/0%-FFFFFF */
background: #FFFFFF;
	
box-sizing: border-box;
/* 中性色/15%-DDDEE0 */
border: 1px solid #DDDEE0;
	
/* 输入框投影 */
box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
height: 118px;
overflow-y: auto;
display: flex;
flex-direction: column;

&-sendButtonWrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

&-attachments {
  margin-right: 15px;
  position: relative;
  top: 3px;
}
}

.Composer-col-layout{
    height: 118px;
border-radius: 12px;
opacity: 1;
	
/* 自动布局 */
display: flex;
flex-direction: column;
justify-content: flex-start;
padding: 12px;
	
/* 中性色/0%-FFFFFF */
background: #FFFFFF;
	
box-sizing: border-box;
/* 中性色/15%-DDDEE0 */
border: 1px solid #DDDEE0;
	
/* 输入框投影 */
box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);

.InternetSearch {
  border: 0.5px solid #DDDEE0;
  border-radius: 16px;
}
.InternetSearch-active{
  color: #007aff;
  border: 0.5px solid #007aff;
}
}


.InputCardList{
  width: 100%;

}

.ComposerSwitchSlot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  height: 32px;
  width: 100%;
  .ComposerQuickSlot {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .QuickIcon {
      width: 32px;
      height: 32px;
      padding: 7px;  
    }
  }
}
import React, { useCallback, useState } from 'react';
import clsx from 'clsx';
import { SystemMessage } from './SystemMessage';
import { HallucinationMarker } from '../HallucinationMarker';
import { Feedback } from '../Feedback';
import { Avatar } from '../Avatar';
import { Time } from '../Time';
import { Typing } from '../Typing';
import WarningIcon from '../Bubble/images/warning.svg'
import DeleteIcon from './images/delete.svg'
import { ILogParams } from '../../LogPointConfigMap';
import { Bubble } from '../Bubble';
import { ThinkContent } from '../ThinkContent';
import { Lowcode } from '../Lowcode';
import { AiImage } from '../Image';
import { GuessAskMore } from '../GuessAskMore';
import { Card } from '../Card';
import { MarkDown } from '../MarkDown';
import { FileCard } from '../FileCard';
import { Video } from '../Video';
import { References } from '../References';
import { getThinkContent } from '../../utils/aiChat';
import { ReferenceProps } from '../References/interface';
import { FeedbackLabelProps } from '../FeedbackTipPc/interface';
import { CitewebCard } from '../CitewebCard';
import { CitetextCard } from '../CitetextCard';
import { DeleteModal } from '../Modal';

export interface User {
  avatar?: string;
  name?: string;
  url?: string;
  [k: string]: any;
}

export type MessageId = string | number;

export interface MessageProps {
  /**
   * 唯一ID
   */
  _id: MessageId;
  /**
   * 用来回传给后端的messageId
   */
  messageId?: MessageId;
  /**
   * 消息类型
   */
  type?: string;
  /**
   * 消息内容
   */
  content?: any;
  /**
   * 消息位置
   */
  position?: 'left' | 'right' | 'center' | 'pop'; 
  /**
   * 消息发送者信息
   */
  user?: User;
  /**
   * 消息创建时间
   */
  createdAt?: number;
  /**
   * 是否显示时间
   */
  hasTime?: boolean;
  /**
 * 是否需要点赞点踩
 */
  needFeedback?: boolean;
  /**
   * 需要的反馈按钮数组，枚举数据类型，默认['copy', 'good', 'bad']
   */
  feedbackArray?: Array<'copy' | 'good' | 'bad' | 'quote'>;
  /**
   * 消息内容渲染函数
   */
  renderMessageContent?: (message: MessageProps) => React.ReactNode;
  /**
  * 点赞点踩结果,good-点赞，bad-点踩
  */
  feedbackResult?: 'good' | 'bad';
  onFeedBack?: (score: MessageProps['feedbackResult'], message: MessageProps) => void;
  /**
  * log上报回调
  */
  onConsolidateReports?: (params: ILogParams) => void;
  /**
  * 复制文本回调
  */
  copyText?: (text: string, messageId?: MessageId) => void;
  /**
   * 引用文本回调
   */
  onQuote?: (message: MessageProps) => void;
  /**
   * 是否展示删除按钮
   */
  showDeleteMessage?: boolean;
  /**
   * 删除消息回调
   */
  handleDeleteMessage?: (message: MessageProps) => void;
  /**
  * 是否已停止生成
  */
  stopped?: boolean;
  /**
   * 未发送成功
   */
  unsend?: boolean;
  /**
  * 发送了以后获取答案异常（比如接口超时等）
  */
  sendError?: boolean;
  /**
   * 是否展示幻觉标识 aiMsgExtInfoJson缺失了dataSource二级属性 或者 为 "LLM"
   */
  showHallucination?: boolean;
  /**
  * 是否正在思考中
  */
  isThinking?: boolean;
  /**
   * 思考中的内容
   */
  thinkContent?: string;

  // 模方卡片用到的
  lowCodeConfig?: object;
  isDev?: boolean;
  userId?: string;
  // GuessAskMore卡片可能需要的发送
  onSend?: (type: string, content: string, params?: object) => Promise<boolean>;
  // GuessAskMore卡片可能需要的打开链接/markdown卡片上链接的打开
  openPage?: () => {};
  // 文件预览方法
  openFileViews?: () => {};
  // 参考资料
  references?: ReferenceProps[];
  // 参考资料配置项
  referencesConfig?: {
    title?: string | React.ReactNode | ((references: ReferenceProps[]) => React.ReactNode);
    modalWidth?: number;
  };
  // 是否展示反馈弹窗
  showFeedbackModal?: boolean;
  // 反馈标签
  feedbackLabels?: FeedbackLabelProps[];
  /**
    * 点踩反馈弹窗配置
    */
  feedbackModalConfig?: {
    title?: string,
    inputPlaceholder?: string,
    showLabels?: boolean,
  };
  // 对文字信息进行替换
  preprocessContent?: (content: string) => string;

  // 支持自定义“正在思考中”的样式
  customThinkingStyle?: React.ReactNode;

  // 支持自定义“深度思考中”的样式
  customDeepThinkingStyle?: React.ReactNode;
  // 支持自定义“深度思考中”的样式
  customThinkingCompleteStyle?:  React.ComponentType<{ thinkTime: number }>;
  // 自定义停止生成
  renderStopAnswer?: (msg: MessageProps) => React.ReactNode;
  // 思考结束是否自动折叠深度思考
  autoThinkCollapsed?: boolean;
  // 是否是宽屏
  isWide?: boolean;
  // 是否显示token信息
  showToken?: boolean;
  // 总token数
  totalTokens?: number;
  // 总耗时
  totalTime?: number;
  /**
   * 自定义引用渲染组件
   */
  renderReferencesContent?: React.ComponentType<{ references: ReferenceProps[] }>;
  // 是否展示引用
  showQuote?: boolean;
  // 点击删除的回调
  onDeleteMessage?: (msg: MessageProps) => void;
  // 幻觉标识文案
  hallucinationText?: string;
  // 思考耗时
  thinkTime?: number;
}

export type MessageWithoutId = Omit<MessageProps, '_id'> & {
  _id?: MessageId;
};

const Message = (props: MessageProps) => {
  const { 
    renderMessageContent,
    onFeedBack,
    onConsolidateReports,
    copyText,
    onQuote,
    showDeleteMessage = false,
    handleDeleteMessage = () => {},
    lowCodeConfig,
    onSend,
    openPage,
    openFileViews,
    isDev,
    userId,
    feedbackLabels,
    showFeedbackModal,
    feedbackModalConfig,
    referencesConfig,
    // feedbackArray,
    preprocessContent,
    renderStopAnswer,
    autoThinkCollapsed = false,
    isWide,
    renderReferencesContent: CustomRenderReferencesContent,
    onDeleteMessage,
    showQuote,
    hallucinationText,
    ...msg
  } = props;
  const {
    type,
    content,
    user = {},
    _id: id,
    position = 'left',
    hasTime = true,
    createdAt,
    isThinking,
    thinkContent = '',
    references = [],
    feedbackArray = ['copy', 'good', 'bad', 'quote'],
    customThinkingStyle,
    customDeepThinkingStyle,
    customThinkingCompleteStyle,
  } = msg;
  const { name, avatar } = user;

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // 实现多消息卡片类型模板
  const innerRenderMessageContent = useCallback(() => {
    // 根据消息类型来渲染
    switch (type) {
      case 'text':
      case 'richtext':
      case 'thinking':
      case 'markdown': //参考：https://blog.csdn.net/Sakuraaaa_/article/details/128400497
        if (content?.isCite) {
          return (
            <CitetextCard content={content} />
          );
        }
        const formatContent = getThinkContent(content?.text);
        return (
          <Bubble>
            <ThinkContent
              isThinking={(isThinking || formatContent?.isThinking) && !msg.stopped && !msg.sendError}
              thinkContent={thinkContent + formatContent?.thinkContent}
              autoThinkCollapsed={autoThinkCollapsed}
              customDeepThinkingStyle={customDeepThinkingStyle}
              customThinkingCompleteStyle={customThinkingCompleteStyle}
              thinkTime={msg.thinkTime}
            />
            {
              content?.text && (
                <MarkDown references={references} preprocessContent={preprocessContent} content={formatContent?.content} openPage={openPage} />
              )
            }
            {
              references?.length > 0 && CustomRenderReferencesContent ? <CustomRenderReferencesContent references={references} /> : (
                <References preprocessContent={preprocessContent} referencesConfig={referencesConfig} references={references} openPage={openPage} isWide={isWide} />
              )
            }
          </Bubble>
        );
      case 'relatedQuestionList':
        return (
          <Card fluid>
            {/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
            <GuessAskMore questionList={content.relatedQuestionList} onSend={onSend} openPage={openPage} />
          </Card>
        );
      case 'file':
        return (
          <>
            <Bubble type="file">
              <FileCard file={content} openFileViews={openFileViews} />
            </Bubble>
          </>
        );
      case 'image':
        return (
          <>
            <Bubble type="image">
              <AiImage src={content.picUrl} />
            </Bubble>
          </>
        );
      case 'video':
        return (
          <Bubble type="video">
            <Video src={content?.url} />
          </Bubble>
        );
      case 'card':
        return (
          <Bubble type="card">
            <Lowcode
              content={content}
              isDev={isDev}
              userId={userId}
              lowCodeConfig={lowCodeConfig}
            />
          </Bubble>
        );
      case 'web':
      case 'citeweb':
        return (
          <CitewebCard content={content} openPage={openPage} />
        );
      case 'citetext':
        return (
          <CitetextCard content={content} />
        );
      default:
        return null;
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content, isDev, isThinking, lowCodeConfig, msg.sendError, msg.stopped, onSend, openFileViews, openPage, references, thinkContent, type, userId, preprocessContent, customDeepThinkingStyle]);

   const renderNotTyping = () => {
    if (type !== 'typing') {
      if (renderMessageContent && renderMessageContent(msg)) {
        return renderMessageContent(msg);
      }
      return innerRenderMessageContent();
    }
    return null;
  };
  
  if (type === 'system') {
    return <SystemMessage content={content.text} action={content.action} />;
  }

  const isRL = position === 'right' || position === 'left';
  
  const handleDeleteConfirm = () => {
    setShowDeleteModal(false);
    return handleDeleteMessage && handleDeleteMessage(msg);
  };

  return (
    <div className={clsx('Message', position)} data-id={id} data-type={type}>
      {hasTime && createdAt && (
        <div className="Message-meta">
          <Time date={createdAt} />
        </div>
      )}
      <div className="Message-main">
        {isRL && avatar && <Avatar className='Message-avatar-img' src={avatar} alt={name} url={user.url} />}
        <div className="Message-inner">
          {isRL && name && <div className="Message-author">{name}</div>}
          <div className="Message-content" role="alert" aria-live="assertive" aria-atomic="false">
            {
              type === 'typing' && 
              (customThinkingStyle ?
               <Bubble type="typing">{customThinkingStyle}</Bubble>
                : <Typing />)
            }
            {renderNotTyping()}
          </div>
          {msg?.stopped && (renderStopAnswer ? renderStopAnswer(msg) : <div className="Message-stopped">已停止生成</div>)}
          {msg?.sendError && <div className="Message-sendError"> <img src={WarningIcon} />答案生成异常，已被终止</div>}
          <HallucinationMarker showHallucination={msg?.showHallucination} hallucinationText={hallucinationText} />
          <div className="feedbackWrapper">
            <Feedback
              message={msg}
              onFeedBack={onFeedBack}
              onConsolidateReports={onConsolidateReports}
              copyText={copyText}
              onQuote={onQuote}
              feedbackLabels={feedbackLabels}
              showFeedbackModal={showFeedbackModal}
              // 反馈弹窗自定义配置
              feedbackModalConfig={feedbackModalConfig}
              feedbackArray={feedbackArray}
            />
            {/* 删除消息 */}
            {showDeleteMessage && (
              <div className="messageDelete" onClick={() => setShowDeleteModal(true)}>
                <img src={DeleteIcon} alt="delete" className='deleteIcon' />
              </div>
            )}
            {showDeleteMessage && showDeleteModal && (
              <DeleteModal
                visible={showDeleteModal}
                title="是否删除该条消息？"
                content={'删除后，聊天记录不可恢复，对话内的文件也将被彻底删除。'}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
              />
            )}
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default React.memo(Message);

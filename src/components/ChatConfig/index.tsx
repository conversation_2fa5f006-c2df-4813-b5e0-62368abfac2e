/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useImperativeHandle, useState } from "react";
import clsx from "clsx";
import { Tooltip } from "../Tooltip";
import { Input } from "../Input";
import { Select } from "../Select";
import ItemName from "./ItemName";
import InfoIcon from './images/infoIcon.svg';
import MsgIcon from './images/msgIcon.svg';
import EditIcon from './images/editIcon.svg';

interface InputsProps {
    err?: boolean,
    Key: any,
    Name: string,
    Required?: boolean,
    VariableType: string,
    EnumValues?: string[],
    TextMaxLength?: number,
    Value?: any,
}

export interface ChatConfigProps {
    title?: string,
    isNewConversation?: boolean,
    onChange?: (e: any) => void,
    VariableConfigs: InputsProps[],
}
export const ChatConfig = React.forwardRef((props: ChatConfigProps, ref) => {
    const { title, isNewConversation, VariableConfigs } = props;
    const [isEdit, setIsEdit] = useState(isNewConversation);
    // 真实确定应用的值
    const [realInputs, setRealInputs] = useState(VariableConfigs);

    // 临时变量，用于存放输入框的值
    const [tempInputs, setTempInputs] = useState(VariableConfigs);

    useEffect(() => {
        setIsEdit(isNewConversation);
        setTempInputs([...VariableConfigs]);
    }, [isNewConversation])

    const handleInputChange = (e: any, item: InputsProps) => {
        const newItem = { ...item, Value: e };
        if (item?.err && e) {
            newItem.err = false;
        }
        const newTempInputs = [...tempInputs];
        const index = tempInputs.findIndex((i: { Key: any; }) => i.Key === item.Key);
        if (index !== -1) {
            newTempInputs[index] = newItem;
        }
        setTempInputs(newTempInputs);
    }

    const handleCancelBtn = () => {
        setTempInputs(realInputs);
        if (!isNewConversation) {
            setIsEdit(false);
        }
    }

    const checkInputs = (list: InputsProps[]) => {
        const errors = list.filter(
            item => item?.Required && (item?.Value === '' || item?.Value === null || item?.Value === undefined)
        )?.map(item => `${item?.Key}为必填项`)
        return { valid: errors.length === 0, errors };
    }
    const handleOkBtn = () => {
        const newList = tempInputs?.map(item => {
            const isMissing = item?.Required && (item?.Value === '' || item?.Value === null || item?.Value === undefined)
            return {
                ...item,
                err: !!isMissing
            }
        })
        setTempInputs(newList);
        if (!checkInputs(tempInputs).valid) {
            return;
        }
        setRealInputs(tempInputs);
        setIsEdit(false);
        // onChange && onChange(tempInputs);
    }

    const handleEdit = () => {
        setIsEdit(true);
    }
    const getData = () => {
        const formatData = Object.fromEntries(realInputs?.map(item => [item?.Key, item?.Value]));
        return formatData || {};
    }

    useImperativeHandle(ref,
        () => ({
            getData
        }),
        [getData],);

    return (
        <div className="ChatConfig">
            {isEdit ?
                <div className={clsx(isNewConversation && 'ChatConfig-isNew', "ChatConfig-editing")}>
                    <div className='ChatConfig-mask'></div>
                    <div className="ChatConfig-Container">
                        <div className="ChatConfig-Title">{title || ''}</div>
                        <div className="ChatConfig-Body">
                            <div className="ChatConfig-Content">
                                {tempInputs?.map((item: InputsProps, index: number) => {
                                    return (<>
                                        <div key={index} className="ChatConfig-Item">

                                            <div className="ChatConfig-ItemTitle">
                                                <div className="ChatConfig-TitleText" >
                                                    {item?.Required && <span className="ChatConfig-Required">*</span>}
                                                    <ItemName id={item?.Key} name={item?.Key} />

                                                </div>

                                                {item?.Name && <div className="ChatConfig-Instru">
                                                    <Tooltip content={item?.Name} placement={'top'}>
                                                        <img src={InfoIcon} alt="" className="ChatConfig-InstruIcon" />
                                                    </Tooltip>
                                                </div>}
                                            </div>

                                            {item?.VariableType === 'Text' && <Input
                                                className={item?.err ? "ChatConfig-inputErr" : "ChatConfig-input"}
                                                placeholder="请输入"
                                                onChange={(e) => handleInputChange(e, item)}
                                                value={item?.Value || ''}
                                                maxLength={item?.TextMaxLength}
                                                showCount={false}
                                            />}
                                            {item?.VariableType === 'Enum' && <Select
                                                className={item?.err ? "ChatConfig-inputErr" : "ChatConfig-input"}
                                                value={item?.Value || undefined}
                                                placeholder="请选择"
                                                onChange={(e) => handleInputChange(e?.target?.value, item)}
                                            >
                                                {item?.EnumValues?.map((it, idx) => {
                                                    return (
                                                        <option key={idx} value={it}>{it}</option>
                                                    )
                                                })}
                                            </Select>}
                                            {item?.VariableType === 'Paragraph' && <Input
                                                className={item?.err ? "ChatConfig-textAreaErr" : "ChatConfig-textArea"}
                                                placeholder="请输入"
                                                onChange={(e) => handleInputChange(e, item)}
                                                value={item?.Value || ''}
                                                rows={2}
                                                autoSize
                                                maxLength={item?.TextMaxLength}
                                                showCount={false}
                                            />
                                            }
                                        </div>
                                        {item?.err && <div className="ChatConfig-ErrorInstru">{item?.Key}是必填项</div>}
                                    </>)

                                })}
                            </div>
                            <div className="ChatConfig-Footer">
                                {isNewConversation ?
                                    <div className="ChatConfig-OkButton" onClick={handleOkBtn}>
                                        开始会话
                                    </div> :
                                    <>
                                        <div className="ChatConfig-CancelButton" onClick={handleCancelBtn}>
                                            取消
                                        </div>
                                        <div className="ChatConfig-OkButton" onClick={handleOkBtn}>
                                            确定
                                        </div>
                                    </>
                                }

                            </div>
                        </div >
                    </div>

                </div > : <div className="ChatConfig-show">
                    <div className="ChatConfig-ShowMsg">
                        <img src={MsgIcon} alt="" className="ChatConfig-MsgIcon" />
                        <span>您可以修改对话设置</span>
                    </div>

                    <div className="ChatConfig-EditBtn" onClick={handleEdit}>
                        <img src={EditIcon} alt="" className="ChatConfig-EditIcon" />
                        编辑
                    </div>
                </div>}
        </div>


    )
})

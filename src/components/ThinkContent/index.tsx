import React, { useState, useEffect } from 'react';
import { MarkDown } from '../MarkDown';
import { LoadingSpinner } from '../LoadingSpinner';
import { ThinkContentProps } from './interface';

import upSrc from './images/up.svg'
import downSrc from './images/down.svg'

export const ThinkContent = (props: ThinkContentProps) => {
  const {
    isThinking = false,
    thinkContent = '',
    customDeepThinkingStyle,
    autoThinkCollapsed = false,
    customThinkingCompleteStyle: CustomThinkingCompleteContent,
    thinkTime = 0,
  } = props;

  const [isThinkCollapsed, setIsThinkCollapsed] = useState(false);

  const toggleThinkCollapse = () => {
    setIsThinkCollapsed(!isThinkCollapsed);
  }

  useEffect(() => {
    if (autoThinkCollapsed) {
      setIsThinkCollapsed(!isThinking);
    }
  }, [isThinking, autoThinkCollapsed]);

  if (!thinkContent) {
    return null;
  }

  return (
    <div className={`ThinkContentWrap ${thinkContent !== '' && !isThinkCollapsed ? 'isExistThinkContent' : ''}`}>
      {
        isThinking
          ? (
            <div className="Thinking">
              {customDeepThinkingStyle ? customDeepThinkingStyle : (
                <>
                  <div className="ThinkingText">思考中</div>
                  <LoadingSpinner />
                </>
              )}
            </div>
          )
          : (
            <div className={`ThinkingCompleted ${isThinkCollapsed ? 'isThinkingCollapsedCompleted' : ''}`} onClick={toggleThinkCollapse}>
              {isThinking ? '思考已停止' : (CustomThinkingCompleteContent ? <CustomThinkingCompleteContent thinkTime={thinkTime} /> : '已深度思考')}
              {isThinkCollapsed
                ? <img src={upSrc} className="CollapsedIcon" />
                : <img src={downSrc} className="CollapsedIcon" />
              }
            </div>
          )
      }
      <div className="ThinkContent">
        {!isThinkCollapsed && (
          <MarkDown content={thinkContent} disableLink={true} />
        )}
      </div>
    </div>
  );
};
---
nav:
  title: 组件库
  order: -1
group:
  title: 核心对话组件
  order: 20
order: 2
---

# Message 消息组件

Message 组件是一个用于展示聊天消息的组件，支持多种消息类型，包括文本、图片、文件、视频等。它可以根据消息的位置（左侧或右侧）自动调整样式，并支持头像、姓名、时间等信息的显示。

## 基础文本消息

基本的文本消息展示，支持左侧和右侧布局。

<code src="./demo-basic.tsx"></code>

## 带用户信息的消息

展示带有用户头像、名称和时间的消息。

<code src="./demo-user-info.tsx"></code>

## 图片消息

支持展示图片类型的消息。

<code src="./demo-image.tsx"></code>

## 带反馈功能的消息

支持点赞点踩等反馈功能的消息，反馈功能报错：复制、点赞、点踩，用户可以指定需要的功能。

<code src="./demo-feedback.tsx"></code>

## 系统消息

系统通知类型的消息，通常显示在中间位置。

<code src="./demo-system.tsx"></code>

#### 支持用户自定义思考中样式

<code src="./demo-thing-style.tsx"></code>

#### 含有思考过程和参考来源消息  &  关联问题列表

<code src="./demo-references.tsx"></code>

## API

### Message Props

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| _id | `string \| number` | - | 消息唯一ID（必填） |
| messageId | `string \| number` | - | 用于回传给后端的消息ID |
| type | `string` | - | [消息类型](http://sckit.sit.saas.htsc/components/Message#消息类型) |
| content | `object` | - | [消息内容](http://sckit.sit.saas.htsc/components/Message#消息内容) |
| position | `'left' \| 'right' \| 'center' \| 'pop'` | `'left'` | 消息位置 |
| user | `object` | `{}` | 消息发送者信息，包含 avatar 和 name |
| createdAt | `number` | - | 消息创建时间戳 |
| hasTime | `boolean` | `true` | 是否显示时间 |
| feedbackArray | `array` | - | 指定需要的反馈操作 |
| needFeedback | `boolean` | `false` | 是否需要点赞、点踩的功能，如果是false则只有复制功能（兼容老版本，推荐使用`feedbackArray`字段） |
| copyText | `function` | - | 复制文本回调 |
| onFeedBack | `function` | - | 点赞点踩回调 |
| showHallucination | `boolean` | `false` | 是否显示幻觉标识 |
| config | `object` | - | 配置信息（必填） |
| updateMsg | `function` | - | 更新消息的回调函数（必填） |
| customThinkingStyle | `ReactNode` | - | 支持自定义“正在思考中”的样式 |
| customDeepThinkingStyle | `ReactNode` | - | 支持自定义“深度思考中”的样式 |
| onReportLog |`(payload:` [ILogParams](/guide/log-intro)`) => void` | - |  埋点上报方法 | 
| autoThinkCollapsed | `boolean` | false |  思考结束时是否自动折叠思考内容 | 
| hallucinationText | `string` | - |  自定义幻觉标识文案 | 


### 消息类型

Message 组件支持多种消息类型，通过 `type` 属性指定，具体包括：

- `text`: 文本消息
- `image`: 图片消息
- `file`: 文件消息
- `video`: 视频消息
- `card`: 卡片消息
- `system`: 系统消息
- `typing`: 输入中状态
- `markdown`: Markdown 格式消息
- `richtext`: 富文本消息
- `thinking`: 思考中消息
- `relatedQuestionList`: 关联问题列表
- `card`: 卡片消息，该类型需要自定义开发

### 消息内容

根据不同的消息类型，`content` 对象的结构会有所不同：

#### 文本消息

```
{
  text: '这是一条文本消息'
}
```

#### 图片消息

```
{
  picUrl: 'https://example.com/image.jpg'
}
```

#### 文件消息

```
{
  fileName: '文档.pdf',
  fileSize: 1024,
  fileUrl: 'https://example.com/document.pdf'
}
```

#### 视频消息

```
{
  url: 'https://example.com/video.mp4'
}
```
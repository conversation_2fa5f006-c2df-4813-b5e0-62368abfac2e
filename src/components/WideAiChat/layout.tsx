import React from 'react';
import { WideAiChatProps } from './interface';
import { HistoryConversation } from '../HistoryConversation';
import { ChatContextProps } from '../../hooks/useChatContext';

export const WideAiChatLayout = (
  props: WideAiChatProps & { children: React.ReactNode; chatContext: ChatContextProps | undefined },
) => {
  const { children, renderHistoryConversation: CustomSider, chatContext, config, historyConversation } = props;
  let sideBar;
  if (CustomSider) {
    const localProps = {
      ...historyConversation,
      config,
      chatContext,
    };
    sideBar = <CustomSider {...localProps} />;
  } else {
    const { isNewConversation, handleNewConversation, toggleHistory, historyConversationList, conversationId, selectHistoryConversation } = chatContext || {};
    const conversationNavbarProps  = {
      isNewConversation: isNewConversation, // 历史会话页默认传false
      onNewButtonClick: () => {
        handleNewConversation();
      },
      onCloseButtonClick: () => {
        toggleHistory();
      },
      showCloseButton: false,
    }
    sideBar = (
      <HistoryConversation
        standaloneMode={false}
        style={{ height: 'calc(100% - 6px)', width: 'calc(100% - 6px)', margin: 0 }}
        title={historyConversation?.title}
        logo={historyConversation?.logo || config?.robot?.logo || ''}
        navbar={conversationNavbarProps}
        list={historyConversationList || []}
        activeConversationId={conversationId?.current}
        onConversationClick={(e: any) => selectHistoryConversation(e)}
        renderBrand={() => historyConversation?.renderBrand?.()}
        renderFooter={historyConversation?.renderFooter}
        renderListItem={historyConversation?.renderListItem}
        showSearch={historyConversation?.showSearch}
      />
    );
  }
  return (
    <div className={`WideAiChatLayout ${!sideBar ? 'onlyChatContainer' : 'normal'}`}>
      <div className="WideAiChatSider">{sideBar}</div>
      <div className="WideAiChatContent">{children}</div>
    </div>
  );
};

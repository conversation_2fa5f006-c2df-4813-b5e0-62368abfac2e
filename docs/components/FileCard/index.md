---
nav:
    title: 组件库
    order: -1
group:
    title: 基础卡片组件
    order: 10
order: 1
---

# FileCard 文件卡片

## 代码演示

### 基本

<code src="./demo-basic.tsx" ></code>

###  指定拓展名

<code src="./demo-basic-extension.tsx" ></code>

### 额外内容

<code src="./demo-basic-extra-content.tsx" ></code>

### 不支持预览

<code src="./demo-basic-preview-not.tsx" ></code>

## 参数

### FileCard

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| className | 类名 | `string` | - |
| file | 图片（必填{name,url,size}） | `object` | - |
| extension | 扩展名 | `string` | - |
| children | 额外内容 | `React.Node`| - |
| preview | 是否支持预览 | `boolean` | `true` |
| openFileViews | 点击文件 |`(url:string) => void` | - | 
---

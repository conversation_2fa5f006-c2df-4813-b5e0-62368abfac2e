.CustomWelcome {
  padding: 24px 24px 24px 24px;
  > .title {
    height: 25px;
    font-size: 18px;
    font-weight: 500;
    color: #000000;
    line-height: 25px;
    margin-bottom: 10px;
  }
  > .card {
    height: 112px;
    background: rgba(217, 217, 217, 0.2);
    border-radius: 2px;
    padding: 8px 10px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    > .header {
      display: flex;
      margin-bottom: 16px;
      height: 20px;
    width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      > .title {
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #25396f;
        line-height: 20px;
      }

      > .extra {
        span {
          height: 20px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007aff;
          line-height: 20px;
        }
      }
    }
    > .content {
      > .list {
        > .item {
          height: 30px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007aff;
          line-height: 30px;
        }
      }
    }
  }
}

import React from 'react';
import type {
  InputCard as tInputCard,
  InputContent,
  InputFile,
  InputImage,
  InputText,
  InputType,
  InputWeb,
} from '../type';
import './style.less';
import deleteSvg from './svg/delete.svg';
import { getFileTypeIcon } from '../../../../utils/getFileTypeIcon';

export interface InputCardProps extends tInputCard {
  onDelete?: (card: tInputCard) => void;
}
export default function InputCard(props: InputCardProps) {
  const { type, isCite, content, onDelete } = props;

  const onDeleteClick = (card: InputCardProps) => {
    if (onDelete) {
      onDelete(card);
    }
  };

  const RenderCiteTitle = (bCite: boolean, inputType: InputType) => {
    if (!bCite) {
      return null;
    }
    let title = '';
    switch (inputType) {
      case 'text': {
        title = '文本';
        break;
      }
      case 'web': {
        title = '网址';
        break;
      }
      case 'image': {
        title = '图片';
        break;
      }
      case 'file': {
        title = '文件';
        break;
      }
      default: {
        break;
      }
    }
    if (title) {
      return <div className="citeTitle">{`引用${title}`}</div>;
    }
    return null;
  };

  const RenderText = (textContent: InputText) => {
    return <div className="contentText">{textContent}</div>;
  };

  const RenderWeb = (webContent: InputWeb) => {
    return (
      <div className="contentWeb">
        <img className="favicon" src={webContent.favicon} />
        <div>
          <span className="title">{webContent.title}</span>
          <span className="url">{webContent.url}</span>
        </div>
      </div>
    );
  };

  const RenderImage = (imageContent: InputImage) => {
    return imageContent?.url && <img className="contentImage" src={imageContent.url} />;
  };

  const RenderFile = (fileContent: InputFile) => {
    const defaultIcon = getFileTypeIcon(fileContent?.name);
    return (
      <div className="contentFile">
        <img className="icon" src={fileContent.icon || defaultIcon} />
        <div>
          <span className="name">{fileContent.name}</span>
          <span className="size">{fileContent.size} byte</span>
        </div>
      </div>
    );
  };

  const RenderContent = (inputType: InputType, inputContent: InputContent) => {
    switch (inputType) {
      case 'text': {
        return RenderText(inputContent as InputText);
      }
      case 'web': {
        return RenderWeb(inputContent as InputWeb);
      }
      case 'image': {
        return RenderImage(inputContent as InputImage);
      }
      case 'file': {
        return RenderFile(inputContent as InputFile);
      }
      default: {
        return null;
      }
    }
  };

  return (
    <div
      className="inputCard"
      style={{
        width: type === 'image' ? (isCite ? '88px' : '58px') : '236px',
        height: isCite && type !== 'text' ? '88px' : '58px',
      }}
    >
      <img
        className="deleteIcon"
        src={deleteSvg}
        onClick={() => onDeleteClick(props)}
      />
      <div
        className="content"
        style={{
          padding: type === 'image' && !isCite ? '0' : '7px 12px',
        }}
      >
        {RenderCiteTitle(isCite, type)}
        {RenderContent(type, content)}
      </div>
    </div>
  );
}

.QuickReplies {
  // position: absolute;
  // left: 0;
  // right: 0;
  // z-index: @zindex-quick-replies;
  // bottom: 100%;
  overflow: hidden;
  padding: @quick-replies-padding;
  background: transparent;
  transition: opacity 0.3s;
  font-size: 16px;

  &[data-visible='false'] {
    visibility: hidden;
    opacity: 0;
  }

  &:not(.ScrollView--hasControls) {
    padding-left: 0;
    padding-right: 0;

    .ScrollView-inner {
      padding: 0 15px;
    }
    .ScrollView-item:last-child {
      padding-right: @gutter;
    }
  }
}

.QuickReply {
  position: relative;
  margin: 0;
  padding: 7px 10px;
  border: 1px solid transparent;
  border-radius: 8px;
  line-height: 20px;
  background: @quick-reply-bg;
  cursor: pointer;
  transition: 0.15s ease-in-out;
  border-radius: 8px;
  border: 1px solid rgba(204, 204, 204, 0.4);
  font-size: 14px;
  font-weight: 500;
  color: @quick-reply-font-color;

  &.new::after {
    content: '';
    position: absolute;
    top: @quick-reply-dot-top;
    right: @quick-reply-dot-right;
    width: @quick-reply-dot-size;
    height: @quick-reply-dot-size;
    background: @quick-reply-dot-bg;
    overflow: hidden;
    border: 1px solid #fff;
    border-radius: 50%;
  }
  &.highlight {
    background: #fff;
    border-color: #ffd0bf;
    font-weight: 500;
  }
  &-inner {
    display: flex;
    align-items: center;
  }
  &-img {
    max-height: 16px;
  }
  &-inner > .Icon,
  &-img {
    margin-right: 3px;
  }
  .Icon {
    color: @brand-1;
    font-size: 15px;
  }
}

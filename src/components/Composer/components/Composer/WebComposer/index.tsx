import { Input } from '@ht/sprite-ui';
import React from 'react';
import type { ComposerProps } from '../../type';

import InputCardList from '../../InputCardList';

import SendButton from '../components/SendButton';
import CloseSvg from '../svg/close.svg';
import AddWebIcon from './addWeb.svg';
import DefaultIcon from './defaultIcon.svg';
import './style.less';

export interface WebComposerProps extends ComposerProps {
  onAddWeb: () => void; // 添加添加网页回调
  [key: string]: any; // 其他扩展属性
}
export default function WebComposer(props: WebComposerProps) {
  const {
    icon,
    title,
    onclose,
    text = '',
    setText,
    inputCardList = [],
    // setInputCardList,
    onSend,
    onAddWeb,
    ...rest
  } = props;

  const onSendBtnClick = async () => {
    console.log('WebComposer.onSend');
    onSend();
  };

  const onInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  //   const onAddWeb = () => {
  //     const testWebList: InputCardProps[] = [
  //       {
  //         type: 'web',
  //         isCite: false,
  //         content: {
  //           title: 'web-HTSC',
  //           url: 'http://eip.htsc.com.cn/htscPortal/home',
  //           favicon: 'http://eip.htsc.com.cn/htscPortal/favicon.ico',
  //         },
  //       },
  //       //   {
  //       //     type: 'web',
  //       //     isCite: false,
  //       //     content: {
  //       //       title: 'Baidu',
  //       //       url: 'https://www.baidu.com/',
  //       //       favicon: 'https://www.baidu.com/favicon.ico',
  //       //     },
  //       //   },
  //     ];
  //     setInputCardList((old) => old.concat(testWebList));
  //   };

  // const onDelete = (index: number) => {
  //   const newArr = Array.from(inputCardList);
  //   newArr.splice(index, 1);

  //   if (setInputCardList) {
  //     setInputCardList(newArr);
  //   }
  // };

  return (
    <div className="translateComposer">
      <div className="header">
        <div className="title">
          {icon || <img src={DefaultIcon} />}
          <span>{title}</span>
        </div>
        <div className="handle">
          <div className="addWeb" onClick={onAddWeb}>
            <img src={AddWebIcon} />
            <span>添加当前网页</span>
          </div>
          <div style={{ width: '24px' }}> </div>
          <img className="close" src={CloseSvg} onClick={onclose} />
        </div>
      </div>
      <div className="content">
        {inputCardList && inputCardList.length > 0 && (
          <div className="webList">
            <InputCardList
              inputCardList={inputCardList}
              operationConfig={{
                text: [],
                web: [],
                image: [],
                file: []
              }}
              // onDelete={onDelete}
            />
          </div>
        )}
        <Input.TextArea {...rest} rows={3} value={text} onChange={onInputChange} />
      </div>
      <div className="footer">
        <SendButton hasContent={!!text.trim()} onClick={onSendBtnClick} />
      </div>
    </div>
  );
}

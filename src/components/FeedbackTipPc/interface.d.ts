export interface FeedbackLabelProps {
  id: string,
  label: string
}

export interface FeedbackTipPcProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 标签
   */
  labels?: FeedbackLabelProps[];

  /**
   * 关闭弹窗
   */
  onClose?: () => void;
  /**
   * 提交操作函数
   */
  onSubmit?: (eTag?:any, eValue?:any) => void;
 /**
   * 弹窗标题
   */
  title?: string;
 /**
   * 弹窗输入框placeholder
   */
  inputPlaceholder?: string;
 /**
   * 弹窗是否展示标签区域
   */
  showLabels?: boolean;
}

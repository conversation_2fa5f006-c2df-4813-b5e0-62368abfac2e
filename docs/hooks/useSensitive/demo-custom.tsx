/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React, { useCallback } from 'react';
import { Button, useSensitive } from '@ht/chatui';

export default () => {

  const { 
    data: sensitiveData,
    loading: sensitiveLoading,
    run: submitSensitive 
  } = useSensitive({
    // 请求配置
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      type: 'http',
      manual: true,
      headers: { 
        empid: '002332', 
        token: 'token', 
        'iv-user': '002332', 
        'deviceId': 'deviceId' 
      },
    },
    // 请求参数
    requestParams: {
      question: '中国共产党',
    },
  });

  // 处理提交反馈
  const handleSubmitSensitive = useCallback(() => {
    submitSensitive();
  }, [submitSensitive]);

  return (
    <div>
      <Button 
          onClick={handleSubmitSensitive} 
          loading={sensitiveLoading}
        >
          敏感词校验
        </Button>
        {sensitiveData ? (
          <div style={{ color: sensitiveData?.resultData.ifPass ? 'green' : 'red', marginTop: '8px' }}>
          {sensitiveData?.resultData?.msg}: {sensitiveData?.resultData?.data}
        </div>
        ) : ''}
    </div>
    )
}
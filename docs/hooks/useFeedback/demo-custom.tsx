/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useFeedback } from '@ht/chatui';

export default () => {

  const { loading: conversationLoading, run: conversationRun } = useFeedback({
    // 请求配置
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
      type: 'http',
      manual: true,
      headers: { 
        empid: '002332', 
        token: 'token', 
        'iv-user': '002332', 
        'deviceId': 'deviceId' 
      },
    },
    // 请求参数
    requestParams: {
      userId: '002332',
      conversationId: '16ba2e40-4f4f-4dbb-b690-d1440b6b99d0',
      messageId: '0217436762676676772e928cf7b101d9838bd97851ecdae1d7d2f',
      score: 'good',
      type: 'submit', // 接口定义用的是type而不是submitType
    },
  });

  return (
    <div>
      <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>回答反馈评价</Button>
    </div>
    </div>
    )
}
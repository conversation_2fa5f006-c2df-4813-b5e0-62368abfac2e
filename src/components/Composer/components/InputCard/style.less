.inputCard {
  position: relative;
  width: 236px;
  height: 58px;
  flex-shrink: 0;

  /* 圆角 */
  border-radius: 8px;

  /* 中性色/0%-FFFFFF */
  background: #ffffff;
  box-sizing: border-box;

  /* 中性色/6%-F6F6F7 */
  border: 1px solid #f2f2f3;

  .deleteIcon {
    position: absolute;
    width: 16px;
    height: 16px;
    top: -5.5px;
    right: -5.5px;
    cursor: pointer;
    z-index: 2;
  }

  .content {
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    position: absolute;
    top: 0;
    left: 0;
    padding: 7px 12px;
    z-index: 1;

    .citeTitle {
      height: 22px;
      max-width: 100%;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: bold;
      line-height: 22px;
      letter-spacing: 0;
      font-variation-settings: "opsz" auto;

      /* 中性色/100%-1D222C */
      color: #1d222c;
    }
    .contentText {
      max-width: 100%;
      max-height: 100%;
      height: 22px;
      overflow: hidden; /* 隐藏溢出的内容 */
      white-space: nowrap;
      text-overflow: ellipsis; /* 溢出内容显示为省略号 */
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0;
      font-variation-settings: "opsz" auto;

      /* 中性色/65%-6C6F76 */
      color: #6c6f76;
    }

    .contentWeb {
      max-width: 100%;
      max-height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      .favicon {
        width: 36px;
        height: 36px;
      }
      div {
        max-width: 168px;
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;

        .title {
          max-width: 100%;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
          letter-spacing: 0;
          color: #1d222c;
          overflow: hidden; /* 隐藏溢出的内容 */
          white-space: nowrap;
          text-overflow: ellipsis; /* 溢出内容显示为省略号 */
        }
        .url {
          max-width: 100%;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          letter-spacing: 0;
          color: #6c6f76;
          overflow: hidden; /* 隐藏溢出的内容 */
          white-space: nowrap;
          text-overflow: ellipsis; /* 溢出内容显示为省略号 */
        }
      }
    }

    .contentImage {
      // max-width: 58px;
      // max-height: 58px;
      // object-fit: contain;
      border-radius: 8px;
      border: 1px solid #f2f2f3;
      // 以下兼容长图展示
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
    }

    .contentFile {
      max-width: 100%;
      max-height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      .icon {
        width: 36px;
        height: 36px;
      }
      div {
        max-width: 168px;
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;

        .name {
          max-width: 100%;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
          letter-spacing: 0;
          color: #1d222c;
          overflow: hidden; /* 隐藏溢出的内容 */
          white-space: nowrap;
          text-overflow: ellipsis; /* 溢出内容显示为省略号 */
        }
        .size {
          max-width: 100%;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          letter-spacing: 0;
          color: #6c6f76;
          overflow: hidden; /* 隐藏溢出的内容 */
          white-space: nowrap;
          text-overflow: ellipsis; /* 溢出内容显示为省略号 */
        }
      }
    }
  }
}

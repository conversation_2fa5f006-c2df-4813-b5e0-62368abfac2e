import React from 'react';
import SendIcon from '../images/send.svg';
import SendDisabledIcon from '../images/sendDisabled.svg';
import { useLocale } from '../../LocaleProvider';

interface SendButtonProps {
  disabled?: boolean;
  onSendClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  showLoading?: boolean;
  onStopClick?: () => void;
}

export const SendButton = ({
  disabled,
  onSendClick,
  onStopClick,
  showLoading,
}: SendButtonProps) => {
  const { trans } = useLocale('Composer');
  const imgSrc = disabled ? SendDisabledIcon : SendIcon;
  return (
    <div className="Composer-actions">
      {showLoading ? (
        <div className="Composer-StopAnswerIcon" onClick={onStopClick}>
          <div className="Composer-StopAnswerIcon-inner"></div>
        </div>
      ) : (
        <div className="Composer-SendIconWrap">
          <img src={imgSrc} alt={trans('send')} className="Composer-SendIcon" />
          <div onClick={onSendClick} className="Composer-SendIconClick" />
        </div>
      )}
    </div>
  );
};

---
nav:
  title: 组件库
  order: -1
group:
  title: 核心对话组件
  order: 10
order: 2
---

# Composer 输入框组件

## 代码演示

### 窄屏或移动端

#### 基础用法

<code src="./demo-compact-base.tsx" ></code>

#### 停止生成

设置 `showStopAnswer` 为 `true` 时，显示停止按钮。点击停止按钮触发 `onStopAnswer` 回调。

<code src="./demo-compact-stop.tsx" ></code>

#### 联网搜索

设置 `showInternetSearch` 为 `true` 时，显示联网搜索按钮。点击联网搜索按钮触发 `onInternetSearch` 回调。

<code src="./demo-compact-net.tsx" ></code>

#### 插槽

对话框上、下侧插槽

<code src="./demo-compact-slot.tsx" ></code>

### PC 宽屏

#### 基础用法

设置 `isWide` 为 `true` 时，显示宽屏模式。

<code src="./demo-wide-base.tsx" ></code>

#### 停止生成

<code src="./demo-wide-stop.tsx" ></code>

#### 联网搜索

<code src="./demo-wide-net.tsx" ></code>

#### 插槽

输入框上、下侧插槽

<code src="./demo-wide-slot.tsx" ></code>

#### 文件上传

<code src="./demo-wide-upload.tsx" ></code>

#### 文件上传提示

通过 `popoverConfig` 配置文件上传提示。

<code src="./demo-wide-upload-popover.tsx" ></code>

#### 自定义上传方法

通过 `customRequest` 自定义上传方法。

<code src="./demo-wide-upload-customRequest.tsx" ></code>

## 参数

### ComposerProps

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| style | 容器样式 | `any` | - |
| isWide | 是否宽屏模式 | `boolean` | false |
| text | 输入框文本 | `string` | - |
| inputOptions | 输入框选项 | `InputProps` | - |
| placeholder | 占位文本 | `string` | 请输入您的问题 |
| inputType | 输入框类型 | `InputType` | `text` |
| onInputTypeChange | 输入框类型变化时的回调 | `(inputType: InputType) => void` | - |
| recorder | - | `RecorderProps` | - |
| onSend | 发送问题时的回调 | `(type: string, content: string, payload?: object) => Promise<boolean>` | - |
| onFocus | 输入框获取焦点 | `(event: React.FocusEvent<HTMLTextAreaElement>) => void` | - |
| onChange | 输入框内容变化 | `(value: string, event: React.ChangeEvent<Element>) => void` | - |
| onBlur | 输入框失去焦点 | `(event: React.FocusEvent<HTMLTextAreaElement>) => void` | - |
| extraAction | - | `React.ReactNode` | - |
| showStopAnswer | 显示停止按钮 | `RecorderProps` | - |
| onStopAnswer | 点击停止按钮 | `RecorderProps` | - |
| showInternetSearch | 显示网络搜索 | `RecorderProps` | false |
| enableInternetSearch | 激活网络搜索 | `RecorderProps` | false |
| toggleEnableInternetSearch | 切换网络搜索状态 | `RecorderProps` | - |
| llmConfig | 大模型选项配置，非 `undefined` 时显示模型切换 | `LlmConfig` | - |
| uploadConfig | 文件上传配置，非 `undefined` 时显示文件上传按钮 | `UploadConfig` | - |
| aboveNode | 输入框上方插槽 | `React.ReactNode` | - |
| belowNode | 输入框下方插槽 | `React.ReactNode` | - |

### LlmConfig

| 参数        | 类型                                          | 默认值 | 说明         |
| ----------- | --------------------------------------------- | ------ | ------------ |
| selectProps | `SelectProps`                                 | -      | select 选项  |
| llmOptions  | `{ value: string , number; label: string }[]` | []     | 大模型选项   |
| onLLMSwitch | `(value: string) => void`                     | -      | 选中模型变化 |

### UploadConfig

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| action | `string \| (file) => Promise<string>` | - | 文件上传的地址 |
| directory | `boolean` | false | 支持上传文件夹 |
| headers | `{ [key: string]: string }` | - | 设置上传的请求头 |
| data | `object` \| `(file) => object` | - | 设置上传的额外请求数据 |
| multiple | `boolean` | false | 是否支持多选文件 |
| accept | `string` | - | 接受文件类型 |
| beforeUpload | `(file, fileList) => boolean` \| `Promise<File>` | - | 上传文件之前的钩子，参数为上传的文件，若返回 false 则停止上传。 |
| onPreview | `(file) => void` | - | 点击文件，自行处理预览逻辑 |
| onRemove | `(file) => void` | - | 删除文件 |
| disabled | `boolean` | false | 是否禁用 |
| withCredentials | `boolean` | false | 上传请求时是否携带 cookie |
| iconRender | `(file: UploadFile, listType?: UploadListType) => ReactNode` | - | 自定义图标 |
| maxCount | `number` | - | 最大文件数量 |
| customRequest | `CustomRequestOptions` | - | 覆盖默认的上传行为，可自定义自己的上传实现 |
| responseTransform | `(response, file: UploadFile) => any` | - | 对于非标准的上传接口响应，自定义响应数据转换 |
| popoverConfig | `PopoverConfig` | - | popover 配置 |

### PopoverConfig

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| content | `ReactNode` | - | 内容 |
| trigger | `hover` \| `click` \| `contextMenu` | hover | 触发方式 |
| overlayClassName | `string` | - | 弹出层类名 |
| placement | `string` | topRight | 弹出位置 |
| autoAdjustOverflow | `boolean` | true | 气泡被遮挡时自动调整位置 |
| getPopupContainer | `(triggerNode: HTMLElement) => HTMLElement` | - | 偏移距离 |
| zIndex | `number` | - | 设置 Tooltip 的 z-index |
| mouseEnterDelay | `number` | 0.3 | 鼠标移入后延时多少才显示 Tooltip，单位：秒 |
| mouseLeaveDelay | `number` | 0.3 | 鼠标移出后延时多少才隐藏 Tooltip，单位：秒 |

### CustomRequestOptions

| 参数            | 类型                                  | 默认值 | 说明                 |
| --------------- | ------------------------------------- | ------ | -------------------- |
| onProgress      | `(event: { percent: number }): void`  | -      | 上传进度             |
| onError         | `(event: Error, body?: Object): void` | -      | 上传失败后需手动调用 |
| onSuccess       | `(body: Object): void`                | -      | 上传成功后需手动调用 |
| data            | `Object`                              | {}     | 上传的额外请求数据   |
| filename        | `String`                              | -      | 文件名               |
| file            | `File`                                | -      | 上传的文件           |
| withCredentials | `Boolean`                             | false  | 是否携带 cookie      |
| action          | `String`                              | -      | 文件上传 url         |
| headers         | `Object`                              | -      | 请求头               |

## 覆盖 less 变量

### 窄屏模式

| 参数                                      | 说明               |
| ----------------------------------------- | ------------------ |
| @composer-compact-bg                      | 背景色             |
| @composer-compact-border-radius           | 边框圆角           |
| @composer-compact-border-color            | 边框颜色           |
| @composer-compact-border-width            | 边框宽度           |
| @composer-compact-net-search-bg           | 网络搜索背景色     |
| @composer-compact-net-search-color        | 网络搜索文本色     |
| @composer-compact-net-search-active-bg    | 网络搜索激活背景色 |
| @composer-compact-net-search-active-color | 网络搜索激活文本   |
| @composer-compact-input-color             | 输入框文本色       |

### 宽屏模式

| 参数                                   | 说明                 |
| -------------------------------------- | -------------------- |
| @composer-wide-border-width            | 边框宽度             |
| @composer-wide-border-color            | 边框颜色             |
| @composer-wide-border-radius           | 边框圆角             |
| @composer-wide-send-bg                 | 发送按钮背景色       |
| @composer-wide-send-size               | 发送按钮尺寸         |
| @composer-wide-send-disable-bg         | 发送按钮禁用背景颜色 |
| @composer-wide-llm-height: 24px        | 模型选择组件高度     |
| @composer-wide-llm-bg                  | 模型选择背景色       |
| @composer-wide-llm-color               | 模型选择文本色       |
| @composer-wide-net-search-border       | 网络搜索边框         |
| @composer-wide-net-search-height       | 网络搜索组件高度     |
| @composer-wide-net-search-color        | 网络搜索文本色       |
| @composer-wide-net-search-active-color | 网络搜索激活文本色   |

---

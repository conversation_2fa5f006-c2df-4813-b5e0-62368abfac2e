import { NavbarProps } from '../Navbar';

export interface WelcomeProps {
  open?:boolean //是否展示欢迎页，默认true
  logo?:string //logo图标的地址
  title?:string //欢迎页主标题，默认："智能助手为您服务~",
  showTitle?: boolean //是否展示标题，默认true
  subtitle:string //欢迎页副标题，默认："作为您的智能助手，我可以为您答疑解惑",
  showsubTitle?: boolean //是否展示副标题，默认true
  riskTip:string //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别",
  navbar?: NavbarProps;
  renderNavbar?: () => React.ReactNode;
  openMessages?: {type?: string, content?: any}[];
}
export interface GuidePageProps {
  /**
   * 宽版模式断点
   */
  // wideBreakpoint?: string;
  /**
   * 当前语言
   */
  locale?: string;
  /**
   * 多语言
   */
  locales?: any; // FIXME
  /**
   * 导航栏配置
   */
  navbar?: NavbarProps;
  /**
   * 导航栏渲染函数
   */
  renderNavbar?: () => React.ReactNode;
  guideWelcome: WelcomeProps;
  onRiskClick?: () => void;
  isAorta?: boolean;
  keyboardShow?: boolean;
   /**
  * 引导问题点击事件
  */
  onSend?: (type: string, content: string) => void;
};

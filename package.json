{"name": "@ht/chatui", "version": "1.0.0-beta.11", "description": "The React library for Chatbot UI", "main": "lib/index.js", "module": "es/index.js", "browser": "dist/index.js", "style": "dist/index.css", "typings": "lib/index.d.ts", "files": ["dist", "es", "lib"], "scripts": {"dev": "dumi dev", "docs:build": "dumi build", "clean": "rimraf dist es lib", "prebuild": "npm run clean", "prefix": "cross-env NODE_ENV=production postcss dist/index.css -o dist/index.css", "copy:less": "copyfiles -u 1 \"src/**/*.less\" es", "js:cjs": "cross-env BABEL_ENV=cjs babel src -d lib --extensions '.ts,.tsx'", "js:esm": "cross-env BABEL_ENV=esm babel src -d es --extensions '.ts,.tsx'", "build:types": "tsc -p tsconfig.build.json", "build": "npm run js:cjs && npm run js:esm && npm run build:types && npm run build:css", "build:css": "lessc src/styles/index.less dist/index.css && npm run prefix && npm run copy:less", "build:umd": "cross-env BABEL_ENV=umd rollup -c && npm run build:css", "prepublishOnly": "npm run build && npm run build:umd", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install && node ./.husky/prepare.js"}, "dependencies": {"@babel/runtime": "^7.18.3", "@babel/runtime-corejs3": "^7.18.3", "@ht/chatui": "^1.0.0-alpha.21", "@ht/h5-utils": "4.2.2", "@ht/sprite-ui": "^1.3.0", "@lowcode/lc-render": "^1.2.6-beta.618", "@types/react-syntax-highlighter": "^15.5.13", "clsx": "^1.1.1", "copy-to-clipboard": "^3.3.3", "core-js": "^3.23.1", "dompurify": "^2.3.8", "dumi": "^2.4.18", "github-markdown-css": "^5.8.0", "intersection-observer": "^0.12.2", "jest-environment-jsdom": "^29.7.0", "js-sha256": "^0.11.1", "moment": "^2.30.1", "rc-select": "^14.16.6", "rc-tooltip": "^6.4.0", "rc-upload": "^4.8.1", "rc-util": "^5.44.4", "react-image-lightbox": "^5.1.4", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.6.1", "rehype-external-links": "^3.0.0", "rehype-katex": "^6.0.2", "rehype-raw": "^6.0.0", "remark-gemoji": "^8.0.0", "remark-gfm": "^3.0.1", "remark-math": "^6.0.0"}, "resolutions": {"@types/react": "^17.0.45", "string-width": "4.2.3"}, "devDependencies": {"@babel/cli": "^7.17.10", "@babel/core": "^7.18.5", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-transform-runtime": "^7.18.5", "@babel/preset-env": "^7.18.2", "@babel/preset-react": "^7.17.12", "@babel/preset-typescript": "^7.17.12", "@commitlint/cli": "^17.0.2", "@commitlint/config-conventional": "^17.0.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/dompurify": "^2.3.3", "@types/jest": "^28.1.1", "@types/react": "^17.0.45", "@types/react-dom": "^17.0.17", "@types/resize-observer-browser": "^0.1.7", "@typescript-eslint/eslint-plugin": "^5.28.0", "@typescript-eslint/parser": "^5.28.0", "autoprefixer": "^10.4.7", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "cssnano": "^5.1.11", "eslint": "^8.17.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-compat": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "7.0.4", "jest": "^28.1.1", "less": "^4.1.3", "postcss": "^8.4.14", "postcss-cli": "^9.1.0", "postcss-pxtorem": "^6.0.0", "react": "17.0.2", "react-dom": "17.0.2", "rollup": "^2.75.6", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^28.0.5", "typescript": "^4.7.3", "wrap-ansi": "^6.2.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "browserslist": [">0.2%", "Android >= 4.4", "not dead", "not op_mini all", "chrome >= 42", "ios >= 9", "ie >= 11"], "keywords": ["react", "react-component", "chat", "chat-ui"], "homepage": "http://web.npm.htsc/package/@ht/chatui", "bugs": {"url": "http://gitlab2.htsc/aorta-web/tools/chatui/-/issues"}, "repository": {"type": "git", "url": "http://gitlab2.htsc/aorta-web/tools/chatui"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}
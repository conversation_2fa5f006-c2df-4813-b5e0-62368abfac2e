/*
 * @Author: 020446 tianxia<PERSON><EMAIL>
 * @Date: 2025-07-22 20:37:09
 * @LastEditors: 020446 <EMAIL>
 * @LastEditTime: 2025-08-12 13:14:00
 */
import React from 'react';

export interface GroupTitleProps {
    children?: React.ReactNode;
    title?: string;
    renderImg?: () => JSX.Element;
}



const GroupTitle: React.FC<GroupTitleProps> = ({ renderImg, title }) => {

    return (
        <div className='groupTitle'>
            {renderImg && renderImg()}
            <div className='titleText'>{title}</div>
        </div>
    );
};

export default GroupTitle;

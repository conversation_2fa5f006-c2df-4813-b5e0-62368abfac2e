---
nav:
  title: hooks
  order: -1
group:
  title: API
  order: 20
order: 1
---

# 创建会话
**useCreateConversation**

```js
import { useCreateConversation } from '@ht/chatui';
```

## HiAgent

### 示例

<code src="./demo-hiagent.tsx" ></code>

### Options

requestConfig
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestConfig.type | 请求类型:tcp/http | `string` | `http` |
| requestConfig.action | 站内请求action | `string` | - |
| requestConfig.tcpRequest | 站内请求方法 | `functon` | - |
| requestConfig.method | 请求类型 | `string` | `post` |
| requestConfig.paramsKey | 站内请求时外层包裹key | `string` | - |
| requestConfig.requestTransfer | 请求转换器 | `function` | - |
| requestConfig.responseTransfer | 响应转换器 | `function` | - |
| requestConfig.manual | 是否手动触发 | `boolean` | `false` |
| requestConfig.headers | 头信息 | `object` | - |

requestParams
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestParams.userId | 用户id | `string` | - |
| requestParams.inputs | 会话变量 | `object` | - |
| requestParams.appKey | appkey | `string` | - |


## 自定义服务

### 示例

<code src="./demo-custom.tsx" ></code>

### Options

requestConfig
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestConfig.type | 请求类型:tcp/http | `string` | `http` |
| requestConfig.action | 站内请求action | `string` | - |
| requestConfig.tcpRequest | 站内请求方法 | `functon` | - |
| requestConfig.method | 请求类型 | `string` | `post` |
| requestConfig.paramsKey | 站内请求时外层包裹key | `string` | - |
| requestConfig.requestTransfer | 请求转换器 | `function` | - |
| requestConfig.responseTransfer | 响应转换器 | `function` | - |
| requestConfig.responseTransfer | 响应转换器 | `function` | - |
| requestConfig.manual | 是否手动触发 | `boolean` | `false` |
| requestConfig.headers | 头信息 | `object` | - |

requestParams
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestParams.userId | 用户id | `string` | - |
| requestParams.appId | 应用id | `string` | - |


---

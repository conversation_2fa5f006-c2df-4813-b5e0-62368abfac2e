import RadioIcon from '../components/FileCard/images/radioIcon.svg';
import WordIcon from '../components/FileCard/images/wordIcon.svg';
import PPtIcon from '../components/FileCard/images/pptIcon.svg';
import ExcelIcon from '../components/FileCard/images/excelIcon.svg';
import PdfIcon from '../components/FileCard/images/pdfIcon.svg';
import ZipIcon from '../components/FileCard/images/zipIcon.svg';
import ImgIcon from '../components/FileCard/images/imgIcon.svg';
import TxtIcon from '../components/FileCard/images/txtIcon.png';
import FileIcon from '../components/FileCard/images/fileIcon.png';
import getExtName from './getExtName';

const iconMap: any = {
  raw: RadioIcon,
  wav: RadioIcon,
  flac: RadioIcon,
  ogg: RadioIcon,
  mp3: RadioIcon, // 音频文件，后续待补充
  doc: WordIcon,
  docx: WordIcon,
  xls: ExcelIcon,
  xlsx: ExcelIcon,
  ppt: PPtIcon,
  pptx: PPtIcon,
  pdf: PdfIcon,
  zip: ZipIcon,
  png: ImgIcon, // 图片文件，后续待补充
  svg: ImgIcon, // 图片文件，后续待补充
  jepg: ImgIcon, // 图片文件，后续待补充
  txt: TxtIcon,
}

// eslint-disable-next-line no-bitwise
export function getFileTypeIcon(fileName: string = '') {
  const ext = getExtName(fileName);
  return iconMap?.[ext] || FileIcon;
}
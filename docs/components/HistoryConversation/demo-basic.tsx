import React from 'react';
import { HistoryConversation, ConversationItemProps } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';

export default () => {

  const list: ConversationItemProps[] = Array.from({ length: 10 }).map((_, index) => ({
    conversationId: index,
    createTime: index < 3 ? Date.now() : index < 7 ? new Date().setDate(new Date().getDate() - 6) : new Date().setDate(new Date().getDate() - 30),  // 当前时间戳，毫秒级
    question: `这是第${index + 1}条历史消息`,
    disabled: index === 3,
  }));

  const onConversationClick = (conversationItem: ConversationItemProps) => {
    alert(`点击${conversationItem.question}`);
  };

  const renderTitle = () => {
    return (
      <div style={{ fontSize: '16px', fontWeight: 'bold', margin:'20px', color: '#1677ff' }}>📝 自定义历史会话列表标题</div>
    )
  }

  const renderFooter = () => {
    return (
      <div style={{ fontSize: '16px', border:'1px solid transparent', background: '#e6f4ff', height: '100px', margin: '20px',  padding: '20px', color: '#1677ff'}}>自定义底部区域</div>
    )
  }

    return (
      <div className="history">
        <HistoryConversation 
          style={{ width: '100%', height: '500px' }}
          renderTitle= {renderTitle}
          showSearch={true} 
          searchPlaceholder="请输入搜索关键字" 
          activeConversationId={1} 
          onConversationClick={onConversationClick}   
          list={list}
          renderFooter={renderFooter}
        />
      </div>      
    )
}

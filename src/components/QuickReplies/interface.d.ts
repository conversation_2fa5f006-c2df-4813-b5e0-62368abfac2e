export interface QuickRepliesProps {
  quickReplies: QuickReplyItemProps[];
  visible?: boolean;
  openPage?: (url: string) => void;
  onClick: (item: QuickReplyItemProps, index: number) => void;
  onScroll?: (event: React.UIEvent<HTMLDivElement, UIEvent>) => void;
}

export interface QuickReplyItemProps {
  name?: string;
  code?: string;
  icon?: string;
  img?: string;
  isNew?: boolean;
  isHighlight?: boolean;
  title?: string;
  content?: string;
  url?: string;
}

export interface QuickReplyProps {
  item: QuickReplyItemProps;
  index: number;
  onClick: (item: QuickReplyItemProps, index: number) => void;
}

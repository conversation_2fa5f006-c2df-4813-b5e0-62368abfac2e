/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { Tooltip } from '../Tooltip';

interface Props {
    name: string;
    id: string;
}

const ItemName = ({ name, id }: Props) => {
    const [tooltipShow, setTooltipShow] = useState(false);
    useEffect(() => {
        const titleEle = document.getElementById(`${id}`);
        if (titleEle) {
            if (titleEle.scrollWidth > titleEle.clientWidth) {
                setTooltipShow(true);
            } else {
                setTooltipShow(false);
            }
        }
    }, []);

    return <>{tooltipShow ? <Tooltip content={name}>
        <div id={id} className="ChatConfig-ItemLabel">{name}</div>
    </Tooltip> : <div id={id} className="ChatConfig-ItemLabel">{name}</div>}</>;
};
export default ItemName;

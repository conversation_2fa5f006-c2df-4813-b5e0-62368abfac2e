.RiskTipPcWrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 12;
  }

  .RiskTipPc {
    position: relative;
    padding: 20px;
    // width: 980px;
    // height: 800px;
    // width: min(90vw, 980px); /* 不超过980px，但占满90%视口 */
    // height: min(90vh, 800px); /* 不超过800px，但占满90%视口 */
    width: 60vw;
    height: 90vh;
    background: #fff;
    border-radius: 4px;
    z-index: 13;

    &-icon {
      width: 18px;
      height: 18px;
      margin-right: 8px;
    }

    &-titleClose {
      width: 14px;
      height: 14px;
      cursor: pointer;
    }

    &-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      line-height: 26px;
      padding-bottom: 20px;
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: space-between;
    }

    &-content {
      padding: 0 20px;
      // height: 670px;
      height: calc(100% - 110px);
      overflow-y: scroll;
      margin-bottom: 20px;
    }

    &-contentBlock {
      margin-top: 20px;

      &:first-child {
        margin-top: 0px;
      }
    }

    &-contentFirTitle {
      font-size: 14px;
      font-weight: bold;
      color: #333333;
      line-height: 22px;
    }

    &-contentSecTitle {
      font-size: 14px;
      color: #333333;
      line-height: 22px;
      margin-top: 10px;
    }

    &-contentText {
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      margin-top: 6px;
    }

    .RiskTipItem {
      font-size: 14px;
      color: #666;
      line-height: 22px;
      padding-bottom: 15px;

      &:last-child {
        padding-bottom: 20px;
      }
    }
  }

  @media (min-width: 1919px) {
      .RiskTipPc {
        width: 980px;
      }
  }

  @media (min-height: 1079px) {
      .RiskTipPc {
        height: 800px;
      }
  }

  .RiskTipFooter {
    float: right;
    display: flex;
    align-items: center;
    margin-top: 10px;
  }

  .FooterCancelBtn {
    padding: 6px 12px;
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    margin-right: 10px;
    font-size: 14px;
    font-weight: 400;
    color: #4A4A4A;
    line-height: 20px;
    cursor: pointer;
  }

  .FooterOkBtn {
    float: right;
    width: 122px;
    background: #108EE9;
    border-radius: 3px;
    text-align: center;
    padding: 6px 12px;
    cursor: pointer;

    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
  }

  .FooterOkBtnDisable {
    float: right;
    background: #EEEEEE;
    border-radius: 3px;
    border: 1px solid #CCCCCC;
    text-align: center;
    padding: 6px 12px;
    cursor: not-allowed;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
  }

  /* 整个滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    /* 垂直滚动条宽度 */
    height: 6px;
    /* 水平滚动条高度 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
    /* 轨道背景色 */
    // border-radius: 6px;
    /* 轨道圆角 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #E5E7EE;
    /* 滑块背景色 */
    border-radius: 3px;
    /* 滑块圆角 */
    border: 3px solid #f1f1f1;
    /* 滑块边框 */
    width: 6px;
    height: 45px;
    background: #E5E7EE;
    border-radius: 3px;
  }

  /* 滚动条滑块悬停效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
    /* 悬停时滑块背景色 */
  }
}

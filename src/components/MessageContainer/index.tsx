/* eslint-disable no-underscore-dangle */
import React, { useState, useEffect, useRef, useCallback, useImperativeHandle } from 'react';
import { PullToRefresh, PullToRefreshHandle, ScrollToEndOptions } from '../PullToRefresh';
import { Message, MessageProps } from '../Message';
import { BackBottom } from '../BackBottom';
import canUse from '../../utils/canUse';
import throttle from '../../utils/throttle';
import getToBottom from '../../utils/getToBottom';
import { ILogParams } from '../../LogPointConfigMap';
import { FeedbackLabelProps } from '../FeedbackTipPc/interface';
import { MessageId } from '../Message/Message';
import { ReferenceProps } from '../References/interface';


const listenerOpts = canUse('passiveListener') ? { passive: true } : false;

export interface MessageContainerProps {
  messages: MessageProps[];
  renderMessageContent?: (message: MessageProps) => React.ReactNode;
  loadMoreText?: string;
  onRefresh?: () => Promise<any>;
  onScroll?: (event: React.UIEvent<HTMLDivElement, UIEvent>) => void;
  renderBeforeMessageList?: () => React.ReactNode;
  backBottomButton?: { icon?: string };
  onBackBottomShow?: () => void;
  onBackBottomClick?: () => void;
  onFeedBack?: (score: MessageProps['feedbackResult'], message: MessageProps, reason?: any) => void;
  onConsolidateReports?: (params: ILogParams) => void;
  copyText?: (text: string, messageId?: MessageId) => void;
  /**
   * 引用文本回调
   */
  onQuote?: (message: MessageProps) => void;
  /**
   * 是否展示删除按钮
   */
  showDeleteMessage?: boolean;
  /**
   * 删除消息回调
   */
  handleDeleteMessage?: (message: MessageProps) => void;
  backBottomButtonProps?: any; // 回到底部按钮组件属性透传
  // 模方卡片用到的
  lowCodeConfig?: object;
  isDev?: boolean;
  userId?: string;
  robot?: {
    logo?: string;
    showMsgLogo?: boolean;
  };
  // GuessAskMore卡片需要的发送
  onSend?: (type: string, content: string, params?: object) => Promise<boolean>;
  // GuessAskMore卡片可能需要的打开链接
  openPage?: () => {};
  // 文件预览方法
  openFileViews?: () => {};
  // 是否展示反馈弹窗
  showFeedbackModal?: boolean;
  //
  feedbackModalConfig?: {
    title?: string,
    inputPlaceholder?: string,
    showLabels?: boolean,
  };
  // 反馈标签
  feedbackLabels?: FeedbackLabelProps[];
  preprocessContent?: (content: string) => string;
  referencesConfig?: {
    title?: string | React.ReactNode | ((references: any[]) => React.ReactNode);
    modalWidth?: number;
  };
  // 支持自定义“正在思考中”的样式
  customThinkingStyle?: React.ReactNode;

  // 支持自定义“深度思考中”的样式
  customDeepThinkingStyle?: React.ReactNode;
  // 支持自定义“深度思考完成”的样式
  customThinkingCompleteStyle?: React.ComponentType<{thinkTime: number}>;
  // 自定义停止生成
  renderStopAnswer?: (msg: MessageProps) => React.ReactNode;
  // 思考结束是否自动折叠深度思考
  autoThinkCollapsed?: boolean;
  // 是否是宽屏
  isWide?: boolean;
  // 自定义引用渲染
  renderReferencesContent?: React.ComponentType<{ references: ReferenceProps[] }>;
  // 是否展示引用
  showQuote?: boolean;
  // 幻觉标识文案
  hallucinationText?: string;
}

export interface MessageContainerHandle {
  ref: React.RefObject<HTMLDivElement>;
  scrollToEnd: (options?: ScrollToEndOptions) => void;
}

function isNearBottom(el: HTMLElement, n: number) {
  const offsetHeight = Math.max(el.offsetHeight, 600);
  return getToBottom(el) < offsetHeight * n;
}

export const MessageContainer = React.forwardRef<MessageContainerHandle, MessageContainerProps>(
  (props, ref) => {
    const {
      messages,
      loadMoreText,
      onRefresh,
      onScroll,
      renderBeforeMessageList,
      renderMessageContent,
      backBottomButton,
      onBackBottomShow,
      onBackBottomClick,
      onFeedBack,
      onConsolidateReports,
      copyText,
      onQuote,
      showDeleteMessage = false,
      handleDeleteMessage,
      backBottomButtonProps = {},
      lowCodeConfig,
      isDev,
      userId,
      onSend,
      openPage,
      openFileViews,
      robot,
      showFeedbackModal,
      feedbackLabels,
      feedbackModalConfig,
      preprocessContent,
      referencesConfig,
      renderStopAnswer,
      customThinkingStyle,
      customDeepThinkingStyle,
      customThinkingCompleteStyle,
      autoThinkCollapsed = false,
      isWide,
      renderReferencesContent,
      showQuote,
      hallucinationText,
    } = props;
    
    const [showBackBottom, setShowBackBottom] = useState(false);
    const [newCount, setNewCount] = useState(0);
    const showBackBottomtRef = useRef(showBackBottom);
    const newCountRef = useRef(newCount);
    const messagesRef = useRef<HTMLDivElement>(null);
    const scrollerRef = useRef<PullToRefreshHandle>(null);
    const lastMessage = messages[messages.length - 1];

    const clearBackBottom = () => {
      setNewCount(0);
      setShowBackBottom(false);
    };

    const scrollToEnd = useCallback((opts?: ScrollToEndOptions) => {
      if (scrollerRef.current) {
        if (!showBackBottomtRef.current || (opts && opts.force)) {
          scrollerRef.current.scrollToEnd(opts);
          if (showBackBottomtRef.current) {
            clearBackBottom();
          }
        }
      }
    }, []);

    const handleBackBottomClick = () => {
      scrollToEnd({ animated: false, force: true });
      // setNewCount(0);
      // setShowBackBottom(false);

      if (onBackBottomClick) {
        onBackBottomClick();
      }
    };

    const checkShowBottomRef = useRef(
      throttle((el: HTMLElement) => {
        if (isNearBottom(el, 3)) {
          if (newCountRef.current) {
            // 如果有新消息，离底部0.5屏-隐藏提示
            if (isNearBottom(el, 0.5)) {
              // setNewCount(0);
              // setShowBackBottom(false);
              clearBackBottom();
            }
          } else {
            setShowBackBottom(false);
          }
        } else {
          // 3屏+显示回到底部
          setShowBackBottom(true);
        }
      }),
    );

    const handleScroll = (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
      checkShowBottomRef.current(e.target);
      if (onScroll) {
        onScroll(e);
      }
    };

    useEffect(() => {
      newCountRef.current = newCount;
    }, [newCount]);

    useEffect(() => {
      showBackBottomtRef.current = showBackBottom;
    }, [showBackBottom]);

    useEffect(() => {
      const scroller = scrollerRef.current;
      const wrapper = scroller && scroller.wrapperRef.current;

      if (!wrapper || !lastMessage || lastMessage.position === 'pop') {
        return;
      }

      if (lastMessage.position === 'right') {
        // 自己发的消息，强制滚动到底部
        scrollToEnd({ force: true });
      } else if (isNearBottom(wrapper, 1)) {
        const animated = !!wrapper.scrollTop;
        scrollToEnd({ animated, force: true });
      } else {
        setNewCount((c) => c + 1);
        setShowBackBottom(true);
      }
    }, [lastMessage, scrollToEnd]);

    useEffect(() => {
      const wrapper = messagesRef.current!;

      let needBlur = false;
      let startY = 0;

      function reset() {
        needBlur = false;
        startY = 0;
      }

      function touchStart(e: TouchEvent) {
        const { activeElement } = document;
        if (activeElement && activeElement.nodeName === 'TEXTAREA') {
          needBlur = true;
          startY = e.touches[0].clientY;
        }
      }

      function touchMove(e: TouchEvent) {
        if (needBlur && Math.abs(e.touches[0].clientY - startY) > 20) {
          (document.activeElement as HTMLElement).blur();
          reset();
        }
      }

      wrapper.addEventListener('touchstart', touchStart, listenerOpts);
      wrapper.addEventListener('touchmove', touchMove, listenerOpts);
      wrapper.addEventListener('touchend', reset);
      wrapper.addEventListener('touchcancel', reset);

      return () => {
        wrapper.removeEventListener('touchstart', touchStart);
        wrapper.removeEventListener('touchmove', touchMove);
        wrapper.removeEventListener('touchend', reset);
        wrapper.removeEventListener('touchcancel', reset);
      };
    }, []);

    useImperativeHandle(ref, () => ({ ref: messagesRef, scrollToEnd }), [scrollToEnd]);



    return (
      <div className="MessageContainer" ref={messagesRef} tabIndex={-1} id="MessageContainer">
        {renderBeforeMessageList && renderBeforeMessageList()}
        <PullToRefresh
          onRefresh={onRefresh}
          onScroll={handleScroll}
          loadMoreText={loadMoreText}
          ref={scrollerRef}
        >
          <div className="MessageList">
            {messages.map((msg) => (
              <Message
                {...msg}
                user={{ 
                  ...(msg.user || {}), 
                  avatar: (msg.position === 'left' && robot?.showMsgLogo) ? robot?.logo || msg?.user?.avatar : '' 
                }}
                // user={{
                //   ...(msg.user || {}),
                //   // 添加宽屏判断，宽屏保持原来逻辑，否则取消息里的avatar
                //   avatar: msg.position === 'left' ? (robot?.showMsgLogo ? robot?.logo || msg?.user?.avatar) : msg?.user?.avatar
                // }}
                renderMessageContent={renderMessageContent}
                key={msg._id}
                onFeedBack={onFeedBack}
                onConsolidateReports={onConsolidateReports}
                copyText={copyText}
                onQuote={onQuote}
                showDeleteMessage={showDeleteMessage}
                handleDeleteMessage={handleDeleteMessage}
                lowCodeConfig={lowCodeConfig}
                isDev={isDev}
                userId={userId}
                onSend={onSend}
                openPage={openPage}
                openFileViews={openFileViews}
                showFeedbackModal={showFeedbackModal}
                // 反馈弹窗自定义配置
                feedbackModalConfig={feedbackModalConfig}
                referencesConfig={referencesConfig}
                feedbackLabels={feedbackLabels}
                preprocessContent={preprocessContent}
                // 自定义停止生成
                renderStopAnswer={renderStopAnswer}
                customThinkingStyle={customThinkingStyle}
                customDeepThinkingStyle={customDeepThinkingStyle}
                customThinkingCompleteStyle={customThinkingCompleteStyle}
                autoThinkCollapsed={autoThinkCollapsed}
                isWide={isWide}
                renderReferencesContent={renderReferencesContent}
                showQuote={showQuote}
                hallucinationText={hallucinationText}
              />
            ))}
          </div>
        </PullToRefresh>
        {showBackBottom && (
          <BackBottom
            count={newCount}
            backBottomButton={backBottomButton}
            onClick={handleBackBottomClick}
            onDidMount={onBackBottomShow}
            {...backBottomButtonProps}
          />
        )}       
      </div>
    );
  },
);

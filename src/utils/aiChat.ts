/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-02 19:20:16
 */
import { MessageProps } from '../components/Message';

// 解析思维链，获取思考内容和正文
export function getThinkContent(msgText: string) {
  if (!msgText) {
    return {
      content: '',
      thinkContent: '',
      isThinking: false,
    };
  }

  const thinkStartTag = '<think>';
  const thinkEndTag = '</think>';
  
  // 找到所有的<think>和</think>标签位置
  const startPositions: number[] = [];
  const endPositions: number[] = [];
  
  let startIndex = 0;
  while ((startIndex = msgText.indexOf(thinkStartTag, startIndex)) !== -1) {
    startPositions.push(startIndex);
    startIndex += thinkStartTag.length;
  }
  
  let endIndex = 0;
  while ((endIndex = msgText.indexOf(thinkEndTag, endIndex)) !== -1) {
    endPositions.push(endIndex);
    endIndex += thinkEndTag.length;
  }
  
  // 检查是否有未闭合的<think>标签
  const isThinking = startPositions.length > endPositions.length;
  
  // 提取所有think标签内的内容
  const thinkContents: string[] = [];
  
  for (let i = 0; i < startPositions.length; i++) {
    const startPos = startPositions[i];
    // 找到对应的结束标签
    const correspondingEndIndex = endPositions.find(endPos => endPos > startPos);
    
    if (correspondingEndIndex !== undefined) {
      const thinkText = msgText.slice(startPos + thinkStartTag.length, correspondingEndIndex);
      thinkContents.push(thinkText);
    } else {
      // 没有找到对应的结束标签，说明是未闭合的
      const thinkText = msgText.slice(startPos + thinkStartTag.length);
      thinkContents.push(thinkText);
    }
  }
  
  // 合并所有think内容
  const thinkContent = thinkContents.join('\n');
  
  // 提取content：构建不包含任何<think>标签的内容
  let content = '';
  let lastEndPos = 0;
  
  for (let i = 0; i < startPositions.length; i++) {
    const startPos = startPositions[i];
    // 添加当前<think>标签之前的内容
    content += msgText.slice(lastEndPos, startPos);
    
    // 找到对应的结束标签
    const correspondingEndIndex = endPositions.find(endPos => endPos > startPos);
    
    if (correspondingEndIndex !== undefined) {
      // 有对应的结束标签，更新lastEndPos
      lastEndPos = correspondingEndIndex + thinkEndTag.length;
    } else {
      // 没有对应的结束标签，停止处理
      break;
    }
  }
  
  // 如果有未闭合的标签，不添加任何后续内容
  if (!isThinking) {
    // 添加最后一个</think>标签之后的内容
    content += msgText.slice(lastEndPos);
  }
  
  return {
    content,
    thinkContent,
    isThinking,
  };
}

// 格式一下魔方卡片内容
export function formatLowcodeContent(content: MessageProps['content']) {
  const lowCodeDomin = window.isLowCodeDev ? 'http://168.61.127.80/app/' : 'http://lowcode.fe.htsc/app/';

  return {
    url: lowCodeDomin + content?.cardBizType + '/editor',
    data: content,
  };
}

// 时间格式转换
const REGEX_FORMAT = /YYYY|MM|DD|HH|mm|ss/g;
type DateFormats = {
  [p: string]: string;
};
const padStart = (n: number) => (n <= 9 ? '0' : '') + n;
export function getDateFormat(d: number | string | Date, formatString = 'YYYY-MM-DD HH:mm:ss') {
  const date = new Date(d);

  const dates: DateFormats = {
    YYYY: date.getFullYear() + '',
    MM: padStart(date.getMonth() + 1) ,
    DD: padStart(date.getDate()),
    HH: padStart(date.getHours()),
    mm: padStart(date.getMinutes()),
    ss: padStart(date.getSeconds()),
  }

  return formatString?.replace(REGEX_FORMAT, (match) => dates[match]);
}

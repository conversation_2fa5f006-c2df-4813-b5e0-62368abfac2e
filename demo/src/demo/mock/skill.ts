export const skillsMock = [
  {
    key: '',// 必传，唯一标识
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '智能翻译',// 文案
    question: '翻译上面文字',// 发送时的命令
    agentId: '1',//发送时可指定智能体ID
    children: [
      {
        key: 'translate',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: 'AI翻译',// 文案
        question: 'AI翻译',// 发送时的命令
        agentId: 'translate',//发送时可指定智能体ID
        translateOptions: {
          src_lang: {
            defaultValue: 'auto',
            options: [{
              label: '自动检测',
              value: 'auto',
              disabled: false,
            },
            {
              label: '中文',
              value: 'zh_CN',
              disabled: false,
            },
            {
              label: '英文',
              value: 'en_US',
              disabled: false,
            },
            {
              label: '日文',
              value: 'ja_JP',
              disabled: false,
            },
            {
              label: '法语',
              value: 'fr_FR',
              disabled: false,
            }],
          },
          tgt_lang: {
            defaultValue: 'zh_CN',
            options: [{
              label: '自动检测',
              value: 'auto',
              disabled: false,
            },
            {
              label: '中文',
              value: 'zh_CN',
              disabled: false,
            },
            {
              label: '英文',
              value: 'en_US',
              disabled: false,
            },
            {
              label: '日文',
              value: 'ja_JP',
              disabled: false,
            },
            {
              label: '法语',
              value: 'fr_FR',
              disabled: false,
            }],
          },
        }
      },
      {
        key: 'translate-page',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译此页面',// 文案
        question: '翻译此页面',// 发送时的命令
        agentId: 'translate-page',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log('翻译此页面'),// 技能点击回调 -》 由AiChat统一写入Composer
      }
    ],
  },
  {
    key: 'summary',// 必传，唯一标识
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '网页总结',// 文案
    question: '网页总结',// 发送时的命令
    agentId: 'summary',//发送时可指定智能体ID
    // children?: Skill[],//折叠子项,有子项时，点击会展开子项
    // expandIcon?: string,// 折叠图标
    // customRender?: React.ReactNode,//自定义渲染
    onClick: () => {
      console.log('点击网页总结');
    },// 技能点击回调 -》 由AiChat统一写入Composer
  },
  {
    key: 'more',// 必传，唯一标识
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '更多',// 文案
    question: '翻译上面文字',// 发送时的命令
    agentId: '1',//发送时可指定智能体ID
    children: [
      {
        key: 'default',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '图片文字抓取',// 文案
        question: '翻译上面文字',// 发送时的命令
        agentId: 'default',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'text-condenser',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '缩写',// 文案
        question: '缩写',// 发送时的命令
        agentId: 'text-condenser',//发送时可指定智能体ID
      },
      {
        key: 'text-expander',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '扩写',// 文案
        question: '扩写',// 发送时的命令
        agentId: 'text-expander',//发送时可指定智能体ID
      },
      {
        key: 'text-polisher',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '润色',// 文案
        question: '润色',// 发送时的命令
        agentId: 'text-polisher',//发送时可指定智能体ID
      },
      {
        key: 'grammar-corrector',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '修正拼写和语法',// 文案
        question: '修正拼写和语法',// 发送时的命令
        agentId: 'grammar-corrector',//发送时可指定智能体ID
      },
    ],
  }
];

/*
 * @Author: 016645
 * @Description: AI聊天相关hooks
 * @Date: 2025-03-20 10:05:32
 */
import {
  CreateConversation,
  SendMessage,
  SendMessageAgain,
  DeleteConversation,
  DeleteMessages,
  StopMessage,
  GetConversationList,
  GetMessageList,
  Feedback,
  Sensitive,
} from './types';

// HiAgent平台实现
import hiAgentUseCreateConversation from './hiAgent/useCreateConversation';
import hiAgentUseGetConversationList from './hiAgent/useGetConversationList';
import hiAgentUseDeleteConversation from './hiAgent/useDeleteConversation';
import hiAgentUseSendMessage from './hiAgent/useSendMessage';
import hiAgentUseSendMessageAgain from './hiAgent/useSendMessageAgain';
import hiAgentUseStopMessage from './hiAgent/useStopMessage';
import hiAgentUseGetMessageList from './hiAgent/useGetMessageList';
import hiAgentUseFeedback from './hiAgent/useFeedback';
import hiAgentUseDeleteMessages from './hiAgent/useDeleteMessages';

// Custom平台实现
import customUseCreateConversation from './custom/useCreateConversation';
import customUseSendMessage from './custom/useSendMessage';
import customUseSendMessageAgain from './custom/useSendMessageAgain';
import customUseGetConversationList from './custom/useGetConversationList';
import customUseDeleteConversation from './custom/useDeleteConversation';
import customUseStopMessage from './custom/useStopMessage';
import customUseGetMessageList from './custom/useGetMessageList';
import customUseFeedback from './custom/useFeedback';
import customUseSensitive from './custom/useSensitive';
import customUseDeleteMessages from './custom/useDeleteMessages';

/**
 * 平台类型
 */
type Platform = 'hiAgent' | 'custom';

/**
 * 获取规范化的平台名称
 * @param platform - 原始平台名称
 * @returns 标准化的平台名称
 */
const getNormalizedPlatform = (platform: string = 'hiAgent'): Platform => {
  const normalizedPlatform = platform.toLowerCase();
  
  if (normalizedPlatform === 'hiagent') return 'hiAgent';
  if (normalizedPlatform === 'custom') return 'custom';
  
  throw new Error(`不支持的平台: ${platform}`);
};

/**
 * 创建会话
 * @param options - 创建会话配置项
 * @returns 创建会话hook返回值
 */
export const useCreateConversation = (options: CreateConversation) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseCreateConversation(options);
    case 'custom':
      return customUseCreateConversation(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 发送消息
 * @param options - 发送消息配置项
 * @returns 发送消息hook返回值
 */
export const useSendMessage = (options: SendMessage) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseSendMessage(options);
    case 'custom':
      return customUseSendMessage(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 获取会话列表
 * @param options - 获取会话列表配置项
 * @returns 获取会话列表hook返回值
 */
export const useGetConversationList = (options: GetConversationList) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseGetConversationList(options);
    case 'custom':
      return customUseGetConversationList(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 删除会话
 * @param options - 删除会话配置项
 * @returns 删除会话hook返回值
 */
export const useDeleteConversation = (options: DeleteConversation) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseDeleteConversation(options);
    case 'custom':
      return customUseDeleteConversation(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 重新生成回复
 * @param options - 重新生成回复配置项
 * @returns 重新生成回复hook返回值
 */
export const useSendMessageAgain = (options: SendMessageAgain) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseSendMessageAgain(options);
    case 'custom':
      return customUseSendMessageAgain(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 停止消息生成
 * @param options - 停止消息生成配置项
 * @returns 停止消息生成hook返回值
 */
export const useStopMessage = (options: StopMessage) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseStopMessage(options);
    case 'custom':
      return customUseStopMessage(options);   
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 获取消息列表
 * @param options - 获取消息列表配置项
 * @returns 获取消息列表hook返回值
 */
export const useGetMessageList = (options: GetMessageList) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseGetMessageList(options); 
    case 'custom':
      return customUseGetMessageList(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 反馈
 * @param options - 反馈配置项
 * @returns 反馈hook返回值
 */
export const useFeedback = (options: Feedback) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseFeedback(options);
    case 'custom':
      return customUseFeedback(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};  

/**
 * 敏感词校验
 * @param options - 敏感词校验配置项
 * @returns 敏感词校验hook返回值
 */
export const useSensitive = (options: Sensitive) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      // TODO: 待实现hiAgent的反馈
      return customUseSensitive(options); // 临时使用custom实现
    case 'custom':
      return customUseSensitive(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

/**
 * 删除消息
 * @param options - 删除消息
 * @returns 删除消息hook返回值
 */
export const useDeleteMessages = (options: DeleteMessages) => {
  const platform = getNormalizedPlatform(options?.requestConfig?.platform);
  
  switch (platform) {
    case 'hiAgent':
      return hiAgentUseDeleteMessages(options);
    case 'custom':
      return customUseDeleteMessages(options);
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
};

export type CreateConversationProps = CreateConversation;
export type SendMessageProps = SendMessage;
export type SendMessageAgainProps = SendMessageAgain;
export type GetConversationListProps = GetConversationList;
export type DeleteConversationProps = DeleteConversation;
export type StopMessageProps = StopMessage;
export type GetMessageListProps = GetMessageList;
export type FeedbackProps = Feedback;
export type SensitiveProps = Sensitive; 
export type DeleteMessagesProps = DeleteMessages;

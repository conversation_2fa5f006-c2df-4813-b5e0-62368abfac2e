/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useRef, useEffect } from 'react';
import CloseIcon from './images/closeIcon.png';
import { RiskTipAppProps } from './interface';

// const defaultRiskTip = '1、数据覆盖：\n数据来源于海量网络数据、公开数据集等多渠道信息整合。受数据收集范围所限，可能存在对某些特定领域或小众数据覆盖不全面的情况，无法涵盖所有行业细节与特殊场景。\n2、数据更新：\n尽管模型在持续优化，但由于数据更新存在延迟，部分数据难以实时跟进市场动态与行业最新变化，可能出现信息滞后，无法及时反映最新趋势与突发事件影响。\n3、分析准确性：\n模型分析基于数据模式识别与统计规律，面对复杂多变、充满不确定性的现实环境，在一些需要深度专业判断、涉及人为因素及非结构化信息处理时，可能存在分析不准确、结论偏差的问题。\n二、局限性说明\n1、辅助工具定位：\n大模型应用旨在作为辅助工具，为您在展业过程中提供分析参考，助力拓展思路、提升效率，但不能替代您自身专业判断与实际调研。\n2、审慎应用：\n在实际工作应用中，务必结合您的专业知识与实践经验，全面审慎权衡各类市场因素、行业特征、政策导向等。同时，需对模型输出内容的准确性、合规性、及时性进行多维度验证，确保内容契合实际工作需求与市场现状。\n3、合规遵循：\n严格遵守公司内部制定的合规指引，严禁将大模型生成内容未经核实与调整，直接引用至与客户沟通的材料，如报告、邮件、推介资料等。以维护公司专业形象，保障客户权益，防范潜在合规风险。\n4、数据安全：\n鉴于当前网络环境的复杂性与开放性，您在使用大模型过程中输入的信息，存在被外网非法采集的风险。为切实保障数据使用安全，维护公司及客户的核心利益，请务必谨慎操作，严禁输入任何敏感信息，包括但不限于客户信息（姓名、联系方式、资产状况等）、公司信息（财务数据、战略规划、未公开业务信息等）、交易策略（投资组合方案、风险控制模型、买卖时机决策等）及其他机密数据。';
export const RiskTipApp = (props: RiskTipAppProps) => {
  const { onClose, onAgree, hasAgreedRisk, onChatClose } = props;

  const [isBottom, setIsBottom] = useState(hasAgreedRisk); // 是否滑到底部
  const hasReachedBottom = useRef(hasAgreedRisk); // 是否已经滑到底部的标志位
  const contentRef = useRef<any>(null); // 获取 div 的引用

  // 监听 div 的滚动事件
  useEffect(() => {
    const scrollContent = contentRef.current;
    if (!scrollContent) return;

    const handleScroll = () => {
      // 如果已经滑到底部，则不再处理
      if (hasReachedBottom.current) return;

      const scrollTop = scrollContent.scrollTop; // 当前滚动位置
      const scrollHeight = scrollContent.scrollHeight; // 内容高度
      const clientHeight = scrollContent.clientHeight; // 可见高度

      const threshold = 10; // 阈值
      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        setIsBottom(true);
        hasReachedBottom.current = true; // 标记为已经滑到底部
      } else {
        setIsBottom(false);
      }
    };

    // 添加滚动事件监听
    scrollContent.addEventListener("scroll", handleScroll);

    // 清理事件监听
    return () => {
      scrollContent.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleAgree = () => {
    if (!isBottom) {
      return;
    }
    if (hasAgreedRisk) {
      onClose && onClose();
      return;
    }
    onAgree && onAgree();
    onClose && onClose();
  }

  const handleClose = () => {
    onClose && onClose();
    if (!hasAgreedRisk) {
      onChatClose && onChatClose();
    }
  }

  return (
    <div className="RiskTipAppWrap">
      <div className="RiskTipApp">
        <div className="RiskTipAppWarning">
          <div className="RiskTipAppWarning-up" />
          <div className="RiskTipAppWarning-down" />
        </div>
        <div className="RiskTipApp-title">风险提示及使用说明</div>
        <div className="RiskTipApp-content" ref={contentRef}>
          <div className="RiskTipApp-contentBlock">
            <div className='RiskTipApp-contentFirTitle'>一、内容来源</div>
            <div className='RiskTipApp-contentText'>本内容由 AI 大模型自动生成。大模型基于深度学习算法，通过对海量数据的学习和训练，生成相应的文本内容。</div>
          </div>
          <div className="RiskTipApp-contentBlock">
            <div className='RiskTipApp-contentFirTitle'>二、局限性说明</div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>1、数据覆盖：</div>
              <div className='RiskTipApp-contentText'>数据来源于海量网络数据、公开数据集等多渠道信息整合。受数据收集范围所限，可能存在对某些特定领域或小众数据覆盖不全面的情况，无法涵盖所有行业细节与特殊场景。</div>
            </div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>2、数据更新：</div>
              <div className='RiskTipApp-contentText'>尽管模型在持续优化，但由于数据更新存在延迟，部分数据难以实时跟进市场动态与行业最新变化，可能出现信息滞后，无法及时反映最新趋势与突发事件影响。</div>
            </div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>3、分析准确性：</div>
              <div className='RiskTipApp-contentText'>模型分析基于数据模式识别与统计规律，面对复杂多变、充满不确定性的现实环境，在一些需要深度专业判断、涉及人为因素及非结构化信息处理时，可能存在分析不准确、结论偏差的问题。</div>
            </div>
          </div>
          <div className="RiskTipApp-contentBlock">
            <div className='RiskTipApp-contentFirTitle'>三、使用要求</div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>1、辅助工具定位：</div>
              <div className='RiskTipApp-contentText'>大模型应用旨在作为辅助工具，为您在展业过程中提供分析参考，助力拓展思路、提升效率，但不能替代您自身专业判断与实际调研。</div>
            </div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>2、审慎应用：</div>
              <div className='RiskTipApp-contentText'>在实际工作应用中，务必结合您的专业知识与实践经验，全面审慎权衡各类市场因素、行业特征、政策导向等。同时，需对模型输出内容的准确性、合规性、及时性进行多维度验证，确保内容契合实际工作需求与市场现状。</div>
            </div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>3、合规遵循：</div>
              <div className='RiskTipApp-contentText'>严格遵守公司内部制定的合规指引，严禁将大模型生成内容未经核实与调整，直接引用至与客户沟通的材料，如报告、邮件、推介资料等。以维护公司专业形象，保障客户权益，防范潜在合规风险。</div>
            </div>
            <div>
              <div className='RiskTipApp-contentSecTitle'>4、数据安全：</div>
              <div className='RiskTipApp-contentText'>鉴于当前网络环境的复杂性与开放性，您在使用大模型过程中输入的信息，存在被外网非法采集的风险。为切实保障数据使用安全，维护公司及客户的核心利益，请务必谨慎操作，严禁输入任何敏感信息，包括但不限于客户信息（姓名、联系方式、资产状况等）、公司信息（财务数据、战略规划、未公开业务信息等）、交易策略（投资组合方案、风险控制模型、买卖时机决策等）及其他机密数据。</div>
            </div>
          </div>
        </div>
        <div className={isBottom ? "RiskTipFooter" : "RiskTipFooterDisable"} onClick={handleAgree}>
          {hasAgreedRisk ? '确定' : '我已阅读并同意'}
        </div>
      </div>
      <img src={CloseIcon} alt='' className='RiskTipClose' onClick={handleClose} />
    </div>
  );
};
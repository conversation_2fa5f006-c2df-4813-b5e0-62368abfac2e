/*
 * @Author: 016645
 * @Description: API请求和数据处理工具函数
 * @Date: 2025-03-20 10:48:56
 */
import { request as h5request } from '@ht/h5-utils';
import { workerCode } from './sse.worker';
import  getBaseUrl, {TBaseUrl}   from '../../utils/getBaseUrl';
import { ReferenceProps } from '../../components/References/interface';


const { request } = h5request;

/**
 * 深度合并两个对象
 * @param obj1 - 目标对象
 * @param obj2 - 源对象
 * @returns 合并后的新对象
 */
export function deepMerge(obj1: any, obj2: any) {
  const result = { ...obj1 };
  for (const key in obj2) {
    if (
      obj2.hasOwnProperty(key)
      && typeof obj2[key] === 'object'
      && obj2[key] !== null
      && !Array.isArray(obj2[key])
      && typeof result[key] === 'object'
      && result[key] !== null
      && !Array.isArray(result[key])
    ) {
      result[key] = deepMerge(result[key], obj2[key]);
    } else {
      result[key] = obj2[key];
    }
  }
  return result;
}

/**
 * 构建完整URL
 * @param baseUrl - 基础URL
 * @param url - 路径或完整URL
 * @returns 格式化后的完整URL
 */
export function getUrl(baseUrl: TBaseUrl, url: string): string {
  //替换baseurl
  const newbase = getBaseUrl(baseUrl);
  // 检查 url 是否已经以 http:// 或 https:// 开头
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return newbase.replace(/\/+$/, '') + '/' + url.replace(/^\/+/, '');
}

/**
 * 将对象转换为URL查询字符串
 * @param query - 要转换的对象
 * @returns 格式化的查询字符串
 */
export function queryToString(query: Record<string, unknown>): string {
  return Object.keys(query)
    .map(
      (key) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(query[key] as any)}`
    )
    .join('&');
}

/**
 * 数据处理函数
 */

/**
 * 检查字符串是否为有效的JSON
 * @param str - 要检查的字符串
 * @returns 是否为有效JSON
 */
export function isValidJSON(str: string): boolean {
  if (typeof str !== 'string') return false;
  
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 处理结构化数据
 */
export const handleStructuredData = (data: any, originResult: any, conversationId: string) => {
  const {
    event,
    conversation_id: initConversationId, 
    task_id: taskId, 
    id, 
    answer,
    traceId,
    packetId,
  } = data;

  let result = {...originResult, endFlag: false};
  if (traceId) result.traceId = traceId;

  if(event === 'agent_thought' && data?.shootIntention === false) {
    result.taskId = taskId;
    result.shootIntention = false;
    result.backUpTalking = data?.backUpTalking;
  } else if(event === 'agent_thought_end') {
    // 思考耗时
    result = {
      ...result,
      taskId,
      thinkTime: data?.latency,
    }
  } else if (event === 'tool_message') {
    const { message_title: messageTitle } = data;
    const messageArr = [];
    if(messageTitle && messageTitle !== '') {
      messageArr.push(
        {
          taskId,
          type: 'thinking',
          content: {
            text: `**${messageTitle}**\n\n`,
          },
        }
      );
    }
    if(answer) {
      messageArr.push(
        {
          taskId,
          type: 'thinking',
          content: {
            text: `${answer}\n\n`,
          },
        }
      );
    }
    result = {
      ...result,
      conversationId: conversationId || initConversationId,
      taskId,
      messageId: id,
      list: [...(result.list || []), ...messageArr],
    }
  } else if (event === 'message' && answer) {
    const answerStr = answer
      //.replace(/\n/g, '')
      // 给属性名添加双引号
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":')
      // 处理可能的单引号
      .replace(/'/g, '"')
      // 移除对象或数组最后一个元素后的逗号
      .replace(/,\s*([}\]])/g, '$1')
      // 处理可能的多余空格
      // .trim();
    
    result = {
      ...result,
      conversationId: conversationId || initConversationId,
      taskId,
      messageId: id,
      packetId,
      list: [...(result.list || []), {
        taskId,
        type: 'markdown',
        content: {
          text: answerStr,
        },
      }],
    };
  } else if (event === 'knowledge_retrieve_end' || 'agent_thought_end') {
    // 文件数据结构的格式
    // {
    //   event: string;
    //   conversation_id: string;
    //   task_id: string;
    //   id: string;
    //   answer: string;
    //   latency: number;
    //   input_tokens: number;
    //   output_tokens: number;
    //   docs: {
    //     outputList: {
    //       metadata: {
    //         document_name: string;
    //         document_obs_url: string;
    //         document_type: number;
    //         document_url: string;
    //       };
    //       output: string;
    //       slice_id: string;
    //     }
    //   }
    // }
    if (data.docs || data.observation) {
      // 文件索引 hiagent原始事件
      const { outputList } = data.docs || data.observation || {};
      const references: ReferenceProps = outputList.map((item: { metadata: {
        document_name: string; // hiagent转换后的文件名称
        document_obs_url: string;
        document_type: number;
        document_url: string;
        file_name?: string; // 原生文件名称
        storage_gateway_id?: string; // 文件存储网关id
      }, output: string; slice_id: string; }) => ({
        source: 'doc',
        title: item.metadata.document_name,
        content: item.output,
        url: item.metadata.document_url,
        slice_id: item.slice_id,
        file_name: item.metadata.file_name,
        storage_gateway_id: item.metadata.storage_gateway_id,
      }));
      result = {
        conversationId: conversationId || initConversationId,
        taskId,
        messageId: id,
        endFlag: false,
        list: [{
          taskId,
          type: 'references',
          references,
        }, ...(result.list || [])],
      };
    } else {
      // 默认做网址进行处理 // TODO待确认网址类型的数据结构

    }
  } else if (event === 'message_cost') {
    const {
      input_tokens: inputTokens,
      output_tokens: outputTokens,
      latency,
    } = data;
    result = {
      ...result,
      totalTime: latency,
      totalTokens: inputTokens + outputTokens,
    }
  } else if (event === 'suggestion') {
    const { suggested_questions: suggestedQuestions } = data;
    const relatedQuestionList = suggestedQuestions.map((item: string) => ({
      title: item,
      content: item,
      url: null
    }))
    result = {
      ...result,
      relatedQuestionList,
    }
  } else if (event === 'message_end') {
    result = {
      ...result,
      endFlag: true,
    }
  }
  return result;
}
/**
 * 解析SSE数据（hiAgent格式）
 * @param rawData - 原始SSE数据字符串
 * @param responseTransfer - 可选的响应转换器函数
 * @returns 解析后的数据对象
 * @throws 解析失败时抛出错误
 */
export const parseSSEData = (dataObject: any) => {
  const { rawData, conversationId, responseTransfer, isAibag } = dataObject;
  const charNum = isAibag ? 5 : 10;
  try {
    // 分离 event 和 data
    let result: any = {};
    const chunks = rawData.split('\n\n');
  
    chunks.forEach((chunk: any) => {
      const lines = chunk.split('\n');
      
      lines.forEach((line: string) => {
        if (line.startsWith('data:') || isValidJSON(line)) {
          try {
            // 只解析 data 部分的 JSON 字符串
            let jsonStr = '';
            // 如果数据已经是JSON字符串则不需要截取
            if(isValidJSON(line)) {
              jsonStr = line;
            } else {
              jsonStr = line.slice(charNum).trim();
            }
            
            if (jsonStr !== '') {
              if (isValidJSON(jsonStr)) {
                let resultData = {};
                // 解析aibag接口
                if(isAibag) {
                  let allData = JSON.parse(jsonStr) || {};
                  let packetId = allData?.packetId;
                  let midResultBINDATA = JSON.parse(jsonStr)?.BINDATA || JSON.parse(jsonStr) || {};
                  if(typeof midResultBINDATA === 'string') {
                    midResultBINDATA = JSON.parse(midResultBINDATA);
                  }
                  const traceId = midResultBINDATA?.reqTraceId || midResultBINDATA?.traceId;
                  // const packetId = midResultBINDATA?.packetId || '';
                  let midResultData = midResultBINDATA?.resultData?.answerInfos[0];
                  if(midResultData?.type === 'stream') {
                    const midDataStr = midResultData?.text;
                    if(midDataStr) {
                      const midJsonStr = midDataStr.slice(charNum).trim();
                      if (isValidJSON(midJsonStr)) {
                        resultData = {...JSON.parse(midJsonStr), traceId, packetId};
                      }
                    }
                  }
                } else {
                  resultData = JSON.parse(jsonStr);
                }
                const finalResultData = responseTransfer 
                  ? responseTransfer(resultData) 
                  : resultData;
                result = handleStructuredData(finalResultData, result, conversationId);
              }
            }
          } catch (e) {
            console.error('解析 data 失败:', e);
          }
        }
      });
    });
    
    return result;
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'SSE 数据解析错误');
  }
};

/**
 * 解析SSE数据（自定义格式）
 * @param rawData - 原始SSE数据字符串
 * @param responseTransfer - 响应转换器函数
 * @returns 解析后的数据对象
 * @throws 解析失败时抛出错误
 */
export const parseSSEDataCustom = (rawData: string, responseTransfer: any, isAibag: boolean) => {
  // const charNum = isAibag ? 5 : 10;
  try {
    // 分离 event 和 data
    let result: any = {};
    const chunks = rawData.split('\n\n');
    
    chunks.forEach((chunk) => {
      const lines = chunk.split('\n');
      
      lines.forEach((line: string) => {
        if (line.startsWith('data:')) {
          try {
            // 只解析 data 部分的 JSON 字符串
            const jsonStr = line.slice(5).trim();
            
            if (jsonStr !== '') {
              if (isValidJSON(jsonStr)) {
                let resultData: any = {};
                // 解析aibag接口
                if(isAibag) {
                  const allData = JSON.parse(jsonStr) || {};
                  let initResultData: any = {};
                  initResultData = allData?.BINDATA || allData;
                  if(typeof initResultData === 'string') {
                    initResultData = JSON.parse(initResultData);
                  }
                  // 获取packetId
                  const packetId = initResultData?.packetId;
                  // 获取traceId
                  const traceId = initResultData?.reqTraceId || initResultData?.traceId;
                  let midResultData = initResultData?.resultData?.answerInfos[0];
                  if(midResultData?.type === 'stream') {
                    const midDataStr = midResultData?.text;
                    if(midDataStr) {
                      let midJsonStr = midDataStr;
                      if(midDataStr.startsWith('data:')) {
                        midJsonStr = midDataStr.slice(5).trim();
                      }
                      if (isValidJSON(midJsonStr)) {
                        const midJsonData = JSON.parse(midJsonStr);
                        resultData = {...midJsonData, traceId, packetId};
                      }
                    }
                  }
                  // 如果packetId为-1，则表示流式结束
                  if(packetId === '-1') resultData.endFlag = true;
                } else {
                  resultData = JSON.parse(jsonStr);
                }
         
                const finalResultData = responseTransfer 
                  ? responseTransfer(resultData) 
                  : resultData;
                const { list, ...rest } = finalResultData;
                
                if (list) {
                  const currentList = list.map((item: any) => (
                    { ...item, taskId: finalResultData?.taskId }
                  ));
                  
                  result = {
                    ...rest,
                    list: [...(result.list || []), ...currentList],
                  };
                } else {
                  result = {
                    ...result,
                    ...rest,
                  };
                }
              }
            }
          } catch (e) {
            console.error('解析 data 失败:', e);
          }
        }
      });
    });
    
    return result;
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'SSE 数据解析错误');
  }
};

/**
 * 请求函数
 */

/**
 * 网络请求接口选项
 */
interface FetchOptions {
  url: string;
  method?: string;
  type?: string;
  tcpRequest?: (data: any) => any;
  data?: any;
  headers?: object | null | undefined;
}

/**
 * 发送标准HTTP请求
 * @param options - 请求选项
 * @returns 响应数据
 * @throws 请求失败时抛出错误
 */
export async function fetchData(options: FetchOptions & { responseType?: string }) {
  const { 
    url, 
    method = 'post', 
    data, 
    headers, 
    responseType = 'json' 
  } = options;
  
  // 处理GET请求，将参数转换为查询字符串并附加到URL
  const isGetMethod = method.toLowerCase() === 'get';
  const finalUrl = isGetMethod && data ? `${url}${url.includes('?') ? '&' : '?'}${queryToString(data)}` : url;
  
  // eslint-disable-next-line compat/compat
  const response = await fetch(finalUrl, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    // 仅在非GET请求时设置body
    ...(!isGetMethod && { body: JSON.stringify(data) })
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  switch (responseType) {
    case 'json':
      return response.json();
      
    case 'text':
      const sseText = await response.text();
      try {
        return parseSSEData(sseText);
      } catch (err) {
        console.error('SSE解析错误:', err);
        return { data: { text: sseText } };
      } 
      
    case 'blob':
      return response.blob();
      
    case 'formData':
      return response.formData();
      
    case 'arrayBuffer':
      return response.arrayBuffer();
      
    default:
      return response.json();
  }
}

/**
 * SSE回调选项接口
 */
interface SSECallbackOptions {
  onMessage?: (data: any) => void;
  onMessageBatch?: (dataBatch: any[]) => void;
  onEnd?: () => void;
  onError?: (error: Error) => void;
  onRetry?: (attempt: number, error: Error, delay: number) => void;
}

/**
 * 重试配置接口
 */
interface RetryConfig {
  maxRetries?: number;     // 最大重试次数
  initialDelay?: number;   // 初始延迟(毫秒)
  maxDelay?: number;       // 最大延迟(毫秒)
  backoffFactor?: number;  // 退避因子
  shouldRetry?: (error: Error) => boolean; // 判断是否应该重试的函数
}

/**
 * 获取绝对URL
 * @param url - 输入URL
 * @returns 绝对URL
 */
function getAbsoluteUrl(url: string): string {
  // 已经是绝对URL则直接返回
  if (url.match(/^https?:\/\//)) {
    return url;
  }
  
  // 获取当前页面的origin
  const origin = typeof window !== 'undefined' ? 
    window.location.origin : 
    'http://localhost'; // 默认值，实际应根据环境确定
  
  // 构建绝对URL
  return url.startsWith('/') ? 
    origin + url : 
    origin + '/' + url;
}

/**
 * 创建处理SSE数据的Worker
 */
function createSSEWorker(): Worker {
  // 创建Worker内联代码
  const workerBlob = new Blob([workerCode], { type: 'application/javascript' });
  // eslint-disable-next-line compat/compat
  return new Worker(URL.createObjectURL(workerBlob));
}

// 全局活跃请求管理
let activeSSERequest: {
  terminate: () => void;
  worker: Worker | null;
  isActive: boolean;
} | null = null;

/**
 * 发送SSE流式请求
 * 使用Worker处理高负载数据，避免主线程阻塞
 * @param options - 请求选项
 * @param callbackOptions - 回调函数选项
 * @returns 清理函数，用于终止请求和释放资源
 */
export function fetchSSE(options: FetchOptions, callbackOptions: SSECallbackOptions): (() => void) {
  const { url, method = 'post', data, headers, type } = options;
  const { messageInterval = 30, perMessageNum = 1 } = data || {}; // 默认消息间隔为100ms
  
  // 是否启用调试日志
  const enableDebugLogs = false;
  
  // 调试日志函数
  const debugLog = (...args: any[]) => {
    if (enableDebugLogs) {
      console.log(`[SSE Debug ${new Date().toISOString()}]`, ...args);
    }
  };
  
  // 处理GET请求，将参数转换为查询字符串并附加到URL
  const isGetMethod = method.toLowerCase() === 'get';
  const finalUrl = isGetMethod && data ? `${url}${url.includes('?') ? '&' : '?'}${queryToString(data)}` : url;
  
  try {
    // 确保URL是绝对路径
    const absoluteUrl = getAbsoluteUrl(finalUrl);
    debugLog('开始SSE请求:', absoluteUrl);

    // 如果有活跃的请求，立即终止它
    if (activeSSERequest && activeSSERequest.isActive) {
      debugLog('检测到活跃请求，立即终止');
      activeSSERequest.terminate();
      activeSSERequest = null;
    }

    // 创建Worker处理SSE数据流
    const worker = createSSEWorker();
    
    // 跟踪Worker活动状态
    let isActive = true;
    let totalMessagesReceived = 0;
    let totalMessagesProcessed = 0;
    
    // 声明提前需要的函数引用
    let terminateWorker: () => void;
    let finalizeSseRequest: () => void;
    
    // 终止Worker并清理资源的函数
    terminateWorker = () => {
      if (isActive) {
        debugLog('终止SSE请求，清理资源');
        isActive = false;
        if (worker) {
          worker.terminate();
        }
        // 清理全局活跃请求状态
        if (activeSSERequest && activeSSERequest.worker === worker) {
          activeSSERequest = null;
        }
      }
    };
    
    // 完成SSE请求的函数 - 确保所有回调都执行后再清理资源
    finalizeSseRequest = () => {
      if (!isActive) return; // 防止重复调用
      
      debugLog(`请求完成。总接收: ${totalMessagesReceived}, 总处理: ${totalMessagesProcessed}`);
      
      // 调用onEnd回调
      try {
        callbackOptions.onEnd?.();
      } catch (err) {
        console.error('执行onEnd回调时出错:', err);
      }
      
      // 清理资源
      terminateWorker();
    };

    // 设置当前请求为活跃状态
    activeSSERequest = {
      terminate: terminateWorker,
      worker,
      isActive: true
    };

    // 监听Worker返回的消息
    worker.onmessage = (event) => {
      // 如果请求已被终止，不再处理消息
      if (!isActive) return;
      
      const { type: eventType, data: sseData, error } = event.data;
      
      switch (eventType) {
        case 'onMessage':
          // 增加待处理消息计数和总接收计数
          totalMessagesReceived++;
          debugLog(`收到消息 #${totalMessagesReceived}`);
          
          // 使用requestIdleCallback异步处理消息，避免阻塞主线程
          const processMessage = () => {
            // 再次检查是否仍然活跃
            if (!isActive) return;
            
            try {
              callbackOptions.onMessage?.(sseData);
              totalMessagesProcessed++;
              debugLog(`消息处理完成 ${totalMessagesProcessed}/${totalMessagesReceived}`);
            } catch (err) {
              console.error('处理SSE消息时出错:', err);
            }
          };
          
          // 优先使用requestIdleCallback，降级到setTimeout
          if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
            (window as any).requestIdleCallback(processMessage, { timeout: 100 });
          } else {
            setTimeout(processMessage, 0);
          }
          break;
          
        case 'onEnd':
          debugLog('收到流结束事件');
          
          // 延迟一小段时间后完成请求，确保最后的UI更新
          setTimeout(() => {
            finalizeSseRequest();
          }, 50);
          break;
          
        case 'onError':
          if (error) {
            debugLog('收到错误:', error);
            const err = new Error(error);
            callbackOptions.onError?.(err);
          }
          break;
      }
    };
    
    // 设置错误处理
    worker.onerror = (err) => {
      console.error('Worker错误:', err);
      debugLog('Worker错误:', err.message);
      callbackOptions.onError?.(new Error(`Worker错误: ${err.message}`));
      terminateWorker();
    };

    // 发送初始化消息到Worker
    worker.postMessage({
      finalUrl: absoluteUrl,
      url,
      type,
      method,
      headers,
      isGetMethod,
      data,
      messageInterval,
      perMessageNum,
    });
    
    debugLog('SSE请求已初始化');
    
    // 返回终止函数，允许用户手动终止请求
    return terminateWorker;
  } catch (err) {
    // 捕获fetch或流处理过程中的错误并向上抛出
    console.error('SSE请求初始化失败:', err);
    if (err instanceof Error) {
      throw err;
    } else {
      throw new Error('未知的SSE请求错误');
    }
  }
}

// 全局活跃TCP请求管理
let activeTcpSSERequest: {
  terminate: () => void;
  isActive: boolean;
} | null = null;

export function fetchTcpSSE(options: FetchOptions, callbackOptions: SSECallbackOptions): (() => void) {
  const { url, data, tcpRequest } = options;
  const { messageInterval = 30, perMessageNum = 1 } = data || {}; // 默认消息间隔为100ms
  
  // 消息队列和状态管理
  const messageQueue: any[] = [];
  let isProcessing = false;
  let isActive = true;
  let totalMessagesReceived = 0;
  let totalMessagesProcessed = 0;
  
  // 调试日志函数
  const debugLog = (...args: any[]) => {
    if (false) { // 可配置是否启用调试日志
      console.log(`[TCP SSE Debug ${new Date().toISOString()}]`, ...args);
    }
  };
  
  // 如果有活跃的TCP请求，立即终止它
  if (activeTcpSSERequest && activeTcpSSERequest.isActive) {
    debugLog('检测到活跃TCP请求，立即终止');
    activeTcpSSERequest.terminate();
    activeTcpSSERequest = null;
  }
  
  // 处理单条消息
  const processNextMessage = () => {
    if (messageQueue.length === 0) {
      isProcessing = false;
      return;
    }
    
    isProcessing = true;
    const message = messageQueue.splice(0, perMessageNum);
    
    try {
      // 每次处理一条完整消息
      const sseData = message[0];
      callbackOptions.onMessage?.(sseData);
      totalMessagesProcessed++;
      debugLog(`TCP消息处理完成 ${totalMessagesProcessed}/${totalMessagesReceived}`);
    } catch (err) {
      console.error('处理TCP SSE消息时出错:', err);
    }
    
    // 控制处理间隔
    setTimeout(processNextMessage, messageInterval);
  };
  
  // 启动消息处理
  const startProcessing = () => {
    if (isProcessing) return;
    
    if (messageQueue.length > 0) {
      processNextMessage();
    }
  };
  
  // 添加消息到队列
  const addMessageToQueue = (sseData: any) => {
    if (!isActive) return;
    
    totalMessagesReceived++;
    debugLog(`收到TCP消息 #${totalMessagesReceived}`);
    messageQueue.push(sseData);
    startProcessing();
  };
  
  // 终止函数
  const terminate = () => {
    if (isActive) {
      debugLog('终止TCP SSE请求，清理资源');
      isActive = false;
      messageQueue.length = 0; // 清空队列
      // 清理全局活跃TCP请求状态
      if (activeTcpSSERequest) {
        activeTcpSSERequest = null;
      }
    }
  };
  
  // 完成函数
  const finalize = () => {
    if (!isActive) return;
    
    debugLog(`TCP请求完成。总接收: ${totalMessagesReceived}, 总处理: ${totalMessagesProcessed}`);
    
    // 处理队列中剩余的消息
    if (messageQueue.length > 0) {
      setTimeout(() => {
        while (messageQueue.length > 0) {
          const message = messageQueue.splice(0, 1);
          try {
            callbackOptions.onMessage?.(message[0]);
          } catch (err) {
            console.error('处理剩余TCP消息时出错:', err);
          }
        }
        
        // 调用onEnd回调
        try {
          callbackOptions.onEnd?.();
        } catch (err) {
          console.error('执行TCP onEnd回调时出错:', err);
        }
        
        terminate();
      }, 30);
    } else {
      // 直接调用onEnd回调
      try {
        callbackOptions.onEnd?.();
      } catch (err) {
        console.error('执行TCP onEnd回调时出错:', err);
      }
      terminate();
    }
  };
  
  // 设置当前TCP请求为活跃状态
  activeTcpSSERequest = {
    terminate,
    isActive: true
  };
  
  if(tcpRequest) {
    tcpRequest({
      url,
      params: data,
      onMessage: (sseData: any) => {
        addMessageToQueue(sseData);
      },
      onEnd: () => {
        finalize();
      },
      onError: (error: any) => {
        debugLog('TCP请求错误:', error);
        callbackOptions.onError?.(error instanceof Error ? error : new Error(String(error)));
        terminate();
      }
    });
  }
  
  // 返回清理函数
  return terminate;
}

/**
 * 发送SSE流式请求（带重连重试功能）
 * @param options - 请求选项
 * @param callbackOptions - 回调函数选项
 * @param retryConfig - 重试配置
 * @returns 清理函数，用于终止请求和释放资源
 */
export function fetchSSEWithRetry(
  options: FetchOptions, 
  callbackOptions: SSECallbackOptions,
  retryConfig?: RetryConfig
): (() => void) {
  // 默认重试配置
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    shouldRetry = () => true,
  } = retryConfig || {};

  let attempt = 0;
  let currentDelay = initialDelay;
  let cleanup: (() => void) | null = null;
  let isActive = true;

  // 包装回调函数，添加重试逻辑
  const wrappedCallbacks: SSECallbackOptions = {
    ...callbackOptions,
    onError: (err) => {
      // 先调用原始错误处理
      callbackOptions.onError?.(err);
      
      // 如果已经停止或达到最大重试次数，不再重试
      if (!isActive || attempt >= maxRetries || !shouldRetry(err)) {
        return;
      }
      
      // 计算重试延迟
      attempt++;
      currentDelay = Math.min(currentDelay * backoffFactor, maxDelay);
      
      // 通知即将重试
      callbackOptions.onRetry?.(attempt, err, currentDelay);
      
      // 延迟后重试
      setTimeout(() => {
        if (isActive) {
          // 清理旧的连接
          if (cleanup) {
            cleanup();
            cleanup = null;
          }
          
          // 重新连接
          cleanup = fetchSSE(options, wrappedCallbacks);
        }
      }, currentDelay);
    }
  };
  
  // 首次尝试连接
  try {
    cleanup = fetchSSE(options, wrappedCallbacks);
  } catch (error) {
    // 处理初始化错误
    const err = error instanceof Error ? error : new Error(String(error));
    wrappedCallbacks.onError?.(err);
  }
  
  // 返回清理函数
  return () => {
    isActive = false;
    if (cleanup) {
      cleanup();
    }
  };
}

/**
 * 站内请求接口选项
 */
interface MSFetchOptions {
  action: string;
  url: string;
  method?: string;
  paramsKey?: string;
  data?: any;
  headers?: any;
}

/**
 * 发送站内请求
 * @param options - 请求选项
 * @returns 响应数据
 * @throws 请求失败时抛出错误
 */
export async function fetchMS(options: MSFetchOptions) {
  const { action, url, method = 'post', paramsKey = 'params', data, headers } = options;
  
  try {
    const finalUrl = method === 'GET' ? `${url}?${queryToString(data)}` : url;
    const requestConfig: any = {
      action,
      path: finalUrl,
      ...headers,
    };
    
    // 参数包裹的key动态传入
    if (paramsKey) {
      requestConfig[paramsKey] = JSON.stringify(data);
    }
    
    const response = await request(requestConfig);
    return response;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : `站内请求失败, ${url}`);
  }
}

/**
 * 对象属性名首字母转小写,支持多层级
 * @param obj - 对象
 * @returns 新对象
 */
export function traverseObject(obj: any) {
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const newKey = key.charAt(0).toLowerCase() + key.slice(1);
      
      if (Array.isArray(value) && value !== null) {
        obj[newKey] = value.map(item => {
          if (typeof item === 'object' && item !== null) {
            return traverseObject(item);
          }
          return item;
        });
      } else if (typeof value === 'object') {
        obj[newKey] = traverseObject(value);
      }else {
        obj[newKey] = value;
      }
      delete obj[key];
    }
  }
  return obj;
}

/**
 * 清理所有活跃的SSE和TCP请求
 * 用于在组件卸载或需要强制清理时调用
 */
export function cleanupAllActiveRequests(): void {
  // 清理活跃的SSE请求
  if (activeSSERequest && activeSSERequest.isActive) {
    console.log('清理活跃的SSE请求');
    activeSSERequest.terminate();
    activeSSERequest = null;
  }
  
  // 清理活跃的TCP请求
  if (activeTcpSSERequest && activeTcpSSERequest.isActive) {
    console.log('清理活跃的TCP请求');
    activeTcpSSERequest.terminate();
    activeTcpSSERequest = null;
  }
}

/**
 * 获取当前活跃请求状态
 * @returns 包含SSE和TCP请求状态的对象
 */
export function getActiveRequestsStatus(): {
  hasActiveSSE: boolean;
  hasActiveTCP: boolean;
} {
  return {
    hasActiveSSE: !!(activeSSERequest && activeSSERequest.isActive),
    hasActiveTCP: !!(activeTcpSSERequest && activeTcpSSERequest.isActive),
  };
}
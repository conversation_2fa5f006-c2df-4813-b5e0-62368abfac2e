/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useFeedback } from '@ht/chatui';

export default () => {

  const { loading: conversationLoading, run: conversationRun } = useFeedback({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      manual: true,
      type: 'http',
    },
    requestParams: {
      userId: '016645',
      messageId: '01JXYP2P22YTR363AW7HS44CP2',
      score: 'good',
      appKey: 'd07gpo0im975aeu764t0',
    },
  });

  return (
    <div>
      <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>回答反馈评价</Button>
    </div>
    </div>
    )
}

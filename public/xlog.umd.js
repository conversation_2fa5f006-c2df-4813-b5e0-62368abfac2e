!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).xlog={})}(this,(function(t){"use strict";if(typeof window === "undefined"){return}function n(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
n=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var o=t&&t.prototype instanceof p?t:p,i=Object.create(o.prototype),a=new S(r||[]);return i._invoke=function(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return T()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var u=x(a,n);if(u){if(u===f)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),i}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var f={};function p(){}function d(){}function v(){}var h={};c(h,i,(function(){return this}));var m=Object.getPrototypeOf,g=m&&m(m(O([])));g&&g!==t&&r.call(g,i)&&(h=g);var y=v.prototype=p.prototype=Object.create(h);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(o,i,a,u){var c=l(e[o],e,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,u)}),(function(e){n("throw",e,a,u)})):t.resolve(f).then((function(e){s.value=e,a(s)}),(function(e){return n("throw",e,a,u)}))}u(c.arg)}var o;this._invoke=function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}}function x(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method))return f;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var r=l(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,f;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function O(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return d.prototype=v,c(y,"constructor",v),c(v,"constructor",d),d.displayName=c(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,c(e,u,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new w(s(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,u,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=O,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:O(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}function r(e,t,n,r,o,i,a){try{var u=e[i](a),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,o)}function o(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function u(e){r(a,o,i,u,c,"next",e)}function c(e){r(a,o,i,u,c,"throw",e)}u(void 0)}))}}function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c=function(e,t){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},c(e,t)};function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function l(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function u(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,u)}c((r=r.apply(e,t||[])).next())}))}function f(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}function p(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function d(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function v(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function h(e){return this instanceof h?(this.v=e,this):new h(e)}function m(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(e,t||[]),i=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(e){o[e]&&(r[e]=function(t){return new Promise((function(n,r){i.push([e,t,n,r])>1||u(e,t)}))})}function u(e,t){try{!function(e){e.value instanceof h?Promise.resolve(e.value.v).then(c,s):l(i[0][2],e)}(o[e](t))}catch(e){l(i[0][3],e)}}function c(e){u("next",e)}function s(e){u("throw",e)}function l(e,t){e(t),i.shift(),i.length&&u(i[0][0],i[0][1])}}function g(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=p(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,o){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,o,(t=e[n](t)).done,t.value)}))}}}function y(e){return"function"==typeof e}function b(e){var t=e((function(e){Error.call(e),e.stack=(new Error).stack}));return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var w=b((function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}));function x(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var _=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._teardowns=null}var t;return e.prototype.unsubscribe=function(){var e,t,n,r,o;if(!this.closed){this.closed=!0;var i=this._parentage;if(i)if(this._parentage=null,Array.isArray(i))try{for(var a=p(i),u=a.next();!u.done;u=a.next()){u.value.remove(this)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}else i.remove(this);var c=this.initialTeardown;if(y(c))try{c()}catch(e){o=e instanceof w?e.errors:[e]}var s=this._teardowns;if(s){this._teardowns=null;try{for(var l=p(s),f=l.next();!f.done;f=l.next()){var h=f.value;try{O(h)}catch(e){o=null!=o?o:[],e instanceof w?o=v(v([],d(o)),d(e.errors)):o.push(e)}}}catch(e){n={error:e}}finally{try{f&&!f.done&&(r=l.return)&&r.call(l)}finally{if(n)throw n.error}}}if(o)throw new w(o)}},e.prototype.add=function(t){var n;if(t&&t!==this)if(this.closed)O(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._teardowns=null!==(n=this._teardowns)&&void 0!==n?n:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&x(t,e)},e.prototype.remove=function(t){var n=this._teardowns;n&&x(n,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e}(),E=_.EMPTY;function S(e){return e instanceof _||e&&"closed"in e&&y(e.remove)&&y(e.add)&&y(e.unsubscribe)}function O(e){y(e)?e():e.unsubscribe()}var T={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},I={setTimeout:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=I.delegate;return((null==n?void 0:n.setTimeout)||setTimeout).apply(void 0,v([],d(e)))},clearTimeout:function(e){var t=I.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function k(e){I.setTimeout((function(){var t=T.onUnhandledError;if(!t)throw e;t(e)}))}function N(){}var R=null;function A(e){if(T.useDeprecatedSynchronousErrorHandling){var t=!R;if(t&&(R={errorThrown:!1,error:null}),e(),t){var n=R,r=n.errorThrown,o=n.error;if(R=null,r)throw o}}else e()}var P=function(e){function t(t){var n=e.call(this)||this;return n.isStopped=!1,t?(n.destination=t,S(t)&&t.add(n)):n.destination=M,n}return s(t,e),t.create=function(e,t,n){return new L(e,t,n)},t.prototype.next=function(e){this.isStopped||this._next(e)},t.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(_),L=function(e){function t(t,n,r){var o,i=e.call(this)||this;if(y(t))o=t;else if(t){var a;o=t.next,n=t.error,r=t.complete,i&&T.useDeprecatedNextContext?(a=Object.create(t)).unsubscribe=function(){return i.unsubscribe()}:a=t,o=null==o?void 0:o.bind(a),n=null==n?void 0:n.bind(a),r=null==r?void 0:r.bind(a)}return i.destination={next:o?C(o):N,error:C(null!=n?n:D),complete:r?C(r):N},i}return s(t,e),t}(P);function C(e,t){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{e.apply(void 0,v([],d(t)))}catch(e){k(e)}}}function D(e){throw e}var M={closed:!0,next:N,error:D,complete:N},j="function"==typeof Symbol&&Symbol.observable||"@@observable";function B(e){return e}function H(e){return 0===e.length?B:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}var F=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(e,t,n){var r,o=this,i=(r=e)&&r instanceof P||function(e){return e&&y(e.next)&&y(e.error)&&y(e.complete)}(r)&&S(r)?e:new L(e,t,n);return A((function(){var e=o,t=e.operator,n=e.source;i.add(t?t.call(i,n):n?o._subscribe(i):o._trySubscribe(i))})),i},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var n=this;return new(t=U(t))((function(t,r){var o=new L({next:function(t){try{e(t)}catch(e){r(e),o.unsubscribe()}},error:r,complete:t});n.subscribe(o)}))},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[j]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return H(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=U(e))((function(e,n){var r;t.subscribe((function(e){return r=e}),(function(e){return n(e)}),(function(){return e(r)}))}))},e.create=function(t){return new e(t)},e}();function U(e){var t;return null!==(t=null!=e?e:T.Promise)&&void 0!==t?t:Promise}function q(e){return function(t){if(function(e){return y(null==e?void 0:e.lift)}(t))return t.lift((function(t){try{return e(t,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}var z=function(e){function t(t,n,r,o,i){var a=e.call(this,t)||this;return a.onFinalize=i,a._next=n?function(e){try{n(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=o?function(e){try{o(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=r?function(){try{r()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return s(t,e),t.prototype.unsubscribe=function(){var t,n=this.closed;e.prototype.unsubscribe.call(this),!n&&(null===(t=this.onFinalize)||void 0===t||t.call(this))},t}(P),W=b((function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})),G=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return s(t,e),t.prototype.lift=function(e){var t=new J(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new W},t.prototype.next=function(e){var t=this;A((function(){var n,r;if(t._throwIfClosed(),!t.isStopped){var o=t.observers.slice();try{for(var i=p(o),a=i.next();!a.done;a=i.next()){a.value.next(e)}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}}))},t.prototype.error=function(e){var t=this;A((function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var n=t.observers;n.length;)n.shift().error(e)}}))},t.prototype.complete=function(){var e=this;A((function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}}))},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,n=t.hasError,r=t.isStopped,o=t.observers;return n||r?E:(o.push(e),new _((function(){return x(o,e)})))},t.prototype._checkFinalizedStatuses=function(e){var t=this,n=t.hasError,r=t.thrownError,o=t.isStopped;n?e.error(r):o&&e.complete()},t.prototype.asObservable=function(){var e=new F;return e.source=this,e},t.create=function(e,t){return new J(e,t)},t}(F),J=function(e){function t(t,n){var r=e.call(this)||this;return r.destination=t,r.source=n,r}return s(t,e),t.prototype.next=function(e){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===n||n.call(t,e)},t.prototype.error=function(e){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===n||n.call(t,e)},t.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},t.prototype._subscribe=function(e){var t,n;return null!==(n=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==n?n:E},t}(G),V={now:function(){return(V.delegate||Date).now()},delegate:void 0},$=function(e){function t(t,n,r){void 0===t&&(t=1/0),void 0===n&&(n=1/0),void 0===r&&(r=V);var o=e.call(this)||this;return o._bufferSize=t,o._windowTime=n,o._timestampProvider=r,o._buffer=[],o._infiniteTimeWindow=!0,o._infiniteTimeWindow=n===1/0,o._bufferSize=Math.max(1,t),o._windowTime=Math.max(1,n),o}return s(t,e),t.prototype.next=function(t){var n=this,r=n.isStopped,o=n._buffer,i=n._infiniteTimeWindow,a=n._timestampProvider,u=n._windowTime;r||(o.push(t),!i&&o.push(a.now()+u)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),n=this._infiniteTimeWindow,r=this._buffer.slice(),o=0;o<r.length&&!e.closed;o+=n?1:2)e.next(r[o]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this,t=e._bufferSize,n=e._timestampProvider,r=e._buffer,o=e._infiniteTimeWindow,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){for(var a=n.now(),u=0,c=1;c<r.length&&r[c]<=a;c+=2)u=c;u&&r.splice(0,u+1)}},t}(G),K=function(e){function t(t,n){return e.call(this)||this}return s(t,e),t.prototype.schedule=function(e,t){return this},t}(_),X={setInterval:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=X.delegate;return((null==n?void 0:n.setInterval)||setInterval).apply(void 0,v([],d(e)))},clearInterval:function(e){var t=X.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},Y=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r.pending=!1,r}return s(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var n=this.id,r=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(r,n,t)),this.pending=!0,this.delay=t,this.id=this.id||this.requestAsyncId(r,this.id,t),this},t.prototype.requestAsyncId=function(e,t,n){return void 0===n&&(n=0),X.setInterval(e.flush.bind(e,this),n)},t.prototype.recycleAsyncId=function(e,t,n){if(void 0===n&&(n=0),null!=n&&this.delay===n&&!1===this.pending)return t;X.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var n=this._execute(e,t);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var n,r=!1;try{this.work(e)}catch(e){r=!0,n=e||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),n},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,n=this.scheduler,r=n.actions;this.work=this.state=this.scheduler=null,this.pending=!1,x(r,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(K),Z=function(){function e(t,n){void 0===n&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(e,t,n){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(n,t)},e.now=V.now,e}(),Q=new(function(e){function t(t,n){void 0===n&&(n=Z.now);var r=e.call(this,t,n)||this;return r.actions=[],r._active=!1,r._scheduled=void 0,r}return s(t,e),t.prototype.flush=function(e){var t=this.actions;if(this._active)t.push(e);else{var n;this._active=!0;do{if(n=e.execute(e.state,e.delay))break}while(e=t.shift());if(this._active=!1,n){for(;e=t.shift();)e.unsubscribe();throw n}}},t}(Z))(Y),ee=Q,te=new F((function(e){return e.complete()}));function ne(e){return e&&y(e.schedule)}function re(e){return ne((t=e)[t.length-1])?e.pop():void 0;var t}var oe=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function ie(e){return y(null==e?void 0:e.then)}function ae(e){return y(e[j])}function ue(e){return Symbol.asyncIterator&&y(null==e?void 0:e[Symbol.asyncIterator])}function ce(e){return new TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var se="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function le(e){return y(null==e?void 0:e[se])}function fe(e){return m(this,arguments,(function(){var t,n,r;return f(this,(function(o){switch(o.label){case 0:t=e.getReader(),o.label=1;case 1:o.trys.push([1,,9,10]),o.label=2;case 2:return[4,h(t.read())];case 3:return n=o.sent(),r=n.value,n.done?[4,h(void 0)]:[3,5];case 4:return[2,o.sent()];case 5:return[4,h(r)];case 6:return[4,o.sent()];case 7:return o.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}}))}))}function pe(e){return y(null==e?void 0:e.getReader)}function de(e){if(e instanceof F)return e;if(null!=e){if(ae(e))return o=e,new F((function(e){var t=o[j]();if(y(t.subscribe))return t.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")}));if(oe(e))return r=e,new F((function(e){for(var t=0;t<r.length&&!e.closed;t++)e.next(r[t]);e.complete()}));if(ie(e))return n=e,new F((function(e){n.then((function(t){e.closed||(e.next(t),e.complete())}),(function(t){return e.error(t)})).then(null,k)}));if(ue(e))return ve(e);if(le(e))return t=e,new F((function(e){var n,r;try{for(var o=p(t),i=o.next();!i.done;i=o.next()){var a=i.value;if(e.next(a),e.closed)return}}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}e.complete()}));if(pe(e))return ve(fe(e))}var t,n,r,o;throw ce(e)}function ve(e){return new F((function(t){(function(e,t){var n,r,o,i;return l(this,void 0,void 0,(function(){var a,u;return f(this,(function(c){switch(c.label){case 0:c.trys.push([0,5,6,11]),n=g(e),c.label=1;case 1:return[4,n.next()];case 2:if((r=c.sent()).done)return[3,4];if(a=r.value,t.next(a),t.closed)return[2];c.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return u=c.sent(),o={error:u},[3,11];case 6:return c.trys.push([6,,9,10]),r&&!r.done&&(i=n.return)?[4,i.call(n)]:[3,8];case 7:c.sent(),c.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}}))}))})(e,t).catch((function(e){return t.error(e)}))}))}function he(e,t,n,r,o){void 0===r&&(r=0),void 0===o&&(o=!1);var i=t.schedule((function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()}),r);if(e.add(i),!o)return i}function me(e,t){return void 0===t&&(t=0),q((function(n,r){n.subscribe(new z(r,(function(n){return he(r,e,(function(){return r.next(n)}),t)}),(function(){return he(r,e,(function(){return r.complete()}),t)}),(function(n){return he(r,e,(function(){return r.error(n)}),t)})))}))}function ge(e,t){return void 0===t&&(t=0),q((function(n,r){r.add(e.schedule((function(){return n.subscribe(r)}),t))}))}function ye(e,t){if(!e)throw new Error("Iterable cannot be null");return new F((function(n){he(n,t,(function(){var r=e[Symbol.asyncIterator]();he(n,t,(function(){r.next().then((function(e){e.done?n.complete():n.next(e.value)}))}),0,!0)}))}))}function be(e,t){if(null!=e){if(ae(e))return function(e,t){return de(e).pipe(ge(t),me(t))}(e,t);if(oe(e))return function(e,t){return new F((function(n){var r=0;return t.schedule((function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())}))}))}(e,t);if(ie(e))return function(e,t){return de(e).pipe(ge(t),me(t))}(e,t);if(ue(e))return ye(e,t);if(le(e))return function(e,t){return new F((function(n){var r;return he(n,t,(function(){r=e[se](),he(n,t,(function(){var e,t,o;try{t=(e=r.next()).value,o=e.done}catch(e){return void n.error(e)}o?n.complete():n.next(t)}),0,!0)})),function(){return y(null==r?void 0:r.return)&&r.return()}}))}(e,t);if(pe(e))return function(e,t){return ye(fe(e),t)}(e,t)}throw ce(e)}function we(e,t){return t?be(e,t):de(e)}function xe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=re(e);return we(e,n)}function _e(e,t){return q((function(n,r){var o=0;n.subscribe(new z(r,(function(n){r.next(e.call(t,n,o++))})))}))}var Ee=Array.isArray;function Se(e){return _e((function(t){return function(e,t){return Ee(t)?e.apply(void 0,v([],d(t))):e(t)}(e,t)}))}function Oe(e,t,n){return void 0===n&&(n=1/0),y(t)?Oe((function(n,r){return _e((function(e,o){return t(n,e,r,o)}))(de(e(n,r)))}),n):("number"==typeof t&&(n=t),q((function(t,r){return function(e,t,n,r,o,i,a,u){var c=[],s=0,l=0,f=!1,p=function(){!f||c.length||s||t.complete()},d=function(e){return s<r?v(e):c.push(e)},v=function(e){i&&t.next(e),s++;var u=!1;de(n(e,l++)).subscribe(new z(t,(function(e){null==o||o(e),i?d(e):t.next(e)}),(function(){u=!0}),void 0,(function(){if(u)try{s--;for(var e=function(){var e=c.shift();a?he(t,a,(function(){return v(e)})):v(e)};c.length&&s<r;)e();p()}catch(e){t.error(e)}})))};return e.subscribe(new z(t,d,(function(){f=!0,p()}))),function(){null==u||u()}}(t,r,e,n)})))}function Te(e){return void 0===e&&(e=1/0),Oe(B,e)}function Ie(){return Te(1)}var ke=["addListener","removeListener"],Ne=["addEventListener","removeEventListener"],Re=["on","off"];function Ae(e,t,n,r){if(y(n)&&(r=n,n=void 0),r)return Ae(e,t,n).pipe(Se(r));var o=d(function(e){return y(e.addEventListener)&&y(e.removeEventListener)}(e)?Ne.map((function(r){return function(o){return e[r](t,o,n)}})):function(e){return y(e.addListener)&&y(e.removeListener)}(e)?ke.map(Pe(e,t)):function(e){return y(e.on)&&y(e.off)}(e)?Re.map(Pe(e,t)):[],2),i=o[0],a=o[1];if(!i&&oe(e))return Oe((function(e){return Ae(e,t,n)}))(de(e));if(!i)throw new TypeError("Invalid event target");return new F((function(e){var t=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.next(1<t.length?t:t[0])};return i(t),function(){return a(t)}}))}function Pe(e,t){return function(n){return function(r){return e[n](t,r)}}}function Le(e,t,n){void 0===e&&(e=0),void 0===n&&(n=ee);var r=-1;return null!=t&&(ne(t)?n=t:r=t),new F((function(t){var o,i=(o=e)instanceof Date&&!isNaN(o)?+e-n.now():e;i<0&&(i=0);var a=0;return n.schedule((function(){t.closed||(t.next(a++),0<=r?this.schedule(void 0,r):t.complete())}),i)}))}function Ce(e,t){return void 0===e&&(e=0),void 0===t&&(t=Q),e<0&&(e=0),Le(e,e,t)}function De(e,t){return q((function(n,r){var o=0;n.subscribe(new z(r,(function(n){return e.call(t,n,o++)&&r.next(n)})))}))}function Me(e){return function(t){for(var n=[],r=function(r){n.push(de(e[r]).subscribe(new z(t,(function(e){if(n){for(var o=0;o<n.length;o++)o!==r&&n[o].unsubscribe();n=null}t.next(e)}))))},o=0;n&&!t.closed&&o<e.length;o++)r(o)}}function je(e,t){return void 0===t&&(t=null),t=null!=t?t:e,q((function(n,r){var o=[],i=0;n.subscribe(new z(r,(function(n){var a,u,c,s,l=null;i++%t==0&&o.push([]);try{for(var f=p(o),d=f.next();!d.done;d=f.next()){(m=d.value).push(n),e<=m.length&&(l=null!=l?l:[]).push(m)}}catch(e){a={error:e}}finally{try{d&&!d.done&&(u=f.return)&&u.call(f)}finally{if(a)throw a.error}}if(l)try{for(var v=p(l),h=v.next();!h.done;h=v.next()){var m=h.value;x(o,m),r.next(m)}}catch(e){c={error:e}}finally{try{h&&!h.done&&(s=v.return)&&s.call(v)}finally{if(c)throw c.error}}}),(function(){var e,t;try{for(var n=p(o),i=n.next();!i.done;i=n.next()){var a=i.value;r.next(a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}r.complete()}),void 0,(function(){o=null})))}))}function Be(e){for(var t,n,r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];var i=null!==(t=re(r))&&void 0!==t?t:Q,a=null!==(n=r[0])&&void 0!==n?n:null,u=r[1]||1/0;return q((function(t,n){var r=[],o=!1,c=function(e){var t=e.buffer;e.subs.unsubscribe(),x(r,e),n.next(t),o&&s()},s=function(){if(r){var t=new _;n.add(t);var o={buffer:[],subs:t};r.push(o),he(t,i,(function(){return c(o)}),e)}};null!==a&&a>=0?he(n,i,s,a,!0):o=!0,s();var l=new z(n,(function(e){var t,n,o=r.slice();try{for(var i=p(o),a=i.next();!a.done;a=i.next()){var s=a.value,l=s.buffer;l.push(e),u<=l.length&&c(s)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}}),(function(){for(;null==r?void 0:r.length;)n.next(r.shift().buffer);null==l||l.unsubscribe(),n.complete(),n.unsubscribe()}),void 0,(function(){return r=null}));t.subscribe(l)}))}function He(e){return q((function(t,n){var r,o=null,i=!1;o=t.subscribe(new z(n,void 0,void 0,(function(a){r=de(e(a,He(e)(t))),o?(o.unsubscribe(),o=null,r.subscribe(n)):i=!0}))),i&&(o.unsubscribe(),o=null,r.subscribe(n))}))}function Fe(e,t,n,r,o){return function(i,a){var u=n,c=t,s=0;i.subscribe(new z(a,(function(t){var n=s++;c=u?e(c,t,n):(u=!0,t),r&&a.next(c)}),o&&function(){u&&a.next(c),a.complete()}))}}var Ue=function(e,t){return e.push(t),e};function qe(){return q((function(e,t){(function(e,t){return q(Fe(e,t,arguments.length>=2,!1,!0))})(Ue,[])(e).subscribe(t)}))}function ze(e,t){return y(t)?Oe(e,t,1):Oe(e,1)}function We(e){return e<=0?function(){return te}:q((function(t,n){var r=0;t.subscribe(new z(n,(function(t){++r<=e&&(n.next(t),e<=r&&n.complete())})))}))}function Ge(e,t){return t?function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Ie()(we(e,re(e)))}(t.pipe(We(1),q((function(e,t){e.subscribe(new z(t,N))}))),n.pipe(Ge(e)))}:Oe((function(t,n){return e(t,n).pipe(We(1),function(e){return _e((function(){return e}))}(t))}))}function Je(e,t){return q((function(n,r){var o=null,i=0,a=!1,u=function(){return a&&!o&&r.complete()};n.subscribe(new z(r,(function(n){null==o||o.unsubscribe();var a=0,c=i++;de(e(n,c)).subscribe(o=new z(r,(function(e){return r.next(t?t(n,e,c,a++):e)}),(function(){o=null,u()})))}),(function(){a=!0,u()})))}))}function Ve(e,t,n){var r=y(e)||t||n?{next:e,error:t,complete:n}:e;return r?q((function(e,t){var n;null===(n=r.subscribe)||void 0===n||n.call(r);var o=!0;e.subscribe(new z(t,(function(e){var n;null===(n=r.next)||void 0===n||n.call(r,e),t.next(e)}),(function(){var e;o=!1,null===(e=r.complete)||void 0===e||e.call(r),t.complete()}),(function(e){var n;o=!1,null===(n=r.error)||void 0===n||n.call(r,e),t.error(e)}),(function(){var e,t;o&&(null===(e=r.unsubscribe)||void 0===e||e.call(r)),null===(t=r.finalize)||void 0===t||t.call(r)})))})):B}function $e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"!==r&&"+"!==r&&"?"!==r)if("\\"!==r)if("{"!==r)if("}"!==r)if(":"!==r)if("("!==r)t.push({type:"CHAR",index:n,value:e[n++]});else{var o=1,i="";if("?"===e[u=n+1])throw new TypeError('Pattern cannot start with "?" at '+u);for(;u<e.length;)if("\\"!==e[u]){if(")"===e[u]){if(0==--o){u++;break}}else if("("===e[u]&&(o++,"?"!==e[u+1]))throw new TypeError("Capturing groups are not allowed at "+u);i+=e[u++]}else i+=e[u++]+e[u++];if(o)throw new TypeError("Unbalanced pattern at "+n);if(!i)throw new TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:i}),n=u}else{for(var a="",u=n+1;u<e.length;){var c=e.charCodeAt(u);if(!(c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||95===c))break;a+=e[u++]}if(!a)throw new TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:a}),n=u}else t.push({type:"CLOSE",index:n,value:e[n++]});else t.push({type:"OPEN",index:n,value:e[n++]});else t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});else t.push({type:"MODIFIER",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,o=void 0===r?"./":r,i="[^"+Xe(t.delimiter||"/#?")+"]+?",a=[],u=0,c=0,s="",l=function(e){if(c<n.length&&n[c].type===e)return n[c++].value},f=function(e){var t=l(e);if(void 0!==t)return t;var r=n[c],o=r.type,i=r.index;throw new TypeError("Unexpected "+o+" at "+i+", expected "+e)},p=function(){for(var e,t="";e=l("CHAR")||l("ESCAPED_CHAR");)t+=e;return t};c<n.length;){var d=l("CHAR"),v=l("NAME"),h=l("PATTERN");if(v||h){var m=d||"";-1===o.indexOf(m)&&(s+=m,m=""),s&&(a.push(s),s=""),a.push({name:v||u++,prefix:m,suffix:"",pattern:h||i,modifier:l("MODIFIER")||""})}else{var g=d||l("ESCAPED_CHAR");if(g)s+=g;else if(s&&(a.push(s),s=""),l("OPEN")){m=p();var y=l("NAME")||"",b=l("PATTERN")||"",w=p();f("CLOSE"),a.push({name:y||(b?u++:""),pattern:y&&!b?i:b,prefix:m,suffix:w,modifier:l("MODIFIER")||""})}else f("END")}}return a}function Ke(e,t){var n=[];return function(e,t,n){void 0===n&&(n={});var r=n.decode,o=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var i=r[0],a=r.index,u=Object.create(null),c=function(e){if(void 0===r[e])return"continue";var n=t[e-1];"*"===n.modifier||"+"===n.modifier?u[n.name]=r[e].split(n.prefix+n.suffix).map((function(e){return o(e,n)})):u[n.name]=o(r[e],n)},s=1;s<r.length;s++)c(s);return{path:i,index:a,params:u}}}(Qe(e,n,t),n,t)}function Xe(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function Ye(e){return e&&e.sensitive?"":"i"}function Ze(e,t,n){return function(e,t,n){void 0===n&&(n={});for(var r=n.strict,o=void 0!==r&&r,i=n.start,a=void 0===i||i,u=n.end,c=void 0===u||u,s=n.encode,l=void 0===s?function(e){return e}:s,f="["+Xe(n.endsWith||"")+"]|$",p="["+Xe(n.delimiter||"/#?")+"]",d=a?"^":"",v=0,h=e;v<h.length;v++){var m=h[v];if("string"==typeof m)d+=Xe(l(m));else{var g=Xe(l(m.prefix)),y=Xe(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var b="*"===m.modifier?"?":"";d+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+b}else d+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else d+="("+m.pattern+")"+m.modifier;else d+="(?:"+g+y+")"+m.modifier}}if(c)o||(d+=p+"?"),d+=n.endsWith?"(?="+f+")":"$";else{var w=e[e.length-1],x="string"==typeof w?p.indexOf(w[w.length-1])>-1:void 0===w;o||(d+="(?:"+p+"(?="+f+"))?"),x||(d+="(?="+p+"|"+f+")")}return new RegExp(d,Ye(n))}($e(e,n),t,n)}function Qe(e,t,n){return e instanceof RegExp?function(e,t){if(!t)return e;for(var n=/\((?:\?<(.*?)>)?(?!\?)/g,r=0,o=n.exec(e.source);o;)t.push({name:o[1]||r++,prefix:"",suffix:"",modifier:"",pattern:""}),o=n.exec(e.source);return e}(e,t):Array.isArray(e)?function(e,t,n){var r=e.map((function(e){return Qe(e,t,n).source}));return new RegExp("(?:"+r.join("|")+")",Ye(n))}(e,t,n):Ze(e,t,n)}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var et={exports:{}},tt={exports:{}};!function(e,t){e.exports=function(){function e(e){return!isNaN(parseFloat(e))&&isFinite(e)}function t(e){return e.charAt(0).toUpperCase()+e.substring(1)}function n(e){return function(){return this[e]}}var r=["isConstructor","isEval","isNative","isToplevel"],o=["columnNumber","lineNumber"],i=["fileName","functionName","source"],a=["args"],u=["evalOrigin"],c=r.concat(o,i,a,u);function s(e){if(e)for(var n=0;n<c.length;n++)void 0!==e[c[n]]&&this["set"+t(c[n])](e[c[n]])}s.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof s)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new s(e)}},toString:function(){var e=this.getFileName()||"",t=this.getLineNumber()||"",n=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+t+":"+n+")":"[eval]:"+t+":"+n:r?r+" ("+e+":"+t+":"+n+")":e+":"+t+":"+n}},s.fromString=function(e){var t=e.indexOf("("),n=e.lastIndexOf(")"),r=e.substring(0,t),o=e.substring(t+1,n).split(","),i=e.substring(n+1);if(0===i.indexOf("@"))var a=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(i,""),u=a[1],c=a[2],l=a[3];return new s({functionName:r,args:o||void 0,fileName:u,lineNumber:c||void 0,columnNumber:l||void 0})};for(var l=0;l<r.length;l++)s.prototype["get"+t(r[l])]=n(r[l]),s.prototype["set"+t(r[l])]=function(e){return function(t){this[e]=Boolean(t)}}(r[l]);for(var f=0;f<o.length;f++)s.prototype["get"+t(o[f])]=n(o[f]),s.prototype["set"+t(o[f])]=function(t){return function(n){if(!e(n))throw new TypeError(t+" must be a Number");this[t]=Number(n)}}(o[f]);for(var p=0;p<i.length;p++)s.prototype["get"+t(i[p])]=n(i[p]),s.prototype["set"+t(i[p])]=function(e){return function(t){this[e]=String(t)}}(i[p]);return s}()}(tt),function(e,t){var n,r,o,i;e.exports=(n=tt.exports,r=/(^|@)\S+:\d+/,o=/^\s*at .*(\S+:\d+|\(native\))/m,i=/^(eval@)?(\[native code])?$/,{parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(o))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(o)}),this).map((function(e){e.indexOf("(eval ")>-1&&(e=e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));var t=e.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,""),r=t.match(/ (\(.+\)$)/);t=r?t.replace(r[0],""):t;var o=this.extractLocation(r?r[1]:t),i=r&&t||void 0,a=["eval","<anonymous>"].indexOf(o[0])>-1?void 0:o[0];return new n({functionName:i,fileName:a,lineNumber:o[1],columnNumber:o[2],source:e})}),this)},parseFFOrSafari:function(e){return e.stack.split("\n").filter((function(e){return!e.match(i)}),this).map((function(e){if(e.indexOf(" > eval")>-1&&(e=e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===e.indexOf("@")&&-1===e.indexOf(":"))return new n({functionName:e});var t=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=e.match(t),o=r&&r[1]?r[1]:void 0,i=this.extractLocation(e.replace(t,""));return new n({functionName:o,fileName:i[0],lineNumber:i[1],columnNumber:i[2],source:e})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)/i,r=e.message.split("\n"),o=[],i=2,a=r.length;i<a;i+=2){var u=t.exec(r[i]);u&&o.push(new n({fileName:u[2],lineNumber:u[1],source:r[i]}))}return o},parseOpera10:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=e.stacktrace.split("\n"),o=[],i=0,a=r.length;i<a;i+=2){var u=t.exec(r[i]);u&&o.push(new n({functionName:u[3]||void 0,fileName:u[2],lineNumber:u[1],source:r[i]}))}return o},parseOpera11:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(r)&&!e.match(/^Error created at/)}),this).map((function(e){var t,r=e.split("@"),o=this.extractLocation(r.pop()),i=r.shift()||"",a=i.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;i.match(/\(([^)]*)\)/)&&(t=i.replace(/^[^(]+\(([^)]*)\)$/,"$1"));var u=void 0===t||"[arguments not available]"===t?void 0:t.split(",");return new n({functionName:a,args:u,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:e})}),this)}})}(et);var nt=et.exports,rt="customError",ot="customEvent",it="customTime",at="XLOG_UNREPORTED",ut="XLOG_REPORTING_IDS",ct={BASE_INFO:"baseInfo",PERFORMANCE:"performance",XHR:"xhr",FETCH:"fetch",USER_EVENT:"userEvent",WINODW_ERROR:"windowError",SOURCE_ERROR:"sourceError",ROUTE_CHANGE:"routeChange",CONSOLE:"console",UNHANDLED_REJECTION:"unhandledrejection",OBSERVABLE_ERROR:"observableError",BLANK_SCREEN:"blankScreen",BUSINESS_POINT:"businessPoint",DEVELOP_CUSTOM:"developCustom"},st={PAGE_LOAD:"load",PAGE_INIT:"pageInit"},lt=0,ft=1,pt=2,dt="batch",vt="sourceError",ht="unhandledrejection",mt="windowError",gt="componentError",yt="xhr",bt="fetch",wt="reqxml",xt="pc_web",_t="log_init",Et="observableError",St="captureError",Ot="customReportBlankScreen",Tt="componentErrorBlankScreen",It="activeBlankScreen",kt="baseInfo",Nt="performance",Rt="xhr",At="fetch",Pt="userEvent",Lt="windowError",Ct="sourceError",Dt="routeChange",Mt="consoleMethods",jt="unhandledrejection",Bt="https:"===window.location.protocol?"https:":"http:",Ht={prd_inner_test:Bt+"//*************/dsj/collect/web/report",prd_outer_test:Bt+"//*************/dsj/collect/web/report",prd_inner:"http://eip.htsc.com.cn/HTAI/collect/web/report",prd_inner_new:"//eipnew.htsc.com.cn/HTAI/collect/web/report",prd_inner_fort:"http://************:8080/HTAI/collect/web/report",prd_outer:"https://crm.htsc.com.cn:3443/collect/web/report",uat_outer_test:Bt+"//***********:9111/HTAI/collect/web/report"},Ft={prd_inner_test:Bt+"//*************/dsj/collect/web/config",prd_outer_test:Bt+"//*************/dsj/collect/web/config",prd_inner:"http://eip.htsc.com.cn/HTAI/collect/web/config",prd_inner_new:"//eipnew.htsc.com.cn/HTAI/collect/web/config",prd_inner_fort:"http://************:8080/HTAI/collect/web/config",prd_outer:"https://crm.htsc.com.cn:3443/collect/web/config",uat_outer_test:Bt+"//***********:9111/HTAI/collect/web/config"},Ut="jsErrorBlankScreen",qt="sourceErrorBlankScreen",zt="unhandledrejectionBlankScreen",Wt=["IMG","AUDIO","VIDEO","SOURCE"],Gt="routePath",Jt="then",Vt="catch",$t="load",Kt="error",Xt="success",Yt="fail",Zt={TraceId:"TraceId",ChainId:"ChainId"},Qt="apiErrorLog",en="apiSuccessLog",tn="0",nn="1",rn=function(e){var t=e,n=t.length-1;return","===t[n]&&(t=t.substring(0,n)),t};function on(e,t,n,r,o,i,a){var u="";if(e.length){u+=t.spacingOuter;for(var c=n+t.indent,s=0;s<e.length;s++){if(u+=c,s===t.maxWidth){u+="…";break}s in e&&(u+=a(e[s],t,c,r,o,!1,i)),s<e.length-1?u+=","+t.spacingInner:t.min||(u+=",")}u+=t.spacingOuter+n}return u=rn(u)}function an(e,t,n,r,o,i,a){var u="",c=function(e,t){var n=Object.keys(e),r=null!==t?n.sort(t):n;return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((function(t){Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r}(e,t.compareKeys);if(c.length){u+=t.spacingOuter;for(var s=n+t.indent,l=0;l<c.length;l++){var f=c[l];u+=s+a(f,t,s,r,o,!1,i)+":"+a(e[f],t,s,r,o,!1,i),l<c.length-1?u+=","+t.spacingInner:t.min||(u+=",")}u+=t.spacingOuter+n}return u=rn(u)}function un(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function cn(e){return String(e).replace(/^Symbol\((.*)\)(.*)$/,"Symbol($1)")}var sn=Object.prototype.toString,ln=Date.prototype.toISOString,fn=Error.prototype.toString,pn=RegExp.prototype.toString,dn=function(e){return"function"==typeof e.constructor&&e.constructor.name||"Object"},vn={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,indent:2,maxDepth:1/0,maxWidth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0};function hn(e){return"["+fn.call(e)+"]"}var mn=function(){return vn.printFunctionName},gn=function(){return vn.escapeRegex},yn=function(){return vn.escapeString};function bn(e,t,n,r){if(!0===e||!1===e)return""+e;if(void 0===e)return'"undefined"';if(null===e)return"null";var o=typeof e;if("number"===o)return""+e;if("bigint"===o)return""+e;if("string"===o)return r?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===o)return'"'+un(e,t)+'"';if("symbol"===o)return'"'+cn(e)+'"';var i=sn.call(e);return"[object WeakMap]"===i?'"WeakMap {}"':"[object WeakSet]"===i?'"WeakSet {}"':"[object Function]"===i||"[object GeneratorFunction]"===i?'"'+un(e,t)+'"':"[object Symbol]"===i?'"'+cn(e)+'"':"[object Date]"===i?isNaN(+e)?'"Date { NaN }"':'"'+ln.call(e)+'"':"[object Error]"===i?'"'+hn(e)+'"':"[object RegExp]"===i?n?pn.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):'"'+pn.call(e)+'"':e instanceof Error?'"'+hn(e)+'"':null}function wn(e,t,n,r,o,i,a){var u=bn(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==u?u:xn(e,t,n,r,o,i,a)}function xn(e,t,n,r,o,i,a){if(-1!==o.indexOf(e))return"[Circular]";(o=o.slice()).push(e);var u=++r>t.maxDepth,c=t.min;if(t.callToJSON&&!u&&e.toJSON&&"function"==typeof e.toJSON&&!i)return wn(e.toJSON(),t,n,r,o,!0,a);var s=sn.call(e);if("[object Arguments]"===s){var l=on(e,t,n,r,o,a,wn);return u?"[Arguments]":a?"["+l+"]":(c?"":"Arguments ")+"["+l+"]"}if(function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(s)){var f=on(e,t,n,r,o,a,wn);return u?"["+e.constructor.name+"]":a?"["+f+"]":(c?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+f+"]"}if("[object Map]"===s){var p=function(e,t,n,r,o,i,a,u){void 0===u&&(u=":");var c="",s=0,l=e.next();if(!l.done){c+=t.spacingOuter;for(var f=n+t.indent;!l.done;){if(c+=f,s++===t.maxWidth){c+="…";break}var p=a(l.value[0],t,f,r,o,!1,i),d=a(l.value[1],t,f,r,o,!1,i);try{c+=""+('"'===p[0]?p:'"'+p+'"')+u+d}catch(e){c+=""+p+u+d}(l=e.next()).done?t.min||(c+=","):c+=","+t.spacingInner}c+=t.spacingOuter+n}return rn(c)}(e.entries(),t,n,r,o,a,wn,":");return u?"[Map]":a?"{"+p+"}":"Map {"+p+"}"}if("[object Set]"===s){var d=function(e,t,n,r,o,i,a){var u="",c=0,s=e.next();if(!s.done){u+=t.spacingOuter;for(var l=n+t.indent;!s.done;){if(u+=l,c++===t.maxWidth){u+="…";break}u+=a(s.value,t,l,r,o,!1,i),(s=e.next()).done?t.min||(u+=","):u+=","+t.spacingInner}u+=t.spacingOuter+n}return rn(u)}(e.values(),t,n,r,o,a,wn);return u?"[Set]":a?"["+d+"]":"Set ["+d+"]"}var v=an(e,t,n,r,o,a,wn);return u||function(e){return"undefined"!=typeof window&&e===window}(e)?"["+dn(e)+"]":a?"{"+v+"}":(c?"":t.printBasicPrototype||"Object"!==dn(e)?dn(e)+" ":"")+"{"+v+"}"}function _n(e,t){if("string"==typeof e)return e;if(void 0===e)return"undefined";var n=bn(e,mn(),gn(),yn());return null!==n?n:xn(e,{callToJSON:vn.callToJSON,compareKeys:vn.compareKeys,escapeRegex:gn(),escapeString:yn(),indent:"",maxDepth:vn.maxDepth,maxWidth:vn.maxWidth,min:vn.min,plugins:vn.plugins,printBasicPrototype:!0,printFunctionName:mn(),spacingInner:"",spacingOuter:""},"",0,[],!1,t)}var En="function"==typeof Buffer,Sn=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder?new TextEncoder:void 0),On=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),Tn=function(e){var t={};return e.forEach((function(e,n){return t[e]=n})),t}(On),In=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,kn=String.fromCharCode.bind(String),Nn=("function"==typeof Uint8Array.from&&Uint8Array.from.bind(Uint8Array),function(e){return e.replace(/=/g,"").replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"}))}),Rn=function(e){return e.replace(/[^A-Za-z0-9\+\/]/g,"")},An=function(e){for(var t,n,r,o,i="",a=e.length%3,u=0;u<e.length;){if((n=e.charCodeAt(u++))>255||(r=e.charCodeAt(u++))>255||(o=e.charCodeAt(u++))>255)throw new TypeError("invalid character found");i+=On[(t=n<<16|r<<8|o)>>18&63]+On[t>>12&63]+On[t>>6&63]+On[63&t]}return a?i.slice(0,a-3)+"===".substring(a):i},Pn="function"==typeof btoa?function(e){return btoa(e)}:En?function(e){return Buffer.from(e,"binary").toString("base64")}:An,Ln=En?function(e){return Buffer.from(e).toString("base64")}:function(e){for(var t=[],n=0,r=e.length;n<r;n+=4096)t.push(kn.apply(null,e.subarray(n,n+4096)));return Pn(t.join(""))},Cn=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?kn(192|t>>>6)+kn(128|63&t):kn(224|t>>>12&15)+kn(128|t>>>6&63)+kn(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return kn(240|t>>>18&7)+kn(128|t>>>12&63)+kn(128|t>>>6&63)+kn(128|63&t)},Dn=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,Mn=function(e){return e.replace(Dn,Cn)},jn=En?function(e){return Buffer.from(e,"utf8").toString("base64")}:Sn?function(e){return Ln(Sn.encode(e))}:function(e){return Pn(Mn(e))},Bn=function(e,t){return void 0===t&&(t=!1),t?Nn(jn(e)):jn(e)},Hn=function(e){if(e=e.replace(/\s+/g,""),!In.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));for(var t,n,r,o="",i=0;i<e.length;)t=Tn[e.charAt(i++)]<<18|Tn[e.charAt(i++)]<<12|(n=Tn[e.charAt(i++)])<<6|(r=Tn[e.charAt(i++)]),o+=64===n?kn(t>>16&255):64===r?kn(t>>16&255,t>>8&255):kn(t>>16&255,t>>8&255,255&t);return o},Fn=Bn,Un="";function qn(e){return zn.apply(this,arguments)}function zn(){return zn=o(n().mark((function e(t){var r,o,i,a,u;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=(r=window)&&r.xReplay&&null!=(o=window)&&null!=(i=o.xReplay)&&i.init){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,a=window.xReplay||{},u=a.init,e.next=6,u(t);case 6:Un=e.sent,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),console.error(e.t0);case 12:case"end":return e.stop()}}),e,null,[[2,9]])}))),zn.apply(this,arguments)}function Wn(){var e,t,n,r,o;if(null!=(e=window)&&e.xReplay&&null!=(t=window)&&null!=(n=t.xReplay)&&n.dump&&null!=(r=window)&&null!=(o=r.xReplay)&&o.getSessionId)try{var i=window.xReplay||{},a=i.getSessionId,u=i.dump;Un=a(),u()}catch(e){console.error(e)}}var Gn,Jn,Vn,$n=[];function Kn(e){return"SCRIPT"===e.tagName}(Vn=null==(Gn=document)||null==(Jn=Gn.head)?void 0:Jn.appendChild)&&(document.head.appendChild=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0];return Kn(r)&&$n.push(r),Vn.apply(this,t)});var Xn="jobid=",Yn=function(){var e,t,n=null==(e=window)||null==(t=e.navigator)?void 0:t.connection;if(n&&"object"==typeof n){var r=n||{};return{downlink:r.downlink,effectiveType:r.effectiveType,rtt:r.rtt}}return{}},Zn=function(e){var t,n,r=function(e){var t;if(yr(e))try{var n=e();hr(n)&&(t=n)}catch(e){}return t}((e||{}).getPackageInfo),o={ua:window.navigator.userAgent,width:window.innerWidth,height:window.innerHeight,screenWidth:null==(t=window.screen)?void 0:t.width,screenHeight:null==(n=window.screen)?void 0:n.height,url:window.location.href,xlogVersion:"4.1.0",network:Yn()};return r&&(o.packageInfo=r),o};function Qn(e){return e.lastUrl&&e.lastUrl===window.location.href?null==e?void 0:e.pv_id:(e.lastUrl=window.location.href,er())}function er(){var e=Date.now(),t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?n:3&n|8).toString(16)}));return t}var tr=function(){var e,t=er();return(e={})[Zt.TraceId]=t,e[Zt.ChainId]="0.0",e},nr=function(e,t){for(var n=(e||[]).length,r=0;r<n;r++){var o=e[r];if(0===t.indexOf(o))return!0}return!1},rr=function(e){return e.blankScreenPollingNum||3},or=function(e){return e.blankScreenPollingIntervalTime||1e3};var ir,ar=function(e){var t=(null==e?void 0:e.btn_title)||{};return null==t?void 0:t.storageId},ur=function(e,t,n){var r=e[t];r&&(e[t]=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];r.call.apply(r,[this].concat(t)),n.call.apply(n,[this].concat(t))})},cr=function(){try{var e=function(e){for(var t,n=document.cookie.split("; "),r=n.length,o=0;o<r;o++){var i=n[o].split("="),a=i[0],u=i[1];if(a===e){t=u;break}}return t}("sensorsdata2015jssdkcross");return JSON.parse(decodeURIComponent(e)).device_id}catch(e){return null}},sr=(ir=null,function(){if(ir)return ir;var e=cr(),t="XLOG_UUID",n=Date.now(),r=Math.floor(n/1e3/3600/24)+"_"+Math.random()+"_"+n,o="";try{var i=window.localStorage.getItem(t);i?o=i:(o=r,window.localStorage.setItem(t,o))}catch(e){o=r}return ir=e||o}),lr=function(e){var t=e.url,n=e.method,r=e.params,o=void 0===r?{}:r,i=e.callback,a=e.error,u=new XMLHttpRequest;u.open(n,t,!0),u.onreadystatechange=function(){if(4==u.readyState)if(200===u.status){var e={};try{e=u.responseText,e=JSON.parse(e)}catch(e){a&&a()}i&&i(e)}else a&&a(u.status)};var c=null;"POST"===n&&(c=Object.keys(o).map((function(e){return encodeURIComponent(e)+"="+encodeURIComponent(o[e])})).join("&")).length&&u.setRequestHeader("Content-type","application/x-www-form-urlencoded"),u.send(c)},fr=function(e){return Object.prototype.toString.call(e).toLowerCase().slice(8,-1)},pr=function(e){return"request"===fr(e)},dr=function(e){return"headers"===fr(e)},vr=function(e){return"array"===fr(e)},hr=function(e){return"object"===fr(e)},mr=function(e){return"string"===fr(e)},gr=function(e){return"number"===fr(e)},yr=function(e){return"function"===fr(e)},br=function(e){return"promise"===fr(e)},wr=function(e){return"undefined"===fr(e)},xr=function(e){return"error"===fr(e)?e.message+"\n "+e.stack:e},_r=function(e){for(var t=0;t<(arguments.length<=1?0:arguments.length-1);t++){var n=t+1<1||arguments.length<=t+1?void 0:arguments[t+1];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Er={init:function(){var e=Sr.get(at),t=Date.now();Er.updatelockStorageIds(),Object.keys(e).length>=100?Sr.set(at,{}):(Object.keys(e).forEach((function(n){var r=e[n].timestamp;(!r||t-r>864e6)&&delete e[n]})),Sr.set(at,e))},get:function(){return Sr.get(at)},getUnreportedKeys:function(){return Object.keys(Er.get())},set:function(e,t){var n=Sr.get(at);n[e]=t,Sr.set(at,n)},remove:function(e){var t=Sr.get(at),n=Er.getLockedIds();e.forEach((function(e){delete t[e],delete n[e]})),Sr.set(at,t),Er.setLockedIds(n)},lockStorageIds:function(e){if(null!=e&&e.length){var t=Er.getLockedIds(),n=Date.now();e.forEach((function(e){t[e]=n})),Er.setLockedIds(t)}},getLockedIds:function(){return Sr.get(ut)},setLockedIds:function(e){return Sr.set(ut,e)},updatelockStorageIds:function(){var e=Er.getLockedIds(),t=Date.now();for(var n in e){if(e.hasOwnProperty(n))t-e[n]>6e4&&delete e[n]}Er.setLockedIds(e)}},Sr={get:function(e){try{return JSON.parse(window.localStorage.getItem(e))||{}}catch(e){return{}}},set:function(e,t){try{return window.localStorage.setItem(e,JSON.stringify(t||{}))}catch(e){}}};function Or(e){var t=e.indexOf("//")+2,n=e.indexOf("/",t),r=e.indexOf("?"),o=e.indexOf("#");if(-1===n)return"/";if(-1===r&&-1===o)return e.slice(n);var i=n;return r>-1&&o>-1&&(i=r<o?r:o),r>-1&&-1===o&&(i=r),o>-1&&-1===r&&(i=o),n>i?"/":e.slice(n,i)}var Tr="hash",Ir="history",kr=function(e,t){return!!vr(e)&&e.indexOf(t)>-1};function Nr(e){var t=Rr(e),n=t.pagePath,r=t.pageHref,o=function(e){return null==e?void 0:e.pv_id}(e);return{pagePath:n,pageHref:r,pv_id:o,hiddenState:document.hidden?"hidden":"visible",visibilityState:document.visibilityState}}function Rr(e,t){void 0===t&&(t=window.location.href);var n=[Tr,Ir],r=e||{},o=r.routeConfig,i=void 0===o?{}:o,a=r.pagePathType,u=i||{},c=u.routeType,s=void 0===c?Tr:c,l=u.dynamicRoutes,f=void 0===l?[]:l,p=u.redirectRoute,d=void 0===p?{}:p;if(kr(n,s)){var v=function(e,t){return e===Tr?function(e){var t=e.indexOf("#"),n=e.indexOf("?");if(-1===t)return"/";if(-1===n&&t>-1)return e.slice(t+1);var r=e.indexOf("?",t);return r>-1?e.slice(t+1,r):e.slice(t+1)}(t):Or(t)}(s,t);v=function(e,t){return(null==t?void 0:t[e])||e}(v,d),v=function(e,t){var n=e,r=(t||[]).length;if(0===r)return n;for(var o=0;o<r;o++){var i=t[o];if(Ke(i)(n)){n=i;break}}return n}(v,f);var h=a===Gt?v:function(e,t,n){return e===Tr?Or(n)+"#"+t:""+t}(s,v,t);return{pageHref:t,pagePath:h}}console.error("路由类型只有hash、history")}var Ar=function(e,t){return Object.keys(e||{}).reduce((function(n,r){return t.indexOf(r)>-1&&(n[r]=e[r]),n}),{})},Pr=function(e){return[ct.BUSINESS_POINT,dt].indexOf(e)>-1},Lr=function(e,t,n){void 0===t&&(t=50),void 0===n&&(n=!1);try{return function(e,t){void 0===t&&(t=2);var n=e||"",r=1024*t;return n.length>r?n.slice(0,r):n}(_n(e,n),t)}catch(e){return""}},Cr=function(e,t){return void 0===t&&(t=50),Lr(e,t,!0)},Dr=function(e){var t=e||"",n=t.indexOf("?");return n>-1?t.slice(0,n):t},Mr=function(e,t){if(vr(t)&&mr(e))for(var n=0;n<t.length;n++)if((e||"").indexOf(t[n])>-1)return!0;return!1},jr=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t},Br=jr(Ht),Hr=function(){return function(e){if(e.type===ct.XHR){var t=null==e?void 0:e.content,n=(null==t?void 0:t.url)||"",r=Mr(n,Br);if(!n||r)return!1}return!0}},Fr=function(e){return(e||[]).reduce((function(e,t){kr(e,t)||e.push(t);var n=function(e){if(!mr(e))return e;var t=e.length-1;return'"'===e[0]&&'"'===e[t]?e.slice(1,t):e}(t);return kr(e,n)||e.push(n),e}),[])},Ur=function(e,t){var n=(null==e?void 0:e.ignoreJsErrorList)||[];return kr(n,t)},qr=function(e,t){for(var n=(null==e?void 0:e.ignoreConsoleList)||[],r=n.length,o=0;o<r;o++){var i=n[o];if(t.indexOf(i)>-1)return!0}return!1},zr=function(e){return"zlcft"===e},Wr=function(e){return"myTrack"===e};function Gr(e){var t=ct.BLANK_SCREEN,n=e||{},r=n.type,o=n.content;return kr([t],r)||o.page_id===t}var Jr=function(e){return"develop_custom"===e},Vr=function(e){var t=(null==e?void 0:e.myTrackConfig)||{};return t.getEnv&&(t.channel_env=t.getEnv(),delete t.getEnv),t},$r=function(e){var t=Vr(e);return function(e){return i({},(null==e?void 0:e.reportProxyConfig)||{},Ht)}(e)[t.channel_env]},Kr=function(e){try{var t=nt.parse(e);return t=t.map((function(e){var t=e.fileName,n=function(e){for(var t=document.querySelectorAll("script"),n=[],r=0;r<t.length;r++)n.push(t[r]);var o=n.concat($n).filter((function(e){return e.src&&e.src.indexOf(Xn)>-1})).map((function(e){return e.src}));if(0!==o.length){var i=o.find((function(t){var n=t.indexOf("?"),r=e.indexOf("?");return(n>-1?t.slice(0,n):t)===(r>-1?e.slice(0,r):e)}));if(i){var a=i.match(new RegExp("jobid=(\\d+)"));if(a)return a[1]}}}(t);if(e.filePath=function(e){var t=e.match(/^(https?:\/\/|htxweb:\/\/)[^\/\?#]+/);if(!t)return"";var n=t[0].length;return e.substring(n)}(t),n){var r=e.filePath;if(!(r.indexOf(Xn)>-1)){var o=-1!==r.indexOf("?")?"&":"?";e.filePath=""+r+o+Xn+n}e.jobId=n}return e})),t}catch(e){return}},Xr=function(e){var t=e.reason||{},n=t.message||t||"",r=Kr(t),o=r?Lr(r,50,!0):Lr(t.stack||"");return{limitErrorMessage:Lr(n),limitErrorStack:o}},Yr=function(e){var t=e.error||{},n=Kr(t),r=n?Lr(n,50,!0):Lr(null==t?void 0:t.stack);return{errorName:t.name||"",limitErrorMessage:Lr(e.message),limitErrorStack:r}},Zr=function(e,t,n,r){void 0===r&&(r="");var o=Nr(n);return i({recordId:Un,errorMessage:e,errorStack:t,errorName:r},o)},Qr=function(e,t){var n=Nr(t),r=e.tagName,o="";try{o=Lr(Fn(e.outerHTML))}catch(e){}var a;return i({tagName:r,sourcePath:"image"===r?null==e||null==(a=e.href)?void 0:a.animVal:e.src||e.href,recordId:Un,message:o},n)},eo=function(e){return void 0!==e&&(!!yr(e)||(console.error("hook function beforeReport defined error"),!1))},to=function(e,t){var n=!1,r=(t||{}).beforeReport;if(!eo(r))return!1;try{r(e)||(n=!0)}catch(e){}return n},no=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:"v2-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12)}},ro=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if("first-input"===e&&!("PerformanceEventTiming"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},oo=function(e,t){var n=function n(r){"pagehide"!==r.type&&"hidden"!==document.visibilityState||(e(r),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},io=function(e){addEventListener("pageshow",(function(t){t.persisted&&e(t)}),!0)},ao=function(e,t,n){var r;return function(o){t.value>=0&&(o||n)&&(t.delta=t.value-(r||0),(t.delta||void 0===r)&&(r=t.value,e(t)))}},uo=-1,co=function(){return"hidden"===document.visibilityState?0:1/0},so=function(){oo((function(e){var t=e.timeStamp;uo=t}),!0)},lo=function(){return uo<0&&(uo=co(),so(),io((function(){setTimeout((function(){uo=co(),so()}),0)}))),{get firstHiddenTime(){return uo}}},fo={},po=function(){return performance.now()},vo={},ho=function(e,t){return e+"-"+t},mo=function(e){return ho(e,"load")},go=function(e){return ho(e,"bootstrapped")},yo=function(e){return ho(e,"mounted")},bo=function(e){return ho(e,"unmounted")},wo=function(e){var t,n;return null==e||null==(t=e.detail)||null==(n=t.app)?void 0:n.name},xo=function(e,t,n){var r=Nr(n);e.next({type:ct.PERFORMANCE,subtype:"bigbox",timestamp:Date.now(),content:i({},t,r)})};function _o(e){return function(e){return e&&"string"==typeof e&&0===e.indexOf("parcel-")}(e)?"parcel":e}var Eo=function(e){return new F((function(t){window.addEventListener("bigbox:app-beforeload",(function(e){var t=wo(e);t&&(vo[mo(t)]=po())})),window.addEventListener("bigbox:app-loaded",(function(n){var r=wo(n);if(r){var o=mo(r);if(vo.hasOwnProperty(o)){var i=po()-vo[o];delete vo[o];var a=function(e){var t=null;try{var n=window[e+"Instance"];n&&(t=Object.prototype.hasOwnProperty.call(n,"createInstance")?"v2":"v1")}catch(e){}return t}(r),u={instance_name:_o(r),bigbox_load:i};a&&(u.bigbox_ver=a),xo(t,u,e)}}}))}))},So=function(e){var t=Eo(e),n=function(e){return new F((function(t){window.addEventListener("bigbox:app-beforebootstrap",(function(e){var t=wo(e);t&&(vo[go(t)]=po())})),window.addEventListener("bigbox:app-bootstrapped",(function(n){var r=wo(n);if(r){var o=go(r);if(vo.hasOwnProperty(o)){var i=po()-vo[o];delete vo[o],xo(t,{instance_name:_o(r),bigbox_bootstrap:i},e)}}}))}))}(e),r=function(e){return new F((function(t){window.addEventListener("bigbox:app-beforemount",(function(e){var t=wo(e);t&&(vo[yo(t)]=po())})),window.addEventListener("bigbox:app-mounted",(function(n){var r=wo(n);if(r){var o=yo(r);if(vo.hasOwnProperty(o)){var i=po()-vo[o];delete vo[o],xo(t,{instance_name:_o(r),bigbox_mount:i},e)}}}))}))}(e),o=function(e){return new F((function(t){window.addEventListener("bigbox:app-beforeunmount",(function(e){var t=wo(e);t&&(vo[bo(t)]=po())})),window.addEventListener("bigbox:app-unmounted",(function(n){var r=wo(n);if(r){var o=bo(r);if(vo.hasOwnProperty(o)){var i=po()-vo[o];delete vo[o],xo(t,{instance_name:_o(r),bigbox_unmount:i},e)}}}))}))}(e);return we([t,n,r,o]).pipe(Te())},Oo={t:!1,i:!1,o:3e4},To=window,Io=To.console,ko=document,No=To.navigator,Ro=To.performance,Ao=function(){return No.deviceMemory},Po=function(){return No.hardwareConcurrency},Lo=function(){return Ro&&!!Ro.getEntriesByType&&!!Ro.now&&!!Ro.mark},Co="4g",Do=!1,Mo=function(){return!!(Po()&&Po()<=4)||!!(Ao()&&Ao()<=4)},jo=function(e,t){switch(e){case"slow-2g":case"2g":case"3g":return!0;default:return Mo()||t}},Bo={u:!1},Ho=function(e){ko.hidden&&(e(),Bo.u=ko.hidden)},Fo=function(e){return parseFloat(e.toFixed(4))},Uo=function(e){return"number"!=typeof e?null:Fo(e/Math.pow(1024,2))},qo=[2e3,4e3],zo=[2500,4e3],Wo=[.1,.25],Go={ttfb:[200,500],fp:qo,fcp:qo,lcp:zo,lcpFinal:zo,fid:[100,300],cls:Wo,clsFinal:Wo,tbt:[300,600]},Jo=function(e,t){return Go[e]?t<=Go[e][0]?"good":t<=Go[e][1]?"needsImprovement":"poor":null},Vo=function(e,t,n){var r;r=function(){Bo.u&&e.indexOf("Final")<0||!Oo.analyticsTracker||Oo.analyticsTracker({metricName:e,data:t,eventProperties:n||{},navigatorInformation:No?{deviceMemory:Ao()||0,hardwareConcurrency:Po()||0,serviceWorkerStatus:"serviceWorker"in No?No.serviceWorker.controller?"controlled":"supported":"unsupported",isLowEndDevice:Mo(),isLowEndExperience:jo(Co,Do)}:{},vitalsScore:Jo(e,t)})},"requestIdleCallback"in To?To.requestIdleCallback(r,{timeout:3e3}):r()},$o=function(e,t,n){Object.keys(t).forEach((function(e){"number"==typeof t[e]&&(t[e]=Fo(t[e]))})),Vo(e,t,n)},Ko=function(e,t,n){var r=Fo(e);r<=Oo.o&&r>=0&&Vo(t,r,n)},Xo={},Yo={value:0},Zo={value:0},Qo={value:0},ei={value:{beacon:0,css:0,fetch:0,img:0,other:0,script:0,total:0,xmlhttprequest:0}},ti={value:0},ni=function(e){var t=e.pop();t&&!t.s&&t.value&&(Yo.value+=t.value)},ri={},oi=function(e,t){try{var n=new PerformanceObserver((function(e){t(e.getEntries())}));return n.observe({type:e,buffered:!0}),n}catch(e){Io.warn("Perfume.js:",e)}return null},ii=function(e){ri[e]&&ri[e].disconnect(),delete ri[e]},ai=function(e){var t=e.pop();t&&Ko(t.processingStart-t.startTime,"fid",{performanceEntry:t}),ii(1),Ko(Qo.value,"lcp"),ri[3]&&"function"==typeof ri[3].takeRecords&&ri[3].takeRecords(),Ko(Yo.value,"cls"),setTimeout((function(){Ko(ti.value,"tbt"),$o("dataConsumption",ei.value)}),1e4)},ui=function(e){e.forEach((function(e){if(!("self"!==e.name||e.startTime<Zo.value)){var t=e.duration-50;t>0&&(ti.value+=t)}}))},ci=function(e){e.forEach((function(e){"first-paint"===e.name?Ko(e.startTime,"fp"):"first-contentful-paint"===e.name&&(Zo.value=e.startTime,Ko(Zo.value,"fcp"),ri[4]=oi("longtask",ui),ii(0))}))},si=function(e){var t=e.pop();t&&(Qo.value=t.renderTime||t.loadTime)},li=function(e){e.forEach((function(e){e.identifier&&Ko(e.startTime,e.identifier)}))},fi=function(e){e.forEach((function(e){if(Oo.t&&$o("resourceTiming",e),e.decodedBodySize&&e.initiatorType){var t=e.decodedBodySize/1e3;ei.value[e.initiatorType]+=t,ei.value.total+=t}}))},pi=function(){ri[2]&&(Ko(Qo.value,"lcpFinal"),ii(2)),ri[3]&&("function"==typeof ri[3].takeRecords&&ri[3].takeRecords(),Ko(Yo.value,"clsFinal"),ii(3))},di=function(e){var t="usageDetails"in e?e.usageDetails:{};$o("storageEstimate",{quota:Uo(e.quota),usage:Uo(e.usage),caches:Uo(t.caches),indexedDB:Uo(t.indexedDB),serviceWorker:Uo(t.serviceWorkerRegistrations)})},vi=function(){function e(e){if(void 0===e&&(e={}),this.l="6.2.0",Oo.analyticsTracker=e.analyticsTracker,Oo.t=!!e.resourceTiming,Oo.i=!!e.elementTiming,Oo.o=e.maxMeasureTime||Oo.o,Lo()){"PerformanceObserver"in To&&(ri[0]=oi("paint",ci),ri[1]=oi("first-input",ai),ri[2]=oi("largest-contentful-paint",si),Oo.t&&oi("resource",fi),ri[3]=oi("layout-shift",ni),Oo.i&&oi("element",li)),void 0!==ko.hidden&&ko.addEventListener("visibilitychange",Ho.bind(this,pi));var t=function(){if(!Lo())return{};var e=Ro.getEntriesByType("navigation")[0];if(!e)return{};var t=e.responseStart,n=e.responseEnd;return{fetchTime:n-e.fetchStart,workerTime:e.workerStart>0?n-e.workerStart:0,totalTime:n-e.requestStart,downloadTime:n-t,timeToFirstByte:t-e.requestStart,headerSize:e.transferSize-e.encodedBodySize||0,dnsLookupTime:e.domainLookupEnd-e.domainLookupStart,redirectTime:e.redirectEnd-e.redirectStart}}();$o("navigationTiming",t),t.timeToFirstByte&&Ko(t.timeToFirstByte,"ttfb"),$o("networkInformation",function(){if("connection"in No){var e=No.connection;return"object"!=typeof e?{}:(Co=e.effectiveType,Do=!!e.saveData,{downlink:e.downlink,effectiveType:e.effectiveType,rtt:e.rtt,saveData:!!e.saveData})}return{}}()),No&&No.storage&&"function"==typeof No.storage.estimate&&No.storage.estimate().then(di)}}return e.prototype.start=function(e){Lo()&&!Xo[e]&&(Xo[e]=!0,Ro.mark("mark_"+e+"_start"),Bo.u=!1)},e.prototype.end=function(e,t){void 0===t&&(t={}),Lo()&&Xo[e]&&(Ro.mark("mark_"+e+"_end"),delete Xo[e],$o(e,Fo(function(e){Ro.measure(e,"mark_"+e+"_start","mark_"+e+"_end");var t=Ro.getEntriesByName(e).pop();return t&&"measure"===t.entryType?t.duration:-1}(e)),t))},e.prototype.endPaint=function(e,t){var n=this;setTimeout((function(){n.end(e,t)}))},e.prototype.clear=function(e){delete Xo[e],Ro.clearMarks&&(Ro.clearMarks("mark_"+e+"_start"),Ro.clearMarks("mark_"+e+"_end"))},e}(),hi=function(e){return new F((function(t){new vi({resourceTiming:!0,analyticsTracker:function(n){var r,o=null==n?void 0:n.metricName,a=null==n?void 0:n.data,u="";switch(o){case"fid":u="fid";break;case"cls":u="cls";break;case"tbt":u="tbt"}if(u){var c=Nr(e),s=e.initialPagePath;c.pagePath===s&&function(e,t){e.next({type:ct.PERFORMANCE,subtype:"timing",timestamp:Date.now(),content:t})}(t,i(((r={})[u]=a,r),c))}}})}))},mi=function(e){var t=new F((function(t){!function(e,t){var n,r=lo(),o=no("FCP"),i=function(e){"first-contentful-paint"===e.name&&(u&&u.disconnect(),e.startTime<r.firstHiddenTime&&(o.value=e.startTime,o.entries.push(e),n(!0)))},a=window.performance&&performance.getEntriesByName&&performance.getEntriesByName("first-contentful-paint")[0],u=a?null:ro("paint",i);(a||u)&&(n=ao(e,o,t),a&&i(a),io((function(r){o=no("FCP"),n=ao(e,o,t),requestAnimationFrame((function(){requestAnimationFrame((function(){o.value=performance.now()-r.timeStamp,n(!0)}))}))})))}((function(n){var r=Nr(e),o=null==n?void 0:n.value,a=e.initialPagePath;if(r.pagePath!==a||!o)return te;t.next({type:ct.PERFORMANCE,subtype:"timing",timestamp:Date.now(),content:i({fcp:o},r)})}))}));return t},gi=function(e){var t=new F((function(t){!function(e,t){var n,r=lo(),o=no("LCP"),i=function(e){var t=e.startTime;t<r.firstHiddenTime&&(o.value=t,o.entries.push(e),n())},a=ro("largest-contentful-paint",i);if(a){n=ao(e,o,t);var u=function(){fo[o.id]||(a.takeRecords().map(i),a.disconnect(),fo[o.id]=!0,n(!0))};["keydown","click"].forEach((function(e){addEventListener(e,u,{once:!0,capture:!0})})),oo(u,!0),io((function(r){o=no("LCP"),n=ao(e,o,t),requestAnimationFrame((function(){requestAnimationFrame((function(){o.value=performance.now()-r.timeStamp,fo[o.id]=!0,n(!0)}))}))}))}}((function(n){var r=Nr(e),o=null==n?void 0:n.value,a=e.initialPagePath;if(r.pagePath!==a||!o)return te;t.next({type:ct.PERFORMANCE,subtype:"timing",timestamp:Date.now(),content:i({lcp:o},r)})}))}));return t},yi=function(e){return Ae(window,"error").pipe(_e((function(t){var n=Yr(t),r=n.limitErrorMessage,o=n.limitErrorStack,i=n.errorName;return Ur(e,r)?te:(Wn(),{type:ct.WINODW_ERROR,content:Zr(r,o,e,i),timestamp:Date.now()})})),e.shouldWindowErrorRepeatReport?Ve((function(){})):(t=function(e){return""+(e&&e.content?e.content.errorMessage:"")},q((function(e,r){var o=new Set;e.subscribe(new z(r,(function(e){var n=t?t(e):e;o.has(n)||(o.add(n),r.next(e))}))),null==n||n.subscribe(new z(r,(function(){return o.clear()}),N))}))));var t,n},bi=["error"],wi=function(e){var t=e.consoleMethods?function(e){var t=["error","warn","log","info"];return vr(e)?e.filter((function(e){return t.indexOf(e)>-1})):(console.error("consoleMethods必须是一个数组！"),[])}(e.consoleMethods):bi,n=[];return t.forEach((function(t){var r=new F((function(n){ur(window.console,t,(function(){for(var r=Nr(e),o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];var c=a.map(xr),s=Lr(c);if(qr(e,s))return te;n.next({type:ct.CONSOLE,subtype:"console."+t,timestamp:Date.now(),content:i({message:s},r)})}))}));n.push(r)})),we(n).pipe(Te())},xi=[];["click"].forEach((function(e){var t=Ae(window,e).pipe(_e((function(t){var n=t.target||{};return{type:ct.USER_EVENT,subtype:"userEvent."+e,content:{x:t.clientX||t.pageX,y:t.clientY||t.pageY,target:{nodeName:n.nodeName,innerText:(n.innerText||"").slice(0,10)}}}})));xi.push(t)}));var _i=we(xi).pipe(Te()),Ei=20,Si=function(e){var t=e.params,n=e.response,r=e.errorMessage,o=e.reqHeader,a=e.resHeader;return i({},e,{params:Cr(t,Ei),response:Cr(n,Ei),errorMessage:Cr(r,Ei),reqHeader:Cr(o,Ei),resHeader:Cr(a,Ei)})},Oi=function(e){if("formdata"===fr(e))try{for(var t,n={},r=u(e.entries());!(t=r()).done;){var o=t.value,i=o[0],a=o[1];n[i]="string"===fr(a)?a:"--"}return n}catch(t){return e}return e};function Ti(e,t){var n=e[0],r=e[1],o=void 0===r?null:r,a=(t||{}).traceIdApiList,u=void 0===a?[]:a,c=function(e,t){try{Object.keys(t).forEach((function(n){e.append(n,t[n])}))}catch(e){}};if(pr(n)){var s=n.url,l=n.headers;if(nr(u,s))c(l,tr())}else if(nr(u,n)){var f=tr();o?dr(o.headers)?c(o.headers,f):o.headers=i({},o.headers||{},f):e[1]={headers:f}}}function Ii(e){var t=e[0],n=e[1],r=void 0===n?null:n,o=null,i=function(e,t){return{api:Dr(e),method:(null==t?void 0:t.toLowerCase())||"get"}};if(pr(t)){var a=t.url,c=t.headers,s=i(a,t.method),l=s.api,f=s.method,p={},d="";try{o=t.clone();for(var v,h=u(c.entries());!(v=h()).done;){var m=v.value,g=m[0],y=m[1];p[g]=y,g===Zt.TraceId.toLowerCase()&&(d=y)}}catch(e){}return{url:a,api:l,method:f,reqHeader:p,traceId:d,cloneRequest:o}}var b=i(t,null==r?void 0:r.method),w=b.api,x=b.method,_=(r||{}).headers,E={};if(dr(_))try{for(var S,O=u(_.entries());!(S=O()).done;){var T=S.value,I=T[0],k=T[1];E[I]=k}}catch(e){}else E=_||{};return{url:t,api:w,method:x,reqHeader:E,traceId:E[Zt.TraceId]||E[Zt.TraceId.toLowerCase()]||"",cloneRequest:o}}function ki(e,t,n){var r=e[0],o=e[1],i=void 0===o?null:o;if(pr(r))!function(e,t){try{var n=null==e?void 0:e.getReader();if(!window.TextDecoder||!n)return void t();var r=new TextDecoder("utf-8"),o="";n.read().then((function e(i){var a=i.done,u=i.value;if(a)try{var c=JSON.parse(o);t(c)}catch(e){t()}else{try{o+=r.decode(u)}catch(e){}n.read().then(e).catch((function(){t()}))}})).catch((function(){t()}))}catch(e){t()}}(null==t?void 0:t.body,(function(e){n({params:e})}));else{var a=(i||{}).body;n({params:Oi(a)})}}function Ni(e,t){var n=function(){for(var e=[],t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach((function(t){t.forEach((function(t){kr(e,t)||e.push(t)}))})),e}(["application/json","text/plain","text/xml","text/html"],t),r=!1;try{var o=e.headers.get("Content-Type");o&&nr(n,o.toLowerCase())&&(r=!0)}catch(e){}return r}function Ri(e){var t={};try{for(var n,r=u(e.entries());!(n=r()).done;){var o=n.value,i=o[0],a=o[1];t[i]=a}}catch(e){}return t}function Ai(e){var t={};try{e.getAllResponseHeaders().trim().split(/[\r\n]+/).filter(Boolean).forEach((function(e){var n=e.split(": "),r=n[0],o=n[1];t[r.trim()]=o}))}catch(e){}return t}function Pi(e,t){e.responseType&&"text"!==e.responseType?"document"===e.responseType?t(e.responseXML):function(e,t){if(window.FileReader)try{!function(e,t){var n=new FileReader;n.onload=function(){t(n.result)},n.onerror=function(){t(n.error)},n.readAsText(e)}(e,t)}catch(n){t(e)}else t(e)}(e.response,t):t(e.responseText)}function Li(e,t,n){var r=(e||{}).reqParamHandler;if(!yr(r))return t;var o=i({},t);try{o=i({},o,{params:r(n,o.url)})}catch(e){}return o}function Ci(e,t,n){var r=(e||{}).responseValidator;if(!yr(r))return t;var o=i({},t);try{var a=r(n,o.url);if(hr(a)){var u=a.code,c=a.isErr,s=a.msg;o=i({},o,{errorMessage:Cr(s||"",Ei),bizCode:u||"",isBizError:c?nn:tn})}else o=i({},o,{isBizError:a?tn:nn});o.isBizError===nn&&(Wn(),o=i({},o,{recordId:Un}))}catch(e){}return o}function Di(e,t,n){var r=(e||{}).resBodyHandler;if(!yr(r))return t;var o=i({},t);try{var a=r(n,o.url);a&&(o=i({},o,{response:a}))}catch(e){}return o}var Mi,ji=function(e){if(!window.XMLHttpRequest)return te;var t=new F((function(t){var n=$r(e),r=e||{},o=r.traceIdApiList,a=void 0===o?[]:o,u=r.responseValidator,c=r.submitType,s=r.ignoreApiSuccessList,l=r.resBodyHandler,f=function(r,o,a){void 0===r&&(r="");var f=a&&a.currentTarget||{},p=f.xhrConfig||{},d=f.status,v=p.openURL||"",h=p.startTime||"",m=p.reqHeader||{},g=p.method||"",y=(m||{}).TraceId;if(!(v.indexOf(n)>-1)){var b=Dr(v);if(!zr(c)||!kr(["/reqxml","/reqlocal","/reqsofttodo","/reqsavemap","/reqsavefile","/reqreadfile","/reqreadmap"],b)){var w=i({url:v,api:b,status:d,params:Oi(r),response:"",traceId:y||"",duration:null,errorMessage:"",method:null==g?void 0:g.toLowerCase(),reqHeader:m,resHeader:Ai(f),bizCode:"",isBizError:tn,recordId:""},p.commonData||{});h&&(w=i({},w,{duration:Date.now()-h}));var x=function(n,r){var o=Li(e,r,r.params);t.next({type:ct.XHR,subtype:n,timestamp:h,content:Si(o)})},_=function(){kr(s,b)||x(o,w)};d>=200&&d<400&&(yr(u)||yr(l)?Pi(f,(function(t){try{var n=t;try{"string"==typeof t&&(n=JSON.parse(t))}catch(e){}w=Ci(e,w,n),w=Di(e,w,n),_()}catch(e){_()}})):_()),(d<=0||d>=400)&&Pi(f,(function(e){Wn(),x(o,i({},w,{recordId:Un,errorMessage:e}))}))}}};ur(window.XMLHttpRequest.prototype,"open",(function(t,r){var o=this,i=Nr(e);if(this.xhrConfig={openURL:r,startTime:Date.now(),reqHeader:{},method:t,commonData:i},!(r.indexOf(n)>-1)&&nr(a,r)){var u=tr();Object.keys(u).forEach((function(e){o.setRequestHeader(e,u[e])}))}})),ur(window.XMLHttpRequest.prototype,"setRequestHeader",(function(e,t){this.xhrConfig&&this.xhrConfig.reqHeader&&(this.xhrConfig.reqHeader[e]=t)})),ur(window.XMLHttpRequest.prototype,"send",(function(){var e=(arguments.length<=0?void 0:arguments[0])||"";this.addEventListener?(this.addEventListener("error",f.bind(null,e,Kt)),this.addEventListener("load",f.bind(null,e,$t))):ur(this,"onreadystatechange",(function(t){4===this.readyState&&f(e,$t,t)}))}))}));return t},Bi=null,Hi=null;(Mi=window.fetch)&&(window.fetch=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!Hi||!Bi)return Mi.apply(this,t).then((function(e){return e})).catch((function(e){throw e}));Ti(t,Hi);var r=Nr(Hi),o=Ii(t),a=o.url,u=o.api,c=o.method,s=o.reqHeader,l=o.traceId,f=o.cloneRequest,p=Date.now(),d=Hi,v=d.resBodyHandler,h=d.responseValidator,m=d.ignoreApiSuccessList,g=void 0===m?[]:m,y=d.contentTypes,b=void 0===y?[]:y,w=function(e,n){ki(t,f,(function(t){var r=t.params,o=i({},n,{params:r||""}),a=Li(Hi,o,r);Bi.next({type:ct.FETCH,subtype:e,timestamp:p,content:Si(a)})}))};return Mi.apply(this,t).then((function(e){var t=i({url:a,api:u,status:e.status,params:"",response:"",traceId:l,duration:Date.now()-p,errorMessage:"",method:c,reqHeader:s,resHeader:Ri(e.headers),bizCode:"",isBizError:tn,recordId:""},r);if(!Ni(e,b))return e.ok||(Wn(),w(Jt,i({},t,{recordId:Un,errorMessage:e.status}))),e;var n,o=e.clone(),f=e.clone(),d=f.status,m=function(){kr(g,u)||w(Jt,t)};return d>=200&&d<400&&function(){try{var e="xlog catch json";if(yr(h)||yr(v)){var n=o.json().catch((function(){return e}));if(!br(n))return void m();n.then((function(n){n!==e?(t=Ci(Hi,t,n),t=Di(Hi,t,n),m()):m()})).catch((function(){m()}))}else m()}catch(e){m()}}(),(d<=0||d>=400)&&(n=function(e){Wn(),w(Jt,i({},t,{recordId:Un,errorMessage:e}))},f.text().then((function(e){n(e)})).catch((function(e){var t=e&&e.message||"";n(t)}))),e})).catch((function(e){throw Wn(),w(Vt,i({url:a,api:u,recordId:Un,status:"",params:"",response:"",traceId:l,duration:Date.now()-p,errorMessage:null==e?void 0:e.message,method:c,reqHeader:s,resHeader:"",bizCode:"",isBizError:tn},r)),e}))});var Fi=function(e){if(e){for(var t=[],n=[e],r=n.shift();r;){t.push(r);var o=r.children.length;if(o)for(var i=o-1;i>=0;i--)n.unshift(r.children[i]);r=n.shift()}return t}};var Ui={"accent-color":"auto","align-content":"normal","align-items":"normal","align-self":"auto",animation:"none 0s ease 0s 1 normal none running",appearance:"none",background:"rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box","background-image":"none","border-top":void 0,"border-right":void 0,"border-bottom":void 0,"border-left":void 0,"border-radius":"0px",bottom:"auto","box-shadow":"none","box-sizing":"border-box","caption-side":"top",clear:"none",clip:"auto","clip-path":"none",color:void 0,content:"normal",direction:"ltr",display:"block",fill:"rgb(0, 0, 0)","fill-opacity":"1",flex:"0 1 auto","flex-basis":"auto","flex-direction":"row","flex-flow":"row nowrap","flex-grow":"0","flex-shrink":"1","flex-wrap":"nowrap",float:"none","flood-opacity":"1","font-size":void 0,"font-style":"normal","font-weight":void 0,"font-stretch":"100%","grid-template":"none / none / none",gap:"normal",grid:"none / none / none / row / auto / auto","grid-area":"auto / auto / auto / auto","grid-template-rows":"none","grid-template-columns":"none","grid-template-areas":"none","grid-column-gap":"normal","grid-row-gap":"normal","grid-column-start":"auto","grid-column-end":"auto","grid-row-start":"auto","grid-row-end":"auto",height:void 0,"inline-size":void 0,"justify-content":"normal","justify-items":"normal","justify-self":"auto",left:"auto","letter-spacing":"normal","line-height":"normal",margin:void 0,"max-height":"none","max-inline-size":"none","max-width":"none","min-height":"0px","min-inline-size":"0px","min-width":"0px","object-fit":"fill","object-position":"50% 50%",opacity:"1",order:"0",outline:"rgb(0, 0, 0) none 0px",overflow:"visible",padding:void 0,perspective:"none","perspective-origin":void 0,"place-content":"normal","place-items":"normal","place-self":"auto",position:"static",right:"auto",rotate:"none",scale:"none","shape-margin":"0px","shape-outside":"none",stroke:"none","stroke-width":"1px","stroke-opacity":"1","tab-size":"8","text-align":"start","text-indent":"0px","text-overflow":"clip","text-shadow":"none","text-size-adjust":"auto",top:"auto",transform:"none","transform-origin":void 0,"transform-style":"flat",translate:"none",visibility:"visible","white-space":"normal",width:void 0,"will-change":"auto","word-break":"normal","writing-mode":"horizontal-tb","z-index":"auto",zoom:"1","-webkit-tap-highlight-color":"rgba(0, 0, 0, 0)"},qi=Object.keys(Ui),zi=qi.length-1;function Wi(e){var t="";try{var n=document.documentElement,r=n.cloneNode(!0);t=function(e,t){var n,r=Fi(e),o=((n=Fi(t)).forEach((function(e){"SCRIPT"===e.tagName&&(e.src=""),"LINK"===e.tagName&&(e.href="")})),n);return r.forEach((function(e,t){var n=o[t];if(!["SCRIPT","HEAD","STYLE","META","LINK","TITLE"].includes(e.tagName)){var r=getComputedStyle(e);qi.forEach((function(t,o){var i=r.getPropertyValue(t);if("background"!==t&&"background-image"!==t||i.indexOf("base64")>-1&&(i=""),Ui[t]!==i&&n.style.setProperty(t,i),o===zi&&"IMG"===e.tagName){var a=e.src;a.indexOf("base64")>-1?a="":0===a.indexOf("/")&&(a=""+window.location.origin+a),n.setAttribute("src",a)}}))}})),(new XMLSerializer).serializeToString(t)}(n,r),t=Lr(Fn(t),800)}catch(t){!function(e,t){var n=(t||{}).foo;if(n){var r=Nr(t);n({id:"develop_custom",page_id:"error",page_title:"",btn_id:St,btn_title:{content:i({errorMessage:Lr(e.message),errorStack:Lr(e.stack)},r),timestamp:Date.now()}})}}(t,e)}return setTimeout((function(){Wn()}),1e4),t}var Gi=["META","TITLE","SCRIPT","STYLE","LINK","TEMPLATE","NOSCRIPT"];function Ji(){var e=document.body.querySelectorAll("*"),t=[];return e.forEach((function(e){var n=e.tagName.toUpperCase();Gi.includes(n)||t.push(e)})),t}function Vi(e){if(!e)return!1;var t=e.display,n=e.visibility;return"none"!==t&&"hidden"!==n}function $i(e){if(!e)return!1;var t=e.opacity,n=parseFloat(t),r=e.backgroundColor;return!isNaN(n)&&n>=1&&!function(e,t){if(!e)return!1;var n=/^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*(\d*\.?\d+)\s*\)$/i.exec(e),r=null!=t?t:1;return!!n&&parseFloat(n[1])<r}(r,1)}function Ki(e,t){if(!(e&&e instanceof Element))return!1;if(!(t&&t instanceof Set&&t.size))return!1;for(;e;){if(t.has(e))return!0;e=e.parentElement||e.parentNode}return!1}function Xi(e){var t=e.id,n=e.className,r=e.nodeName;if(t)return"#"+t;if(!n||["HTML","BODY","MAIN"].includes(r))return r.toLowerCase();try{return"."+n.split(" ").filter((function(e){return!!e})).join(".")}catch(e){return""}}function Yi(e){return vr(e)?e:[e]}function Zi(e,t){return e.width*e.height-t.width*t.height}function Qi(e,t){return e.left>=t.left&&e.right<=t.right&&e.top>=t.top&&e.bottom<=t.bottom}function ea(e){if(!e)return!0;var t="";try{t=e.toDataURL()}catch(e){return!1}var n=function(e,t){if(!e||!t)return"";var n=document.createElement("canvas");return n.width=e,n.height=t,n.toDataURL()}(e.width,e.height);return t===n}function ta(e){if(!e||!e.contentWindow)return!0;try{return""===(e.contentDocument||e.contentWindow.document).body.innerHTML.trim()}catch(e){return!0}}var na=["canvas","svg","img","video","audio","iframe","embed"],ra=["rect","circle","ellipse","line","polygon","polyline","path","text"];function oa(e){return!(!e||!e.length)&&(-1!==e.toLowerCase().indexOf("loading")||-1!==e.indexOf("加载"))}function ia(){var e=Ji(),t=[];return e.forEach((function(e){(function(e){var t,n,r;if(!e)return!1;var o=!e.childElementCount&&oa(e.outerHTML)||oa(Xi(e)),i=null!=(t=null==(n=e.innerText)||null==(r=n.trim())?void 0:r.length)?t:0;return o&&i<=15})(e)&&t.push(e)})),t}function aa(e,t){var n,r;if(!e||null==t||!t.length)return!1;if(t.includes(e))return!0;if(!oa(e.outerHTML))return!1;for(var o=null!=(n=null==(r=e.innerText)?void 0:r.trim())?n:"",i=0;i<t.length;i++){var a,u,c=t[i];if((null!=(a=null==c||null==(u=c.innerText)?void 0:u.trim())?a:"")===o)return!0}return!1}var ua="MOUNTED";function ca(){var e,t,n;return!(null==(e=window)||null==(t=e.bigbox)||null==(n=t.apps)||!n.length)}function sa(){return la.apply(this,arguments)}function la(){return(la=o(n().mark((function e(){var t,r,o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=(t=window)&&null!=(r=t.bigbox)&&r.apps){e.next=2;break}return e.abrupt("return",[]);case 2:if(null!=(o=window.bigbox.apps.filter((function(e){return e.status===ua&&"layout"!==e.name})))&&o.length){e.next=5;break}return e.abrupt("return",[]);case 5:return e.abrupt("return",fa(o));case 6:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function fa(e){return pa.apply(this,arguments)}function pa(){return pa=o(n().mark((function e(t){var r,o,i,a,u;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=[],o=0;case 2:if(!(o<t.length)){e.next=14;break}if(a=null==(i=t[o].customProps)||null==i.domElementGetter?void 0:i.domElementGetter()){e.next=6;break}return e.abrupt("continue",11);case 6:if(!da(a)){e.next=10;break}return e.next=9,va(a);case 9:a=e.sent;case 10:a instanceof HTMLElement?a&&(u=Xi(a),r.push(u)):"string"==typeof a&&r.push(a);case 11:o++,e.next=2;break;case 14:return e.abrupt("return",r);case 15:case"end":return e.stop()}}),e)}))),pa.apply(this,arguments)}function da(e){return e&&"function"==typeof e.then}function va(e){return Promise.resolve(e).then((function(e){if("string"==typeof e||"object"==typeof(t=e)&&1===t.nodeType)return e;var t;console.log("Selector must be a string or HTMLElement.")})).catch((function(e){console.error(e)}))}function ha(e){return ma.apply(this,arguments)}function ma(){return(ma=o(n().mark((function e(t){var r,o,i,a;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=(r=window)&&null!=(o=r.bigbox)&&o.apps&&t){e.next=2;break}return e.abrupt("return",[]);case 2:if(i=t.appName,null==(a=window.bigbox.apps.filter((function(e){return e.name===i})))||!a.length){e.next=6;break}return e.abrupt("return",fa(a));case 6:return e.abrupt("return",[]);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ga(e){if("string"!=typeof e||!e.trim())return console.error("Invalid input: The error message should be a non-empty string."),null;var t=e.match(/(application|parcel) '([^']+)'\s+died in status (\w+): (.*)/i);return t?{type:t[1],appName:t[2],status:t[3],errorDetails:t[4].trim()}:null}var ya;function ba(){var e;return!(null==(e=window)||!e.__POWERED_BY_QIANKUN__)}function wa(){return function(e){for(var t=document.body.querySelectorAll("[id]"),n=[],r=0;r<(null==t?void 0:t.length);r++){var o=Xi(t[r]);e&&e.length?0===o.indexOf(e)&&n.push(o):n.push(o)}return n}("#__qiankun_subapp_wrapper_for_")}var xa=!1,_a="",Ea=function(e,t){void 0===t&&(t=!1),_a=e,xa=t},Sa=function(){xa=!0,_a=""},Oa=function(e){return _a===Rr(e).pagePath},Ta=["#root","#app","main","body","html"],Ia="CONTENT_EMPTY",ka="CONTENT_COLLAPSE",Na=0,Ra=2,Aa=4,Pa=6,La=8,Ca=12,Da=((ya={})[Ra]="NO_ELEMENT_FOUND",ya[Aa]="WRAPPER",ya[Pa]="EMPTY_NODE",ya[La]="NO_MAIN_DOC_NODE",ya[Ca]="LOADING_STATE",ya);function Ma(e){if(!e||null==e||!e.length)return!1;for(var t=e.length,n=0;n<t;n++){var r=e[n];try{var o=document.querySelector(r);if(o&&!o.firstElementChild)return!0}catch(e){console.error("检测元素的内容是否为空报错："+e)}}return!1}function ja(e){if(!e||null==e||!e.length)return!1;for(var t=!1,n=e.length,r=0;r<n;r++){var o=e[r];try{var i=document.querySelector(o);if(i&&!i.offsetHeight*i.offsetWidth){t=!0;break}}catch(e){console.error("检测元素的高度坍报错："+e)}}return t}function Ba(e,t){if(!(document.elementsFromPoint||document.msElementsFromPoint))return Na;for(var n=t,r=[],o=n.length,i=0;i<o;i++){var a=n[i],u=document.querySelector(a);if(u){var c=u.getBoundingClientRect();r.push(c)}}var s=function(e){if(null==e||!e.length)return[];e.sort(Zi);for(var t=e.filter((function(e){return e.width*e.height})),n=new Set,r=0;r<t.length;r++)if(!n.has(r))for(var o=t[r],i=r+1;i<t.length;i++)n.has(i)||Qi(o,t[i])&&n.add(i);var a=t.filter((function(e,t){return!n.has(t)}));return a}(r);null!=s&&s.length||s.push({x:0,y:0,width:window.innerWidth,height:window.innerHeight});var l,f,p=ia(),d=(l=Ji(),f=[],l.forEach((function(e){var t=window.getComputedStyle(e);if(["absolute","fixed","sticky"].includes(t.position)&&Vi(t)&&e.offsetHeight>0&&e.offsetWidth>0){var n=parseInt(t.top,10),r=parseInt(t.height,10);if(n<=1&&n>=0&&r<window.innerHeight/3){var o=Xi(e),i=["header","title","nav","top","head"];(null==i?void 0:i.some((function(e){return-1!==o.toLowerCase().indexOf(e)})))&&f.push(e)}}})),f),v=function(e,t){var n=Ji(),r=[],o=[];return n.forEach((function(n){var i=window.getComputedStyle(n),a=.8*(t?t.height:window.innerHeight),u=.8*(t?t.width:window.innerWidth);if(("absolute"===i.position||"fixed"===i.position)&&i.zIndex>0&&Vi(i)&&n.offsetHeight>=a&&n.offsetWidth>=u){var c=Xi(n);aa(n,e)&&o.push(c),$i(i)?n.innerText&&0!==n.innerText.trim().length||o.push(c):(!n.innerText||n.innerText.trim().length<=15)&&r.push(c)}})),{loadingMaskSelectors:o,transparentMaskSelectors:r}}(p,s[0]),h=v.loadingMaskSelectors,m=v.transparentMaskSelectors;function g(t){if(null==t||!t.length)return Ra;var n=function(e,t){if(null==e||!e.length)return[];if(null==t||!t.length)return e;for(var n=[].concat(e),r=new Set,o=0;o<t.length;o++)for(var i=document.querySelectorAll(t[o]),a=0;a<i.length;a++)r.add(i[a]);for(var u=0;u<n.length&&Ki(n[u],r);u++)n.splice(u,1),u--;return n}(t,m);return null!=n&&n.length?function(e,t){if(null==t||!t.length)return!1;try{var n=Xi(e);return-1!==t.indexOf(n)}catch(e){console.error("检测像素点下的元素报错："+e)}}(n[0],e)?Aa:aa(n[0],p)?Ca:function(e){var t,n,r;if(!e)return!0;if(null!=(t=e.innerText)&&t.trim().length)return!1;var o=null!=(n=null==(r=e.tagName)?void 0:r.toLowerCase())?n:"";if(na.includes(o))if("canvas"===o){if(!ea(e))return!1}else{if("iframe"!==o)return!1;if(!ta(e))return!1}if(ra.includes(o)){if("text"!==o)return!1;if(0!==e.innerHTML.trim().length)return!1}for(var i=0;i<na.length;i++){var a=na[i],u=e.getElementsByTagName(a.toLowerCase());if("canvas"===a){var c=Array.from(u),s=null==c?void 0:c.filter((function(e){return!ea(e)}));if(null!=s&&s.length)return!1}else if("iframe"===a){var l=Array.from(u).filter((function(e){return!ta(e)}));if(null!=l&&l.length)return!1}else if(null!=u&&u.length)return!1}var f=window.getComputedStyle(e);return null==f||!f.backgroundImage||"none"===(null==f?void 0:f.backgroundImage)}(n[0])?Pa:function(e,t,n){if(!e||!(null!=t&&t.length||null!=n&&n.length))return!1;for(var r=new Set,o=0;o<t.length;o++)for(var i=document.querySelectorAll(t[o]),a=0;a<i.length;a++)r.add(i[a]);for(var u=0;u<n.length;u++)n[u]&&r.add(n[u]);return Ki(e,r)}(n[0],h,d)?La:Na:Ra}function y(e,t,n,r){for(var o=[],i=[],a=e||{},u=a.x,c=a.y,s=a.width,l=a.height,f=Na,p=t;p<=n;p++){document.elementsFromPoint?(o=document.elementsFromPoint(u+s*p/r,c+l/2),i=document.elementsFromPoint(u+s/2,c+l*p/r)):document.msElementsFromPoint&&(o=document.msElementsFromPoint(u+s*p/r,c+l/2),i=document.msElementsFromPoint(u+s/2,c+l*p/r));var d=g(o);if(d===Na)return Na;f=Math.max(f,d);var v=g(i);if(v===Na)return Na;f=Math.max(f,v)}return f}for(i=0;i<s.length;i++){var b=y(c=s[i],1,9,10);if(b!==Na)return b}return Na}function Ha(e){return Fa.apply(this,arguments)}function Fa(){return Fa=o(n().mark((function e(t){var r,o,a,u,c,s,l,f,p,d,v,h,m,g,y,b,w,x;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=!1,o="",u=(a=t||{}).errorMessage,c=void 0===u?"":u,s=a.blankScreenSelector,l=void 0===s?[]:s,f=a.foo,e.prev=3,p=Yi(l),d=[],!ca()){e.next=17;break}if(!(v=ga(c))){e.next=14;break}return e.next=11,ha(v);case 11:d=e.sent,e.next=17;break;case 14:return e.next=16,sa();case 16:d=e.sent;case 17:h=ba()?wa():[],m=Ta,g=p.concat(d).concat(h).concat(m).filter((function(e,t,n){return n.indexOf(e)===t})),y=p.concat(d).concat(h).filter((function(e,t,n){return n.indexOf(e)===t})),Ma(g)?(r=!0,o=Ia):ja(y)?(r=!0,o=ka):(b=Ba(g,y))!==Na&&(r=!0,o=Da[b]),e.next=27;break;case 24:e.prev=24,e.t0=e.catch(3),f&&(Wn(),w=Nr(t),x={id:"develop_custom",page_id:"error",page_title:"",btn_id:Et,btn_title:{content:i({recordId:Un,errorMessage:Lr(e.t0.message),errorStack:Lr(e.t0.stack)},w),timestamp:Date.now()}},f(x));case 27:return e.abrupt("return",{isBlankScreen:r,blankScreenType:o});case 28:case"end":return e.stop()}}),e,null,[[3,24]])}))),Fa.apply(this,arguments)}function Ua(e,t){if(t&&"function"==typeof t){var r=rr(e);if(!(r<1))var i=or(e),a=0,u={},c=setInterval(o(n().mark((function o(){var i,s,l,f;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Ha(e);case 2:if(n.t0=n.sent,n.t0){n.next=5;break}n.t0={};case 5:i=n.t0,s=i.isBlankScreen,l=i.blankScreenType,f=!Oa(e),s&&!f||(t({isBlankScreen:!1,blankScreenType:""}),clearInterval(c)),u.blankScreenType||(u.blankScreenType=l),++a===r&&(u.isBlankScreen=!0,t(u),clearInterval(c));case 13:case"end":return n.stop()}}),o)}))),i)}}function qa(e){var t=e.subtype,n=e.blankScreenType,r=e.capture,o=e.opts,a=o.errorMessage,u=o.errorStack,c=void 0===u?"":u,s=o.timestamp,l=Nr(o);return Wn(),{type:ct.BLANK_SCREEN,subtype:t,timestamp:s,content:i({blankScreenType:n,recordId:Un,message:a,errorStack:c,capture:r},l)}}function za(e,t){var r=rr(e),i=or(e),a=function(e,t){return Ce(t).pipe(We(e))}(r,i);return[De((function(){var t=xa;if(t){var n=Rr(e).pagePath;Ea(n)}return t})),Je((function(){return a.pipe(ze(o(n().mark((function t(){var r,o,i,a;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Ha(e);case 2:if(t.t0=t.sent,t.t0){t.next=5;break}t.t0={};case 5:if(r=t.t0,o=r.isBlankScreen,i=r.blankScreenType,a=!Oa(e),o&&!a){t.next=12;break}return Sa(),t.abrupt("return",te);case 12:return t.abrupt("return",{isBlankScreen:o,blankScreenType:i});case 13:case"end":return t.stop()}}),t)})))),(t=function(e){return e!==te},void 0===i&&(i=!1),q((function(e,n){var r=0;e.subscribe(new z(n,(function(e){var o=t(e,r++);(o||i)&&n.next(e),!o&&n.complete()})))}))),je(r));var t,i})),De((function(e){return e.length>0})),_e((function(e){return function(e){for(var t=(e||[]).length,n=0;n<t;n++){var r=e[n];if(null==r||!r.isBlankScreen)return!1}return 0!==t}(e)})),_e(function(){var r=o(n().mark((function r(o){var i,a,u,c,s;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(o){n.next=3;break}return Sa(),n.abrupt("return",te);case 3:return i=Wi(e),n.next=6,Ha(e);case 6:if(n.t0=n.sent,n.t0){n.next=9;break}n.t0={};case 9:if(a=n.t0,u=a.isBlankScreen,c=a.blankScreenType,s=!Oa(e),u&&!s){n.next=18;break}return Sa(),n.abrupt("return",te);case 18:return n.abrupt("return",qa({subtype:t,blankScreenType:c,capture:i,opts:e}));case 19:case"end":return n.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}()),ze((function(e){return we(e)}))]}var Wa=function(e,t,n){var r=!1;return(Ur(n,t)||to(e,n))&&(r=!0),r},Ga=function(e){var t=function(e){var t=Ae(window,"error");return t.pipe.apply(t,[De((function(t){var n=Yr(t),r=n.limitErrorMessage,o=n.limitErrorStack,i=n.errorName,a={type:ct.WINODW_ERROR,content:Zr(r,o,e,i)};return!Wa(a,r,e)&&!!xa&&(e.errorMessage=r,e.errorStack=o,e.timestamp=Date.now(),!0)}))].concat(za(e,Ut)))}(e),n=function(e){var t=Ae(window,"error",!0);return t.pipe.apply(t,[De((function(t){if(t instanceof window.ErrorEvent)return!1;var n=t.target,r=n.tagName;if(!kr(["SCRIPT"],r))return!1;var o={type:ct.SOURCE_ERROR,content:Qr(n,e)};return!to(o,e)&&!!xa&&(e.errorMessage=n.src||n.href,e.errorStack="",e.timestamp=Date.now(),!0)}))].concat(za(e,qt)))}(e),r=function(e){var t=Ae(window,"unhandledrejection");return t.pipe.apply(t,[De((function(t){var n=Xr(t),r=n.limitErrorMessage,o=n.limitErrorStack,i={type:ct.UNHANDLED_REJECTION,content:Zr(r,o,e)};return!Wa(i,r,e)&&!!xa&&(e.errorMessage=r,e.errorStack=o,e.timestamp=Date.now(),!0)}))].concat(za(e,zt)))}(e);return we([t,n,r]).pipe(Te())},Ja=function(e){var t=[],n=Rr(e).pagePath,r=Date.now(),o=function(){var t=Rr(e).pagePath,o=window.location.origin||"",i=Date.now(),a={origin:o,previousTime:r,currentTime:i,previous:n,current:t,stay:i-r,timestamp:i};return n=t,r=i,a};["hashchange","popstate"].forEach((function(e){var n=Ae(window,e).pipe(_e((function(){return{type:ct.ROUTE_CHANGE,subtype:"routeChange."+e,content:o()}})));t.push(n)}));["pushState","replaceState"].forEach((function(e){var n=new F((function(t){ur(window.history,e,(function(){t.next({type:ct.ROUTE_CHANGE,subtype:"routeChange."+e,content:o()})}))}));t.push(n)}));var a="XLOG_UNLOAD_DATA_KEY";Ae(window,"beforeunload").subscribe((function(){var t=Nr(e);window.sessionStorage.setItem(a,JSON.stringify(i({},o(),{commonData:t})))}));var u=window.sessionStorage.getItem(a);if(u){var c=new F((function(e){var t;try{t=JSON.parse(u),e.next({type:ct.ROUTE_CHANGE,subtype:st.PAGE_LOAD,content:t})}catch(e){}}));window.sessionStorage.setItem(a,""),t.push(c)}var s={type:ct.ROUTE_CHANGE,subtype:st.PAGE_INIT,content:i({},o(),{previous:""})};return t.push(xe(s)),we(t).pipe(Te(),De((function(e){var t=e.content;return[st.PAGE_LOAD,st.PAGE_INIT].indexOf(e.subtype)>-1||t.current!==t.previous})),_e((function(t){var n=t.content;if([st.PAGE_LOAD].indexOf(t.subtype)>-1)return t;var r=Qn(e);e.pv_id=r;var o=Nr(e);return Sa(),i({},t,{content:i({},n,{commonData:o})})})),Ve((function(t){if(e.handleRouteChange&&t.subtype!==st.PAGE_INIT){var n=t.content,r=n.stay,o=n.previousTime,i=n.currentTime,a=n.previous,u=n.current;e.handleRouteChange.call(null,{stay:r,lastPageLoadTime:o,currentPageLoadTime:i,lastPageUrl:a,currentPageUrl:u})}})))},Va=["content","subtype","timestamp","storageId"],$a=function(e,t){return e[t]},Ka=function(e){var t,n,r,o=e.id,i=e.page_id,a=e.page_title,u=e.btn_id,c=e.btn_title,s=(c||{}).timestamp||Date.now(),l=null==c||null==(t=c.content)?void 0:t.external,f={id:o,page_id:i,page_title:a,btn_id:u,btn_title:c,timestamp:s};l&&(f.external=l,null==(n=f.btn_title)||(null==(r=n.content)||delete r.external));return f},Xa=function(e){var t=e.content,n=e.storageId,r=t.loadtype,o=null==t?void 0:t.external,a=t.timestamp;o&&delete t.external;var u="disappear"===r?i({},t,{storageId:n}):{loadtype:"appear",pageid:$a(t,"current"),pre:$a(t,"previous"),content:i({},t.commonData||{}),storageId:n,timestamp:a},c={id:"develop_custom",page_id:"autotrack",page_title:"",btn_id:xt,btn_title:u,timestamp:a};return o&&(c.external=o),c},Ya=function(e){return Ka({id:"develop_custom",page_id:"system",page_title:"",btn_id:_t,btn_title:Ar(e,Va)})},Za=function(e){return Ka({id:"develop_custom",page_id:"console",page_title:"",btn_id:e.subtype,btn_title:Ar(e,Va)})},Qa=function(e){return Ka({id:"develop_custom",page_id:"network",page_title:"",btn_id:e.type,btn_title:Ar(e,Va)})},eu=function(e){var t=e.content,n=t.btn_title||{},r=null==t?void 0:t.external;return r&&(null!=n&&n.content||(n.content={}),n.content.external=r,null==t||delete t.external),Ka(i({},t,{btn_title:i({},n,{storageId:e.storageId})}))},tu=function(e){var t=e.content;return i({},t,{btn_title:i({},t.btn_title||{}),timestamp:(null==e?void 0:e.timestamp)||Date.now()})},nu=function(e){return Ka({id:"develop_custom",page_id:"performance",page_title:"",btn_id:e.subtype,btn_title:Ar(e,Va)})},ru=function(e){return Ka({id:"develop_custom",page_id:"error",page_title:"",btn_id:vt,btn_title:Ar(e,Va)})},ou=function(e){return Ka({id:"develop_custom",page_id:"error",page_title:"",btn_id:ht,btn_title:Ar(e,Va)})},iu=function(e){return Ka({id:"develop_custom",page_id:"userEvent",page_title:"",btn_id:e.subtype,btn_title:Ar(e,Va)})},au=function(e){return Ka({id:"develop_custom",page_id:ct.BLANK_SCREEN,page_title:"",btn_id:e.subtype,btn_title:Ar(e,Va)})},uu=function(e){return Ka({id:"develop_custom",page_id:"error",page_title:"",btn_id:mt,btn_title:Ar(e,Va)})},cu=function(e){return Ka({id:"develop_custom",page_id:"network",page_title:"",btn_id:e.type,btn_title:Ar(e,Va)})},su=function(e){return Ka({id:"develop_custom",page_id:"error",page_title:"",btn_id:Et,btn_title:Ar(e,Va)})},lu=function(e,t,n){if(!e.length)return"";var r=e[0],o=r.traceId,a=r.uuid,u=r.from,c=r.type,s=r.event,l=r.content,f=void 0===l?{}:l,p=r.userId,d=r.distinctId,v=t.product_id,h=t.product_name,m=t.channel_env,g=[],y={},b=f.__channel;if(b&&(y=_r(y,Ar(b,["from","product_id","product_name"])),delete f.__channel),c===dt)g=s;else{var w=function(e){var t=function(e){var t={loadtype:"disappear",stay:e.stay,pageid:$a(e,"previous"),timestamp:e.timestamp,content:i({},e.commonData||{})};return null!=e&&e.external&&(t.external=e.external),t},n=[];return e.forEach((function(e){if(n.push(e),e.type===ct.ROUTE_CHANGE){var r=e.content;e.subtype===st.PAGE_LOAD?e.content=t(r):e.subtype!==st.PAGE_INIT&&n.push(i({},e,{content:t(r)}))}})),n}(e),x=function(e){return e.reduce((function(e,t){return vr(t.content)&&t.type===ct.PERFORMANCE?t.content.forEach((function(n){e.push(i({},t,n))})):e.push(t),e}),[])}(w);g=function(e){var t,n=((t={})[ct.ROUTE_CHANGE]=Xa,t[ct.BASE_INFO]=Ya,t[ct.CONSOLE]=Za,t[ct.FETCH]=Qa,t[ct.DEVELOP_CUSTOM]=eu,t[ct.BUSINESS_POINT]=tu,t[ct.PERFORMANCE]=nu,t[ct.SOURCE_ERROR]=ru,t[ct.UNHANDLED_REJECTION]=ou,t[ct.USER_EVENT]=iu,t[ct.WINODW_ERROR]=uu,t[ct.XHR]=cu,t[ct.OBSERVABLE_ERROR]=su,t[ct.BLANK_SCREEN]=au,t),r=e.filter((function(e){return n[e.type]})).map((function(e){return(0,n[e.type])(e)}));return r}(x)}return g.length?i({from:u,uuid:a,userId:p,traceId:o,id:"login",track_alias:"xlog",event:g,distinctId:d,basic:Zn(n),product_id:v,product_name:h,channel_env:m},y):""},fu=!1;var pu,du,vu,hu=function(e,t,n){var r=Date.now(),o=Nr(e);return Wn(),{id:"develop_custom",page_id:"error",page_title:"before_report_hook_error",btn_id:mt,btn_title:{content:i({recordId:Un,errorMessage:"hook function beforeReport execute error："+Lr(t.message),errorName:"",errorStack:Lr(n)},o),timestamp:r},timestamp:r}},mu=function(e){return n=(t=e||{}).id,r=t.page_id,o=t.btn_id,Jr(n)&&"error"===r&&o===vt||function(e){var t=e||{},n=t.id,r=t.page_id,o=t.btn_id,i=[ht,mt,gt];return Jr(n)&&"error"===r&&kr(i,o)}(e)||function(e){var t=e||{},n=t.id,r=t.page_id;return Jr(n)&&"network"===r}(e)||function(e){var t=e.id,n=e.page_id;return Jr(t)&&n===ct.PERFORMANCE}(e)||function(e){var t=e.id,n=e.page_id;return Jr(t)&&n===ct.BLANK_SCREEN}(e)||function(e){var t=e.id,n=e.page_id;return Jr(t)&&n===ct.CONSOLE}(e)||function(e){var t=e||{},n=t.id,r=t.page_id;return Jr(n)&&"customReport"===r}(e);var t,n,r,o},gu=function(e,t){var n={eventItem:null,errorReportItem:null},r=mu(e)?function(e,t){var n={eventItem:null,errorReportItem:null},r=i({},e||{}),o=(t||{}).beforeReport;if(!eo(o))return n.eventItem=r,n;try{var a=r.btn_title,u=r.btn_id,c={type:u,content:(null==a?void 0:a.content)||{}};[yt,bt,wt].includes(u)&&(c.subtype=null==a?void 0:a.subtype);var s=o(c);if(s){var l=(s||{}).content,f=void 0===l?{}:l;null!=r&&r.btn_title||(r.btn_title={}),r.btn_title.content=f,n.eventItem=r}else{var p=ar(r);p&&Er.remove([p])}}catch(e){var d=hu(t,e,r);n.errorReportItem=d,n.eventItem=r}return n}(e,t):function(e,t){var n={eventItem:null,errorReportItem:null},r=i({},e),o=(t||{}).beforeReport;if(!eo(o))return n.eventItem=r,n;try{var a=o(r);if(a)n.eventItem=a;else{var u=ar(r);u&&Er.remove([u])}}catch(e){var c=hu(t,e,r);n.errorReportItem=c,n.eventItem=r}return n}(e,t),o=r.eventItem,a=r.errorReportItem;return n.eventItem=o,n.errorReportItem=a,n},yu=function(e,t,n){if(null!=n&&n.event){var r=(e||{}).submitType;n.event=function(e,t){var n=t.event,r=(e||{}).beforeReport;if(!eo(r))return n;for(var o=(null==n?void 0:n.length)||0,i=[],a=0;a<o;a++){var u=n[a];if(Jr(null==u?void 0:u.id)){var c=gu(u,e),s=c.eventItem,l=c.errorReportItem;l&&i.push(l),s&&i.push(s)}else i.push(u)}return i}(e,n),0!==n.event.length&&(zr(r)?e.commonLogsHandler&&e.commonLogsHandler.call(null,n):xu(t,n))}},bu=function(e,t){var n=null==e?void 0:e.submitType;if(!fu){var r=Vr(e),o=$r(e),a=r&&r.product_id&&r.product_name&&r.channel_env&&3===Object.keys(r).length;if(!(o&&a||"myTrack"!==n))return fu=!0,console.error("myTrackConfig请正确配置product_id，product_name，channel_env（或getEnv方法）");var u=t.filter(Hr()),c=function(e,t,n){var r=t.reduce((function(e,t){var n=t.uuid,r=e.idMap;return r[n]?r[n].push(t):(r[n]=[t],e.order.push(n)),e}),{order:[],idMap:{}});return r.order.map((function(e){return r.idMap[e]})).map((function(t){return lu(t,n,e)})).filter((function(e){return e}))}(e,u,r)||[],s=function(e,t){var n=e.bufferTimeArray;n[0],n[1];var r=n[2];return t.reduce((function(e,t){for(var n=null==t?void 0:t.event;n&&n.length>r;){var o=n.splice(0,r);e.push(i({},t,{event:o}))}return null!=n&&n.length&&e.push(i({},t,{event:n})),e}),[])}(e,c);s.forEach((function(t){yu(e,o,t)}))}},wu=null,xu=function(e,t,n){var r=(null==t?void 0:t.event)||[];if(r.length){var o=function(e){return e.map((function(e){return ar(e)})).filter((function(e){return e}))}(r),a=!1,u=i({},t||{},{event:r.map((function(e){var t=i({},e.btn_title||{});return t.useSendBeacon&&(a=!0,delete t.useSendBeacon),delete t.storageId,i({},e,{btn_title:t})}))}),c=JSON.stringify(u);if(c.length>1048576)return Er.remove(o),console.error("上传数据过大，请检查埋点数据是否上传了base64或其他导致req过大的数据");if(a){var s=new FormData;return s.append("events",c),void navigator.sendBeacon(e,s)}wu.next({url:e,events:c,keys:o,retry:n,data:t})}},_u=function(e,t){var n=null==e?void 0:e.submitType;(Wr(n)||zr(n))&&bu(e,t)},Eu=function(e,t){var n=e||{},r=n.id,o=n.page_id,i=n.page_title,a=n.btn_id;console.warn("请注意：检测到业务埋点事件id为"+r+"\n    （其中page_id, page_title, btn_id分别为"+o+", "+i+", "+a+"）不符合元事件规则，请尽快修改。\n    元事件列表："+t)},Su=function e(t,n,r){for(var o in void 0===r&&(r=[]),t)if(t.hasOwnProperty(o)){var i=[].concat(r,[o]);if(o===n)return{path:i,value:t[o]};if(hr(t[o])){var a=e(t[o],n,i);if(a.path)return a}}return{}},Ou=(pu={},function(e,t){var n=e.type+t,r=pu[n];if(pu.hasOwnProperty(n))return function(e,t){if(!t)return null;for(var n=e,r=0;r<t.length;r++)n=n[t[r]]||"";return n}(e,r);var o=Su(e,t);return pu[n]=(null==o?void 0:o.path)||null,(null==o?void 0:o.value)||null}),Tu=function(e,t){return((null==e?void 0:e.customConfig)||{})[t]},Iu=(du=null,vu=!1,function(e,t){if(!vu)try{var n=Tu(t,"constructionIgnore");vu=!0,du=n?JSON.parse(n):{}}catch(e){return console.error("server config constructionIgnore parse error",e),du={},!0}return!(du&&du.ignoreConfigList||[]).filter((function(t){if(t.type.indexOf(e.type)>-1){var n=Ou(e,t.property);return Mr(n,t.keywords)}return!1})).length}),ku=function(e){return{type:"serverConfig",content:e}},Nu=function(t){var n={},r=Ce(t.fetchConfigTimeout||5e3).pipe(We(1),_e((function(){return ku(n)}))),o=new F((function(e){t.getProductConfigAsync&&t.getProductConfigAsync((function(t){return e.next(ku(t))}))})),a=new F((function(r){var o=Vr(t),a=o.channel_env,u=function(e,t){var n=e.fetchProxyConfig||{};return i({},Ft,n)[t]}(t,a),c=o.product_id,s=function(t,r){var o={};try{var i=function(e){for(var t=e.config||[],n={},r=0;r<t.length;r++){var o=t[r]||{};for(var i in o)o.hasOwnProperty(i)&&(n[i]=o[i])}return n}(o=r?t:JSON.parse(t)),a=i.ignoreRules;o.customConfig=i,a&&"false"!==a&&(o=n)}catch(t){console.error("server config parse error",e),o=n}return o};return t.localProductConfig?r.next(ku(s(t.localProductConfig,!0))):u&&c?void lr({url:u,params:{productId:c},method:"POST",callback:function(e){var t=e&&e.msg||"",o={};try{o=s(t)}catch(e){o=n}r.next(ku(o))},error:function(){r.next(ku(n))}}):r.next(ku(n))})),u=(t.getProductConfigAsync?o:a).pipe(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length?q((function(t,n){Me(v([t],d(e)))(n)})):B}(r));return u},Ru=[kt,Rt,At,Pt,Nt,Lt,Ct,Dt,Mt,jt],Au=sr(),Pu=function(){var e=sr(),t=e,n="XLOG_DEVICE_ID";try{var r=window.localStorage.getItem(n);t=r||(cr()||e)}catch(e){}return window.localStorage.setItem(n,t),t}(),Lu=null,Cu=null,Du="",Mu="";t.isReady=!1,t.responseValidator=null;var ju=function(e){var t=e||{},n=t.subtype,r=t.apiLogType,o=t.eventType,a=t.url,u=t.api,c=t.status,s=void 0===c?"":c,l=t.method,f=t.bizCode,p=void 0===f?"":f,d=t.isBizError,v=void 0===d?"0":d,h=t.params,m=void 0===h?"":h,g=t.response,y=void 0===g?"":g,b=t.traceId,w=void 0===b?"":b,x=t.duration,_=t.errorMessage,E=void 0===_?"":_,S=t.reqHeader,O=void 0===S?"":S,T=t.resHeader,I=void 0===T?"":T,k=t.recordId,N=void 0===k?"":k;if(Lu)if(wr(o))console.error(r+"：eventType属性不能为空");else if(wr(a))console.error(r+"：url属性不能为空");else if(wr(u))console.error(r+"：api属性不能为空");else if(wr(l))console.error(r+"：method属性不能为空");else{var R={url:a,api:u,recordId:N,status:s,method:l,bizCode:p,isBizError:v,params:m,response:y,traceId:w,errorMessage:E,reqHeader:O,resHeader:I};wr(x)||(R.duration=x);var A=Nr(Cu),P={id:"develop_custom",page_id:"network",page_title:"",btn_id:o,btn_title:{subtype:n,content:i({},Si(R),A),timestamp:gr(e.timestamp)?e.timestamp:Date.now()}};Lu(P)}else console.error(r+"：请执行init方法对xlog进行初始化")},Bu=function(e,t,r){if(Lu){if(xa){var a=Rr(Cu).pagePath;Ea(a);var u=i({},Cu,{errorMessage:t});Ua(u,function(){var a=o(n().mark((function o(a){var c,s,l,f,p,d,v,h;return n().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!(c=a.isBlankScreen)&&Sa(),!c){n.next=14;break}return s=Wi(u),n.next=6,Ha(u);case 6:if(n.t0=n.sent,n.t0){n.next=9;break}n.t0={};case 9:l=n.t0,f=l.isBlankScreen,p=l.blankScreenType,d=!Oa(Cu),!f||d?Sa():(v=Nr(Cu),Wn(),h={id:"develop_custom",page_id:ct.BLANK_SCREEN,page_title:"",btn_id:Tt,btn_title:{subtype:Tt,content:i({blankScreenType:p,recordId:Un,message:t,errorStack:r,capture:s},v),timestamp:e}},Lu(h));case 14:case"end":return n.stop()}}),o)})));return function(e){return a.apply(this,arguments)}}())}}else console.error("handleComponentErrorBlankScreen：请执行init方法对xlog进行初始化")};function Hu(e,t,n){if(Lu){var r=e||{},o=r.name,a=r.message,u=r.ext,c=r.labels;if(o){var s=Nr(Cu),l={id:"develop_custom",page_id:"customReport",page_title:"",btn_id:n,btn_title:{subtype:o,content:i({message:Cr(a||""),ext:Cr(u||""),labels:Cr(c||"")},s),timestamp:Date.now()}};Lu(l)}else console.error(t+"：name属性不能为空")}else console.error(t+"：请执行init方法对xlog进行初始化")}var Fu=function(e){if(Lu){var t=e.channels||[];if(t.length){var n=function(e,t,n){var r,o,i=t.channelConfig||{},a={from:t.from,product_id:null==(r=t.myTrackConfig)?void 0:r.product_id,product_name:null==(o=t.myTrackConfig)?void 0:o.product_name},u=(n?[].concat(e,[a]):e).map((function(e){var t,n,r={};if(hr(e)?r=e:mr(e)&&(r=i[e]||{}),null!=(t=r)&&t.from&&null!=(n=r)&&n.product_id)return r})).filter((function(e){return e})).reduce((function(e,t){var n=t.from+t.product_id;return e[n]||(e[n]=t),e}),{});return jr(u)}(t,Cu,e.withBaseReport);n.forEach((function(t){Lu(i({},e,{timestamp:Date.now(),__channel:t}))}))}else Lu(i({},e,{timestamp:Date.now()}))}else console.error("logMultiple：请执行init方法对xlog进行初始化")};t.apiErrorLog=function(e){Wn(),ju(i({recordId:Un,subtype:Yt,apiLogType:Qt},e))},t.apiSuccessLog=function(e){ju(i({subtype:Xt,apiLogType:en},e))},t.blankScreenLog=function(e){if(Lu){Wn();var t=Date.now(),n=Nr(Cu),r=e||{},o=r.errorMessage,a=r.errorStack,u={id:"develop_custom",page_id:ct.BLANK_SCREEN,page_title:"",btn_id:Ot,btn_title:{subtype:Ot,content:i({blankScreenType:"",capture:"",recordId:Un,message:o?Lr(o):"",errorStack:a?Lr(a):""},n),timestamp:t}};Lu(u)}else console.error("blankScreenLog：请执行init方法对xlog进行初始化")},t.componentErrorLog=function(e){if(Lu){Wn();var t=e||{},n=t.errorMessage,r=t.errorStack,o=Lr(n)||"componentError",a=Lr(r)||"",u=Nr(Cu),c=Date.now(),s={id:"develop_custom",page_id:"error",page_title:"",btn_id:gt,btn_title:{content:i({recordId:Un,errorMessage:o,errorStack:a},u),timestamp:c}};Lu(s),Bu(c,o,a)}else console.error("componentErrorLog：请执行init方法对xlog进行初始化")},t.detectBlankScreen=function(e){if(Lu){var t=e||{},r=t.blankScreenInfo,a=t.detectionOptions,u=t.reportOptions,c=t.cb,s=r||{},l=s.message,f=s.labels,p=s.ext,d=(u||{}).reportResult,v=void 0===d||d,h={};l&&(h.message=Cr(l)),p&&(h.ext=Cr(p)),null!=f&&f.length&&(h.labels=Cr(f));var m=Date.now(),g=i({},Cu,a,{errorMessage:l}),y=Rr(Cu).pagePath;Ea(y,!0),Ua(g,function(){var e=o(n().mark((function e(t){var r,o,a,u,s,l,f,p,d;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.isBlankScreen,o=t.blankScreenType,r&&v){e.next=4;break}return c&&c({isBlankScreen:r,blankScreenType:o}),e.abrupt("return");case 4:return a=Wi(g),e.next=7,Ha(g);case 7:if(e.t0=e.sent,e.t0){e.next=10;break}e.t0={};case 10:u=e.t0,s=u.isBlankScreen,l=u.blankScreenType,c&&c({isBlankScreen:s,blankScreenType:l}),f=!Oa(Cu),s&&!f&&(p=Nr(Cu),Wn(),d={id:"develop_custom",page_id:ct.BLANK_SCREEN,page_title:"",btn_id:It,btn_title:{subtype:It,content:i({blankScreenType:l,recordId:Un,capture:a,errorStack:""},p,h),timestamp:m}},Lu(d));case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}else console.error("detectBlankScreen：请执行init方法对xlog进行初始化")},t.getDistinctId=function(){return Pu},t.getLimitTxt=Lr,t.getLimitTxtIgnoreType=Cr,t.getSessionId=function(){return Mu},t.getUserId=function(){return Du},t.init=function(e){if(Lu)return Lu;Cu=_r({},{types:[],externalInfo:0,uuid:Au,userId:"",shouldWindowErrorRepeatReport:!1,bufferTimeArray:[5e3,null,10],reportRandom:1,maxBufferSize:100,maxParallelReport:2,submitType:"myTrack",xReplay:void 0},e),t.responseValidator=Cu.responseValidator||null;var n=(Cu||{}).submitType,r=Wr(n),o=zr(n);if(!Cu.from)return console.error("xlog from字段必填，命名示例@bpc/h5kh");Cu.pv_id=Qn(Cu),t.isReady=!0,Sa(),Cu.uuid=Cu.uuid||Au,Cu.ignoreJsErrorList=Cu.ignoreJsErrorList?Fr(Cu.ignoreJsErrorList):[];var a=Rr(Cu).pagePath;Cu.initialPagePath=a,function(e){new F((function(e){wu=e})).pipe(Oe((function(e){var t=e.url,n=e.events,r=e.keys,o=e.retry,i=e.data;return new F((function(e){lr({url:t,method:"POST",params:{events:n},callback:function(n){200===(n||{}).httpStatus?Er.remove(r):o||setTimeout((function(){xu(t,i,!0)}),5e3),e.complete()},error:function(){e.complete()}})}))}),e.maxParallelReport)).subscribe((function(){}))}(Cu);var u=null,c=new F((function(e){u=e})),s=function(e){var t=Nr(Cu);return Wn(),xe(g({type:ct.OBSERVABLE_ERROR,timestamp:Date.now(),content:i({recordId:Un,errorMessage:Lr(e.message),errorStack:Lr(e.stack)},t)}))},l={userEvent:function(){return _i},performance:function(){return function(e){return we([Ae(window,"load").pipe(_e((function(){var t=window.performance&&window.performance.timing;if(!t)return te;var n=Nr(e),r=e.initialPagePath;return n.pagePath!==r?te:{type:ct.PERFORMANCE,subtype:"timing",timestamp:Date.now(),content:i({perfTiming:t},n)}}))),So(e),hi(e),mi(e),gi(e)]).pipe(Te())}(Cu)},sourceError:function(){return function(e){return Ae(window,"error",!0).pipe(_e((function(t){if(t instanceof window.ErrorEvent)return te;var n=t.target,r=n.tagName;return Wt.indexOf(r)>-1&&!n.getAttribute("src")?te:(Wn(),{type:ct.SOURCE_ERROR,content:Qr(n,e),timestamp:Date.now()})})))}(Cu)},routeChange:function(){return Ja(Cu)},unhandledrejection:function(){return function(e){return Ae(window,"unhandledrejection").pipe(_e((function(t){if(!t.reason)return te;var n=Xr(t),r=n.limitErrorMessage,o=n.limitErrorStack;return Ur(e,r)?te:(Wn(),{type:ct.UNHANDLED_REJECTION,content:Zr(r,o,e),timestamp:Date.now()})})))}(Cu)},xhr:function(){return ji(Cu)},fetch:function(){return function(e){return new F((function(t){Hi=e,Bi=t}))}(Cu)},consoleMethods:function(){return wi(Cu)},baseInfo:function(){return function(e){return new F((function(t){var n=Zn(e),r=Nr(e);t.next({type:ct.BASE_INFO,timestamp:Date.now(),content:i({},e,n,r)})}))}(Cu)},windowError:function(){return yi(Cu)},myTrack:function(){return function(e){return"myTrack"===e.submitType?new F((function(e){window.onHTSCEvent=function(t,n,r,o,i){if(i&&"object"!==fr(i))return console.error("数据"+t+"中btn_title类型错误，必须是json对象");e.next({type:ct.BUSINESS_POINT,content:{id:t,page_id:n,page_title:r,btn_id:o,btn_title:i||{}},timestamp:Date.now()})}})):te}(Cu)},customReport:function(){return function(e){var t=e.customReport;if(!t)return te;var n=vr(t)?t:[t],r=[];return n.forEach((function(e){var t=new F((function(t){e((function(e){t.next({type:"customReport",content:e})}))}));r.push(t)})),r.length?we(r).pipe(Te()):te}(Cu)},directObservable:function(){return c},blankScreen:function(){return Ga(Cu)}},f=Cu.types.filter((function(e){return Ru.indexOf(e)>-1})),p=function(e){f.indexOf(e)>-1||f.push(e)};p(ct.BASE_INFO),p(ct.BLANK_SCREEN),p(ct.PERFORMANCE),r&&p("myTrack"),p("routeChange"),p("customReport"),p("directObservable");var d=Cu.bufferTimeArray;Mu=Cu.uuid+"_"+Date.now();var v=Cu.maxBufferSize;Ce(1e4).subscribe((function(){v=Cu.maxBufferSize}));var h=Date.now()+"_",m=0,g=function(e){if(e.storageId)return e;var t=function(e){var t=Cu.externalInfo;if(t===lt||!t)return e;var n=i({},e),r=n.type;if(t===ft&&Pr(r)||t===pt){var o={href:window.location.href};r===dt?n.event=n.event.map((function(e){return i({},e,{external:o})})):n.content.external=o}return n}(e);return i({},t,{traceId:Mu,distinctId:Pu,uuid:Cu.uuid||Au,userId:Cu.userId||"",from:Cu.from,timestamp:t.timestamp||Date.now(),storageId:h+m++})};!function(e){Er.init();var t=Er.get()||{},n=Er.getLockedIds(),r=Object.keys(t).filter((function(e){return!n[e]})).map((function(e){return t[e]})).filter((function(t){return(null==t?void 0:t.from)===e.from}));we(r).pipe(je(10,10),qe()).subscribe((function(t){t.forEach((function(t){t&&bu(e,t)}))}))}(Cu);var y={},b=Nu(Cu).pipe(We(1),Ve((function(e){!function(e){try{var t=(e||{}).customConfig,n=null==t?void 0:t.xReplayConfig;if(!n)return void(Cu.xReplay&&qn(Cu.xReplay));var r=JSON.parse(n);if(!r.enable)return;var o=(r||{}).xReplay;qn(i({},Cu.xReplay||{},o||{}))}catch(e){console.error(e)}}(y=e.content)})),_e((function(){return{}}))),w=new $;we(f.map((function(e){return l[e].call()})).map((function(e){return e.pipe(He((function(e){return s(e)})))}))).pipe(Te()).subscribe(w);var x=b.pipe(Je((function(){return w}))).pipe(De((function(e){return e&&e.type})),De(Hr()),De((function(e){return Iu(e,y)})),_e(g),Ve((function(e){if(Pr(e.type)){var t=function(e,t){var n=t.metaEvent||[],r=function(e){return!n.length||n.indexOf(e)>-1};if(e.type===ct.BUSINESS_POINT){var o=e.content||{},i=o.id;r(i)||Eu(o,n)}else e.type===dt&&(e.event||[]).filter((function(e){return r(e.id)||Eu(e,n),!0}));return e}(e,y);t&&bu(Cu,[t])}else!function(e){if(r){var t=(e||{}).storageId;Er.lockStorageIds([t]),Er.set(t,e)}}(e)})),De((function(e){return!Pr(e.type)})),De((function(e){return function(e,t){if(e.type===ct.BASE_INFO)return!0;var n=Tu(t,"developCustomDropWeight");return!n||Math.random()>=n}(e,y)})),Ve((function(e){Gr(e)&&bu(Cu,[e])})),De((function(e){return!Gr(e)})),He((function(e){return s(e)})),Be.apply(void 0,d),De((function(e){return e.length>0})),function(e,t){void 0===t&&(t=Q);var n=Le(e,t);return Ge((function(){return n}))}(10)).subscribe((function(e){var t=e||[];if((v-=t.length)<=0){x.unsubscribe();var n=Nr(Cu);Wn();var r=g({type:ct.OBSERVABLE_ERROR,timestamp:Date.now(),content:i({recordId:Un,errorMessage:"report times more than maxBufferSize("+Cu.maxBufferSize+") in 10s",errorStack:"xlog init from is "+(Cu.from||"")},n)});t.push(r)}Cu.submitHandler&&Cu.submitHandler.call(null,t),function(e,t){if(Math.random()<e.reportRandom)_u(e,t);else{var n=t.filter((function(e){return"baseInfo"===e.type}));n.length&&_u(e,n)}}(Cu,t)}));return Lu=function(e){!function(e){var t;if(r||o){var n=e||{},a=n.id,c=n.page_id,s=n.page_title,l=n.btn_id,f=n.btn_title,p=n.timestamp,d=n.type,v=n.__channel;if(d===dt)return void u.next(e);var h=Jr(a),m=f||{};if("object"!==fr(m))return void console.error("数据"+a+"中btn_title类型错误，必须是json对象");if(!a||!c)return void console.error("错误的myTrack上报入参, id 或 page_id不存在");t={type:h?ct.DEVELOP_CUSTOM:ct.BUSINESS_POINT,content:{id:a,page_id:c,page_title:s||"",btn_id:l||"",btn_title:m,__channel:v}},!h&&p&&(t=i({},t,{timestamp:p}))}else t={type:"customReport",content:e};u.next(t)}(e)},Cu.foo=Lu,Lu.setUUID=function(e){if(e!==Cu.uuid){var t=Nr(Cu);Lu({id:"develop_custom",page_id:"login",page_title:"",btn_id:"set_uuid_login",btn_title:{content:i({currentId:e,lastId:Cu.uuid},t),timestamp:Date.now()}}),Cu.uuid=e}},Lu.setUserId=function(e){if(e!==Cu.userId){var t=(Cu||{}).onUserIdChange,n=Nr(Cu);t&&t(e),Lu({id:"develop_custom",page_id:"login",page_title:"",btn_id:"set_user_id_login",btn_title:{content:i({currentUserId:e,lastUserId:Cu.userId},n),timestamp:Date.now()}}),Du=e,Cu.userId=e}},Lu},t.log=function(e){Lu?e.channels?Fu(e):Lu(i({},e,{timestamp:Date.now()})):console.error("log：请执行init方法对xlog进行初始化")},t.logBatch=function(e){Lu?Lu({type:dt,event:e.map((function(e){return i({},e,{timestamp:Date.now()})}))}):console.error("logBatch：请执行init方法对xlog进行初始化")},t.logMultiple=Fu,t.logPV=function(e){Lu?Lu(i({id:"page"},e,{timestamp:Date.now()})):console.error("logPV：请执行init方法对xlog进行初始化")},t.logable=function(e){if(Lu)return function(t,n,r){var o=r.value;return i({},r,{value:function(){Lu(i({},e,{timestamp:Date.now()}));for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return o.apply(this,n)}})};console.error("logable：请执行init方法对xlog进行初始化")},t.reportError=function(e){Hu(e,"reportError",rt)},t.reportEvent=function(e){Hu(e,"reportEvent",ot)},t.reportPerformance=function(e){if(Lu){var t=e||{},n=t.name,r=t.timestamp,o=t.href,a=t.ext;if(n){var u=Nr(Cu),c=Rr(Cu,o||window.location.href),s=c.pagePath,l=c.pageHref,f={id:"develop_custom",page_id:"performance",page_title:"",btn_id:"timing",btn_title:{subtype:"timing",content:i({name:Cr(n),ext:Cr(a||"")},u,{pagePath:s,pageHref:l}),timestamp:r||Date.now()}};Lu(f)}else console.error("reportPerformance：name属性不能为空")}else console.error("reportPerformance：请执行init方法对xlog进行初始化")},t.reportSpeed=function(e){if(Lu){var t=e||{},n=t.name,r=t.duration,o=t.ext,a=t.labels;if(n)if(gr(r)){var u=Nr(Cu),c={id:"develop_custom",page_id:"customReport",page_title:"",btn_id:it,btn_title:{subtype:n,content:i({duration:r,ext:Cr(o||""),labels:Cr(a||"")},u),timestamp:Date.now()}};Lu(c)}else console.error("reportSpeed：duration属性必须是number类型");else console.error("reportSpeed：name属性不能为空")}else console.error("reportSpeed：请执行init方法对xlog进行初始化")},t.setUUID=function(e){Lu?Lu.setUUID(e):console.error("setUUID：请执行init方法对xlog进行初始化")},t.setUserId=function(e){Lu?Lu.setUserId(e):console.error("setUserId：请执行init方法对xlog进行初始化")},t.stringifyAny=_n,t.uuidv4=er,Object.defineProperty(t,"__esModule",{value:!0})}));


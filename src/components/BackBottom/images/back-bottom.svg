<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 10@2x</title>
    <defs>
        <circle id="path-1" cx="16" cy="16" r="16"></circle>
        <filter x="-46.9%" y="-46.9%" width="193.8%" height="193.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.196078431   0 0 0 0 0.196078431   0 0 0 0 0.196078431  0 0 0 0.15 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="APP-智能助手-输入样式" transform="translate(-162.000000, -599.000000)">
            <g id="编组-10" transform="translate(172.000000, 609.000000)">
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <g id="arrow--right" transform="translate(16.000000, 16.000000) rotate(90.000000) translate(-16.000000, -16.000000) translate(8.000000, 8.000000)">
                    <rect id="_Transparent_Rectangle_" x="0" y="0" width="16" height="16"></rect>
                    <polygon id="路径" stroke="#333333" fill="#333333" fill-rule="nonzero" stroke-linejoin="round" points="9 3 8.285 3.6965 12.075 7.5 2 7.5 2 8.5 12.075 8.5 8.285 12.2865 9 13 14 8"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>
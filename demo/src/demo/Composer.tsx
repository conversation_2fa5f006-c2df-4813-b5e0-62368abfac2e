import React, { useState } from 'react';
import { DemoPage, DemoSection } from '../components';
import { Composer } from '../../../src';

export default () => {
  const [enableInternetSearch, setEnableInternetSearch] = useState(false);

  const toggleEnableInternetSearch = () => {
    setEnableInternetSearch(!enableInternetSearch);
  };

  const onSend = async (type: string, content: string) => {
    return new Promise<boolean>((resolve) => {
      console.log('type', type, 'content', content);
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
  };

  return (
    <DemoPage>
      <DemoSection title="基础用法">
        <Composer text="ceshi" inputType="text" onSend={onSend}></Composer>
      </DemoSection>
      <DemoSection title="停止生成">
        <Composer inputType="text" showStopAnswer={true} recorder={{ canRecord: true }}></Composer>
      </DemoSection>
      <DemoSection title="联网搜索">
        <Composer
          text="ceshi"
          inputType="text"
          onSend={onSend}
          showInternetSearch={true}
          showLLMSwitch={true}
          enableInternetSearch={enableInternetSearch}
          toggleEnableInternetSearch={toggleEnableInternetSearch}
        ></Composer>
      </DemoSection>

      <DemoSection title="基础用法 - 宽屏">
        <Composer text="ceshi" inputType="text" onSend={onSend} isWide></Composer>
      </DemoSection>
      <DemoSection title="停止生成 - 宽屏">
        <Composer inputType="text" text="test" showStopAnswer={true} isWide></Composer>
      </DemoSection>
      <DemoSection title="联网搜索 - 宽屏">
        <Composer
          isWide
          text="ceshi"
          inputType="text"
          onSend={onSend}
          showInternetSearch={true}
          showLLMSwitch={true}
          llmConfig={{
            selectProps: {
              defaultValue: 'gpt-3.5-turbo',
            },
            llmOptions: [
              { label: 'gpt-3.5-turbo', value: 'gpt-3.5-turbo' },
              { label: '千问', value: '千问' },
            ],
          }}
          enableInternetSearch={enableInternetSearch}
          toggleEnableInternetSearch={toggleEnableInternetSearch}
        ></Composer>
      </DemoSection>
    </DemoPage>
  );
};

import React from 'react';
import { Message } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';
import { references } from './constants';

export default () => (
  <div className="message-demo">
    <Message
      _id="1"
      messageId="1"
      type="markdown"
      content={{
        text: '\n\n### **两融开户条件总结**  \n根据搜索结果，开通融资融券账户（两融账户）需满足以下核心条件[2][7][14][15]：  \n\n1. **年龄与民事行为能力**  \n   - 投资者需年满18周岁，且年龄一般不超过70或75周岁（具体以券商规定为准）。  \n   - 需具备完全民事行为能力，无重大信用违约记录。\n\n2. **证券交易经验**  \n   - **交易时长**：从事证券交易满6个月（从中国结算记录的首笔交易开始计算）。  \n   - **交易类型**：股票、基金、债券等交易均可计入经验。\n\n3. **资产门槛**  \n   - **日均资产要求**：申请前20个交易日，普通账户的日均证券类资产不低于50万元人民币。  \n   - **资产范围**：包括现金、股票、基金、债券、券商资管计划等（非市值，仅计算账户内资产）。\n\n4. **风险测评等级**  \n   - 风险承受能力测评需达到**C4级及以上**（积极型或进取型），测评结果需在有效期内。\n\n5. **其他要求**  \n   - 投资者在券商处无异常交易记录或违规行为。  \n   - 需签署《融资融券合同》及《风险揭示书》，并通过征信审核。  \n   - 仅允许在一家券商开通一个两融账户（需确认其他券商无存量两融账户）[9][14]。\n\n---\n\n### **补充说明**  \n- **线上/线下办理差异**：部分券商支持线上开户（需视频验证），但多数仍要求线下营业部办理[3][7]。  \n- **利率与佣金**：不同券商费率差异较大，融资利率最低可至**3.8%**（需与客户经理协商）[2][8]。  \n- **担保品划转**：开户后需划转担保品（现金或证券）至信用账户，次交易日方可交易[3][11]。\n\n建议选择服务完善、费率透明的券商，提前联系客户经理确认具体流程及优惠政策。',
      }}
      thinkContent={
        '嗯，用户想了解两融开户的条件，我需要根据提供的搜索结果来整理答案。首先，我得浏览所有资料，找出关于开户条件的部分。资料里提到多个条件，比如资产要求、交易经验、风险测评等级等。\n\n资料2提到需要半年交易经验和前20日日均50万资产，还有风险测评C4级。资料7也提到了类似的条件，包括证券交易满半年，日均资产不低于50万，风险测评C4。资料14和15进一步确认了这些条件，并补充了年龄限制和信用记录要求。\n\n我需要把这些信息综合起来，确保覆盖所有必要条件。然后，分点说明，每个点后面加上对应的引用。比如年龄和民事行为能力、交易经验、资产要求、风险测评、信用记录等。同时，注意不同资料提到的细节是否一致，比如年龄上限可能有差异，需要说明以券商规定为准。\n\n另外，用户可能还关心其他要求，比如只能开一个两融账户，这在资料9和14中都有提到。所以也要包括进去作为补充说明。最后，整理成结构清晰的回答，使用加粗标题和有序列表，并在每个要点后标注引用来源。\n'
      }
      references={references}
      referencesConfig={{
        title: (refers: any[]) => {
          return <span>相关内部资料（{refers?.length}条）</span>;
        },
        modalWidth: 712,
      }}
      position="left"
      config={{}}
      updateMsg={() => {}}
    />
    <Message
      _id="2"
      messageId="2"
      type="relatedQuestionList"
      content={{ relatedQuestionList: references }}
      position="left"
      config={{}}
      updateMsg={() => {}}
    />
  </div>
);

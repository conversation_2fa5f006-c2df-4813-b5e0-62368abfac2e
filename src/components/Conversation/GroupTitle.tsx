import React from 'react';

export interface GroupTitleProps {
    children?: React.ReactNode;
    title?: string;
    renderImg?: () => JSX.Element;
}



const GroupTitle: React.FC<GroupTitleProps> = ({ renderImg, title }) => {

    return (
        <div className='groupTitle'>
            {renderImg ? renderImg() : <div className='titleIcon' />}
            <div className='titleText'>{title}</div>
        </div>
    );
};

export default GroupTitle;

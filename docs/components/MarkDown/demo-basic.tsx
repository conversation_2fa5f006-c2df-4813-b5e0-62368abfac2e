import React from 'react';
import { MarkDown } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';

export default () => {

  const content = `### 基础语法

1.**标题**

# 标题 1
## 标题 2
### 标题 3
#### 标题 4
##### 标题 5
###### 标题 6

2.**段落和换行**

段落之间留空行
可以在行尾加两个空格来强制换行。

3.**列表**

- 无序列表

- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

- 有序列表

1. 项目 1
2. 项目 2
   1. 子项目 2.1
   2. 子项目 2.2
3. 项目 3

4.**引用**

> 这是引用的文本。

5.**粗体和斜体**

**粗体文本**
*斜体文本*

6.**链接**

[链接文本](https://tongyi.aliyun.com/)

自动识别连接： https://chatbot.console.aliyun.com/ChatSDK
链接截断验证：https://chatbot.console.aliyun.com/ChatSDK?test=测试，结束

7.**图片**

![替代文本](https://dashscope-cn-beijing.oss-cn-beijing.aliyuncs.com/code-interpreter/temp_files/code-output-20240131-120401.943328.png?OSSAccessKeyId=LTAI5tKHB4j1Cmo6kRtd5Ac8&Expires=1706704441&Signature=WWyDLwits154vO%2FaU3BFtkBc5fg%3D)


这是一个图文混排的例子
![替代文本](https://gw.alicdn.com/imgextra/i1/O1CN01TinEt01x1vHLqHZGz_!!6000000006384-2-tps-1280-960.png)


8.**PDF文件**

Prompt的类别有哪些？
![如何写好Prompt.pdf](https://files.alicdn.com/tpsservice/f6648a7e019575dfa4b708f0c635b4f2.pdf)

9.**水平线**

---

10.**代码**

- 行内代码

这是 \`行内代码\` 的例子。

- 代码块

\`\`\`javascript
function example() {
  console.log("Hello, World!");
}
\`\`\`

11.**任务列表**

- [x] 已完成任务
- [ ] 未完成任务

12.**表情符号**

:smile: :heart: :rocket:

13.**注释**

<!-- 这是注释 -->

14.**定义型列表**

苹果
: 一种水果，有很多种品种。

桔子
: 另一种水果，橙色的。

15.**LaTeX 公式**

$E=mc^2$

16.**下标和上标**

H~2~O，X^2^

17.**自动链接**

<https://www.example.com>

18.**内部链接**

[跳到文档底部](#文档底部)

19.**代码注释**

\`代码注释\`

20.**支持HTML**

<details>
  <summary>点击展开</summary>
  这是一个可以展开的内容。
</details>

21.**定义标题 ID**

### 标题 {#custom-id}

22.**表格**

| 姓名   | 年龄 | 职业         |
|--------|------|--------------|
| 张三   | 25   | 工程师       |
| 李四   | 30   | 设计师       |

23. **at**

在Markdown中使用@符号来表示"at"的意思。
注意Markdown组件需要透传一个mentionList属性，该属性是一个数组，包含所有需要@的人或团队的名称。

hello @tongyi-ui

@OKR设定专家 @3D头像设计师 @🙈齐天大圣 @资深作家

24. **识别html链接**

<a href="tel:0516-96777" target="_self">0516-96777</a>`;

    return (
      <div>
        <MarkDown content={content}/>
      </div>
      )
}

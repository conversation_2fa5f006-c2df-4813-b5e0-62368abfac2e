/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useGetMessageList } from '@ht/chatui';

export default () => {
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useGetMessageList({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      type: 'http',
      manual: true,
    },
    requestParams: {
      userId: '016645',
      appKey: 'd07gpo0im975aeu764t0',
      conversationId: 'd18ct8mr36pds5qqfirg',
      pageNum: 1,
      pageSize: 10,
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>获取消息列表</Button>
      <div>
        {conversationData?.resultData?.list?.map((item: any, index: number) => (
          <div key={index}>
            {item.list.map((subItem: any, subIndex: number) => {
              return (
                <div key={subIndex}>
                  {subItem?.content?.text}
                </div>
              )
            })}
          </div>
        ))}
      </div>
    </div>
    )
}

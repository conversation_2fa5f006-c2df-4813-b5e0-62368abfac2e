.RiskTipAppWrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 12;

  .RiskTipAppWarning {
    position: absolute;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(180deg, #F9D876 0%, #f0af41 100%);
    top: -26px;
    left: calc(50% - 28px);
    padding: 10px 0 13px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    &-up {
      width: 5px;
      height: 23px;
      background: #fff;
      border-radius: 5px;
    }

    &-down {
      width: 7px;
      height: 7px;
      background: #fff;
      border-radius: 50%;
      margin-top: 3px;
    }
  }

  .RiskTipApp {
    position: relative;
    padding-top: 50px;
    width: 315px;
    background: linear-gradient(180deg, #fff4dd 0%, #fff 115px, #fff 100%);
    border-radius: 4px;
    padding-bottom: 13px;

    &-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      line-height: 26px;
      padding-bottom: 20px;
      text-align: center;
    }

    &-content {
      padding: 0 20px;
      height: 366px;
      overflow-y: scroll;
    }

    &-contentBlock {
      margin-top: 12px;

      &:first-child {
        margin-top: 0px;
      }
    }

    &-contentFirTitle {
      font-size: 14px;
      font-weight: bold;
      color: #333333;
      line-height: 22px;
    }

    &-contentSecTitle {
      font-size: 14px;
      color: #333333;
      font-weight: 400;
      line-height: 22px;
      margin-top: 8px;
    }

    &-contentText {
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      font-weight: 400;
      margin-top: 4px;
    }

    .RiskTipItem {
      font-size: 14px;
      color: #666;
      line-height: 22px;
      padding-bottom: 15px;

      &:last-child {
        padding-bottom: 20px;
      }
    }
  }

  .RiskTipFooter {
    border-top: 1px solid #ebedf0;
    font-size: 16px;
    color: #3E74F7;
    line-height: 22px;
    text-align: center;
    padding-top: 13px;
    cursor: pointer;
  }

  .RiskTipFooterDisable {
    border-top: 1px solid #ebedf0;
    font-size: 16px;
    color: #999999;
    line-height: 22px;
    text-align: center;
    padding-top: 13px;
    cursor: not-allowed;
  }

  .RiskTipClose {
    margin-top: 30px;
    width: 40px;
    height: 40px;
  }
}
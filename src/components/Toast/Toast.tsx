import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import { Icon } from '../Icon';
import { ToastProps } from './interface';
import successSrc from './images/success.svg'

function renderIcon(type: ToastProps['type']) {
  switch (type) {
    case 'success':
      return <img src={successSrc} />;
    case 'error':
      return <Icon type="warning-circle" />;
    case 'loading':
      return <Icon type="spinner" spin />;
    default:
      return null;
  }
}

export const Toast: React.FC<ToastProps> = (props) => {
  const { content, type, duration = 2000, position = 'center', onUnmount } = props;
  const [show, setShow] = useState(false);

  useEffect(() => {
    setShow(true);

    if (duration !== -1) {
      setTimeout(() => {
        setShow(false);
      }, duration);

      setTimeout(() => {
        if (onUnmount) {
          onUnmount();
        }
      }, duration + 300);
    }
  }, [duration, onUnmount]);

  return (
    <div
      className={clsx('Toast', { show }, position && `Toast-${position}`)}
      data-type={type}
      role="alert"
      aria-live="assertive"
      aria-atomic="true"
    >
      <div className="Toast-content" role="presentation">
        {renderIcon(type)}
        <p className="Toast-message">{content}</p>
      </div>
    </div>
  );
};

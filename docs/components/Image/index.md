---
nav:
  title: 组件库
  order: -1
group:
  title: 基础卡片组件
  order: 10
order: 1
---

# Image 图片卡片

## 代码演示

### 基本

<code src="./demo-basic.tsx" ></code>

###  不支持预览

<code src="./demo-basic-preview-not.tsx" ></code>

### 响应式图片

<code src="./demo-basic-fluid.tsx" ></code>

### 懒加载

<code src="./demo-basic-lazy.tsx" ></code>

## 参数

### Image

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| className | 类名 | `string` | - |
| src | 图片地址（必填） | `string` | - |
| preview | 是否可以预览 | `boolean` | `true` |
| fluid | 响应式图片 | `boolean` | `false` |
| lazy | 图片懒加载 | `boolean` | `false` |
| onClick | 图片点击事件 | `() => void` | - |

---

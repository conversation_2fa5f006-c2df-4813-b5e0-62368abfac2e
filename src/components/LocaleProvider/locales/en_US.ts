export default {
  BackBottom: {
    newMsgOne: '{n} new message',
    newMsgOther: '{n} new messages',
    bottom: 'Bottom',
  },
  Time: {
    weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),
    formats: {
      LT: 'HH:mm',
      lll: 'M/D/YYYY HH:mm',
      WT: 'dddd HH:mm',
      YT: 'Yesterday HH:mm',
    },
  },
  Composer: {
    send: 'Send',
  },
  SendConfirm: {
    title: 'Send photo',
    send: 'Send',
    cancel: 'Cancel',
  },
  RateActions: {
    up: 'Up vote',
    down: 'Down vote',
  },
  Recorder: {
    hold2talk: 'Hold to Talk',
    release2send: 'Release to Send',
    releaseOrSwipe: 'Release to send, swipe up to cancel',
    release2cancel: 'Release to cancel',
  },
  Search: {
    search: 'Search',
  },
};

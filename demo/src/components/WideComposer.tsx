/**
 * 按照AIChat的 Footer的参数协议实现，替换默认组件
 */
import React, { useImperativeHandle, useState } from 'react';
import { Select } from '@ht/ioned'; // 临时添加@ht/ioned组件，TODO验证后移除
import { GlobalOutlined, ArrowUpOutlined } from '@ant-design/icons'; // 临时添加@ht/ioned组件，TODO验证后移除
import type { ComposerHandleProps, ComposerProps } from '../../../src';
import '@ht/ioned/dist/style.css';
import './WideComposer.less';

function WideComposer(props: ComposerProps & { chatContext: any }, ref: React.Ref<ComposerHandleProps>) {
  const { onSend, chatContext } = props;
  console.log(chatContext, 'chatContext');
  const [text, setText] = useState('');
  const [searchActive, setSearchActive] = useState(false);
  const [model, setModel] = useState('deepseeksaas');
  useImperativeHandle(ref, () => ({
    setText,
  }));

  const handleSend = () => {
    if (text.trim().length > 0) {
      /**
       * @description: 增加自定义参数
       */
      const payload: Record<string, string> = {
        model: model,
      };
      if (searchActive) {
        // 是否联网搜索
        payload.network = 'network';
      }
      const success = onSend?.('text', text, payload);
      if (success) {
        setText('');
      }
    }
  };
  return (
    <div className="touhang-chat-footer">
      <div className="chat-input-container">
        <div className="chat-textarea">
          <textarea
            placeholder="请输入您的问题，可输入Shift+Enter换行，输入Enter进行发送"
            value={text}
            onChange={(e) => setText(e.target.value)}
          />
        </div>
        <div className="chat-action">
          <div className={`extra-action`}>
            <Select value={model} onChange={(value: string) => setModel(value)} size="small" className="model-select" options={[{ label: '知谱', value: '知谱' }, { label: '千问', value: '千问' }, { label: '豆包', value: '豆包' }, { label: 'DeepSeek-R1', value: 'DeepSeek-R1' }]} defaultValue='deepseeksaas'>
            </Select>
            <div onClick={() => setSearchActive(!searchActive)} className={`search-network-button${searchActive ? ' active' : ''}`}>
              <GlobalOutlined className="search-icon" />
              <span className='search-text'>联网搜索</span>
            </div>
          </div>
          <div className="send-action">
            <div className="send-btn" onClick={handleSend}>
              <ArrowUpOutlined style={{ color: '#fff' }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default React.forwardRef<ComposerHandleProps, ComposerProps>(WideComposer);
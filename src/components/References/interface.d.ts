// 参考资料
export interface ReferencesProps extends React.HTMLAttributes<HTMLDivElement> {
  references: ReferenceProps[];
  openPage?: (url: string) => void;
  referencesConfig?: {
    title?: string | React.ReactNode | ((references: ReferenceProps[]) => React.ReactNode);
    modalWidth?: number;
  };
  preprocessContent?: (content: string) => string;
  // 是否是宽屏
  isWide?: boolean;
}

export interface ReferenceProps {
  /**
   * 链接地址
   */
  url?: string;
  /**
   * 引用名称
   */
  title: string;
  /**
   * 引用内容
   * 
   */
  content: string;
  /**
   * 引用类型
   */
  source: 'link' | 'doc';

  /**
   * 引用类型为网站时，站点名称
   */
  site_name?: string;

  /**
   * 引用类型为网站时，站点内部时间
   */
  publish_time?: number;

  slice_id?: string;
}
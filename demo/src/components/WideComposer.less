.touhang-chat-footer {
  padding: 12px 9px 11px 9px;
  height: 164px;

  .chat-input-container {
    width: 100%;
    height: 100%;
    border-radius: 2px;
    border: 1px solid #dee8f5;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    > .chat-textarea {
      padding: 12px 16px;

      > textarea {
        height: 60px;
        line-height: 20px;
        width: 100%;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #25396f;
        border: none;
        padding: 0;

        &:active {
          border: none;
          outline-width: 0;
        }
        &:focus {
          border: none;
          outline-width: 0;
        }
        &:focus-visible {
          border: none;
          outline-width: 0;
        }
        &:focus-within {
          border: none;
          outline-width: 0;
        }
      }
    }
    > .chat-action {
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 16px 0px 16px;
      > .extra-action {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        > .model-select {
          width: 145px;
          height: 24px;
        }
        > .search-network-button {
          height: 24px;
          width: 100px;
          margin-left: 10px;
          padding: 0 10px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 2px;
          border: 1px solid rgb(229, 229, 229);
          &:hover {
            cursor: pointer;
          }

          &.active {
            color: #d8a977;
            border: 1px solid #d8a977;
          }

          > .search-icon {
            width: 13px;
            height: 13px;
          }
          > .search-text {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }
        }
      }

      > .send-action {
        height: 36px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;

        > .send-btn {
          width: 30px;
          height: 30px;
          border-radius: 2px;
          overflow: hidden;
          background-color: #007aff;
          display: flex;
          justify-content: center;
          align-items: center;
          &:hover {
            cursor: pointer;
          }

          > span {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

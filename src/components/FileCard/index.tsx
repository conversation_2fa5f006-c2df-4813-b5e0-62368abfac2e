import React, { useState } from 'react';
import ReactDOM from "react-dom";
import clsx from 'clsx';
import canUse from '../../utils/canUse';
import { Card } from '../Card';
import FileViewer from '../FileViewer';
import { Flex, FlexItem } from '../Flex';
// eslint-disable-next-line @typescript-eslint/no-redeclare
import { Text } from '../Text';
import getExtName from '../../utils/getExtName';
import prettyBytes from '../../utils/prettyBytes';
import RadioIcon from './images/radioIcon.svg';
import WordIcon from './images/wordIcon.svg';
import PPtIcon from './images/pptIcon.svg';
import ExcelIcon from './images/excelIcon.svg';
import PdfIcon from './images/pdfIcon.svg';
import ZipIcon from './images/zipIcon.svg';
import ImgIcon from './images/imgIcon.svg';
import TxtIcon from './images/txtIcon.png';
import FileIcon from './images/fileIcon.png';
import { FileCardProps } from './interface';


export const FileCard: React.FC<FileCardProps> = (props) => {
  const { className, file, extension, children, openFileViews, preview = true } = props;
  const { name, size, url } = file;
  const ext = extension || getExtName(name);
  // 移动端需外部传入原生方法预览pdf文件，不传默认用浏览器的行为打开链接
  const openWebPage = openFileViews || window.open;

  // 文件预览窗口
  const [showFileViewer, setShowFileViewer] = useState(false);


  const iconMap: any = {
    raw: RadioIcon,
    wav: RadioIcon,
    flac: RadioIcon,
    ogg: RadioIcon,
    mp3: RadioIcon, // 音频文件，后续待补充
    doc: WordIcon,
    docx: WordIcon,
    xls: ExcelIcon,
    xlsx: ExcelIcon,
    ppt: PPtIcon,
    pptx: PPtIcon,
    pdf: PdfIcon,
    zip: ZipIcon,
    png: ImgIcon, // 图片文件，后续待补充
    svg: ImgIcon, // 图片文件，后续待补充
    jepg: ImgIcon, // 图片文件，后续待补充
    txt: TxtIcon,
  }

  const handleFileClick = () => {
    if (url && preview) {
      // 用来区分手机端、pc端
      if (canUse('touch')) {
        // 如果传了预览方法，以传入的方法为准
        if (openFileViews) {
          openFileViews(url);
          return;
        }

        // 铃客端直接open文件进行预览
        if (window.location?.hostname?.indexOf('linkapp') > -1) {
          window.open(url);
          return;
        }

        // 没有传预览方法可以针对pdf和docx使用内置的预览方法
        if (ext === 'pdf' || ext === 'docx') {
          setShowFileViewer(true)
          return;
        }
      } else {
        // pc端打开文件，预览或下载文件
        openWebPage(url, name);
        return;
      }
    } else {
      console.log('handleFileClick');
    }
  }

  return (
    <>
      {/* 文件预览窗口 */}

      {showFileViewer &&
        <>
          {ReactDOM.createPortal(
            <FileViewer
              file={{ url: url, name: name }}
              handleClose={() => setShowFileViewer(false)}
            />,
            document.body
          )}
        </>
      }
      <div onClick={handleFileClick}>
        <Card className={clsx('FileCard', className)} size="xl">
          <Flex align='center'>
            <div className="FileCard-icon" data-type={ext}>
              <img src={iconMap?.[ext] ?? FileIcon} alt='' className='fileIcon' ></img>
            </div>
            <FlexItem>
              <Text truncate={2} breakWord className="FileCard-name">
                <span title={name?.match(/(.*)\.[^.]+$/)?.[1]}>{name?.match(/(.*)\.[^.]+$/)?.[1]}</span>
                {/* {name?.split('.')[0]} */}

              </Text>
              <div className="FileCard-meta">
                {size != null && <span className="FileCard-size">{prettyBytes(size)}</span>}
                {/* children原组件中外部传入供下载用，定制组件中未传入 */}
                {children}
              </div>
            </FlexItem>
          </Flex>
        </Card>
      </div>
    </>
  );
};

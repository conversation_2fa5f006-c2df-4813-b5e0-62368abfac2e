import { NavbarProps } from '../Navbar';
import canUse from '../../utils/canUse';
import deepseekChat from '../Navbar/images/deepseekChat.png';
import AppBack from '../Navbar/images/appBack.png';
import HistoryIcon from '../Navbar/images/historyIcon.svg';
import NewChatIcon from '../Navbar/images/newChatIcon.svg';
import CloseIcon from '../Navbar/images/closeIcon.svg';
import { FrontendAction } from '../../action/interface';

// 用来区分手机端、pc端
const isMobile = canUse('touch');

/*
*** navbarValues参数说明 ***
**
  open?: boolean, // 是否展示navbar，默认true
  newConTooltip?: string; // 新建会话按钮提示语,pc必传值，app不传值

  // 返回按钮设置项
  showReturnButton?: boolean, // 是否展示返回按钮,默认pc不展示，app展示
  returnButtonIcon?: string; // 返回按钮图标路径
  onReturnButtonClick?: ()=>void; // 点击返回按钮响应处理函数，在组件调用处赋具体值

  // 标题区域设置
  showLogo?: boolean, // 是否展示logo,默认为true
  logo?: string, // logo图标的地址
  title?: string; // 头部标题文案，展示于logo右侧,默认为空字符串
  logoAndTitlePosition?: string; // 标题区域的位置：pc端默认靠左边，移动端默认居中

  // 历史会话按钮设置项
  showHistoryButton?: boolean, // 是否展示历史会话按钮，默认为true
  historyButtonIcon?: string, // 历史会话按钮图标路径
  historyButtonPosition?: string , // 'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
  onHistoryButtonClick?: () => void; // 点击历史会话按钮响应处理函数，在组件调用处赋具体值


  // 新建会话按钮设置项
  showNewButton?: boolean; // 是否显示新建会话按钮，默认为true
  newButtonIcon?: string, // 新建会话按钮图标路径
  onNewButtonClick?: () => void; // 点击新建会话按钮响应处理函数，在组件调用处赋具体值

  // 关闭按钮设置项
  showCloseButton?: boolean; // 是否显示关闭按钮，pc端默认true，移动端默认false
  closeButtonIcon?: string; // 关闭按钮图标路径
  onCloseButtonClick?: () => void; // 关闭按钮点击响应处理函数，在组件调用处赋具体值
**
*/

/*
* navbar默认配置
*/
const defaultNavBarValues = isMobile ? {
    open: true,
    newConTooltip: '',
    showReturnButton: true,
    returnButtonIcon: AppBack,
    showLogo: true,
    logo: deepseekChat,
    title: '',
    logoAndTitlePosition: 'center',
    showHistoryButton: true,
    historyButtonIcon: HistoryIcon,
    historyButtonPosition: 'right',
    showNewButton: true,
    newButtonIcon: NewChatIcon,
    showCloseButton: false,
} : {
    open: true,
    newConTooltip: '开启新对话',
    showReturnButton: false,
    showLogo: true,
    logo: deepseekChat,
    title: 'AI助手',
    logoAndTitlePosition: 'left',
    showHistoryButton: true,
    historyButtonIcon: HistoryIcon,
    historyButtonPosition: 'right',
    showNewButton: true,
    newButtonIcon: NewChatIcon,
    showCloseButton: true,
    closeButtonIcon: CloseIcon,
};

/*
* 引导页navbar默认配置
*/
const defaultWelcomeNavBarValues = isMobile ? {
    open: true,
    newConTooltip: '',
    showReturnButton: true,
    returnButtonIcon: AppBack,
    showLogo: false,
    logo: deepseekChat,
    title: '',
    logoAndTitlePosition: 'center',
    showHistoryButton: true,
    historyButtonIcon: HistoryIcon,
    historyButtonPosition: 'right',
    showNewButton: true,
    newButtonIcon: NewChatIcon,
    showCloseButton: false,
    closeButtonIcon: '',
}: {
    open: true,
    newConTooltip: '开启新对话',
    showReturnButton: false,
    showLogo: false,
    logo: deepseekChat,
    title: '',
    logoAndTitlePosition: 'left',
    showHistoryButton: true,
    historyButtonIcon: HistoryIcon,
    historyButtonPosition: 'right',
    showNewButton: true,
    newButtonIcon: NewChatIcon,
    showCloseButton: true,
    closeButtonIcon: CloseIcon,
};

/**
 * 历史会话页navbar默认配置
 */
  const  defaultHistoryConversationNavBarValues = isMobile ? {
    showLogo: true,
    logo: deepseekChat,
    title: '历史会话记录',
    showNewButton: false,
    showReturnButton: false,
    showHistoryButton: false,
    logoAndTitlePosition: "left",
  } : {
    showLogo: true,
    logo: deepseekChat,
    title: '历史会话记录',
    showNewButton: true,
    newConTooltip: '开启新对话',
    showCloseButton: true,
    showReturnButton: false,
    showHistoryButton: false,
    logoAndTitlePosition: "left",
  };

// navBar组件props预处理
export const PreprocessNavBarConfig = (customLogo: string, commonNavBar?: NavbarProps,  welcomeNavbar?: NavbarProps, historyConversationNavbar?: NavbarProps ) => {

    const navBarConfig = {
      navBar: {...defaultNavBarValues, ...commonNavBar, logo: customLogo,},
      welcomeNavBar: {...defaultWelcomeNavBarValues},
      historyConversationNavbar: {...defaultHistoryConversationNavBarValues},
    };
    // 如果外部传入welcomeNavbar,则欢迎页优先使用外部传入的welcomeNavbar，否则使用全局传入的commoNavBar
    if (welcomeNavbar && Object.keys(welcomeNavbar)?.length) {
      navBarConfig.welcomeNavBar = {
        ...navBarConfig.welcomeNavBar,
        ...welcomeNavbar,
        logo: customLogo,
      } as  any;
    }  else if (commonNavBar && Object.keys(commonNavBar)?.length) {
      navBarConfig.welcomeNavBar = {
        ...navBarConfig.welcomeNavBar,
        ...commonNavBar,
        logo: customLogo,
      } as any;
    }

    // 如果外部传入historyConversationNavbar,则欢迎页优先使用外部传入的historyConversationNavbar，否则使用全局传入的commoNavBar
    if (historyConversationNavbar && Object.keys(historyConversationNavbar)?.length) {
      navBarConfig.historyConversationNavbar = {
        ...navBarConfig.historyConversationNavbar,
        ...historyConversationNavbar,
        logo: customLogo,
      } as  any;
    }  else if (commonNavBar && Object.keys(commonNavBar)?.length) {
      navBarConfig.historyConversationNavbar = {
        ...navBarConfig.historyConversationNavbar,
        ...commonNavBar,
        logo: customLogo,
      } as any;
    }

    return navBarConfig;
  }

  const hasFrontActionFlag = (data: any, actions: FrontendAction[]) => {
    let hasFrontAction = false;
    
    // 检查数据是否包含有效的action，并且在actions数组中能找到匹配的动作
    if (data?.action?.name && actions?.find((action: FrontendAction) => action.name === data?.action?.name)) {
      hasFrontAction = true;
    }
    
    return hasFrontAction;
  }

  export { hasFrontActionFlag };

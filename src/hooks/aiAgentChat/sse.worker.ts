export const workerCode = String.raw`
// SSE数据流处理Worker

// 常量定义
const EVENT_SEPARATOR = '\n\n'; // 事件分隔符
const DATA_PREFIX = 'data:'; // 数据前缀

/**
 * 发送消息到主线程
 */
function postToMain(type, payload = {}) {
  self.postMessage({ type, ...payload });
}

/**
 * 高精度定时器 - 比setTimeout更精确
 */
function preciseTimeout(callback, interval) {
  const start = Date.now();
  const tick = () => {
    const elapsed = Date.now() - start;
    if (elapsed >= interval) {
      callback();
      return;
    }
    
    // 针对剩余时间调整检查频率
    const remaining = interval - elapsed;
    const nextCheck = remaining > 25 ? 25 : 1;
    setTimeout(tick, nextCheck);
  };
  
  setTimeout(tick, 0);
}

/**
 * 处理SSE数据流
 */
async function processEventStream(reader, messageInterval, perMessageNum) {
  const decoder = new TextDecoder("utf-8");
  
  // 初始化状态和缓冲区
  let buffer = '';
  const messageQueue = [];
  let isProcessing = false;
  let isStreamDone = false;
  let endPromiseResolve = null;
  
  const endPromise = new Promise(resolve => {
    endPromiseResolve = resolve;
  });
  
  // 处理单条消息
  const processNextMessage = () => {
    if (messageQueue.length === 0) {
      isProcessing = false;
      
      // 只有在流结束且没有更多消息时才发送onEnd
      if (isStreamDone && messageQueue.length === 0) {
        postToMain('onEnd');
        endPromiseResolve();
      }
      return;
    }
    
    isProcessing = true;
    const message = messageQueue.splice(0, perMessageNum);
    
    postToMain('onMessage', { data: message.join('') });
    
    setTimeout(processNextMessage, messageInterval);
  };
  
  // 启动消息处理
  const startProcessing = () => {
    if (isProcessing) return;
    
    if (messageQueue.length > 0) {
      processNextMessage();
    }
  };
  
  // 提取并添加单个事件到队列
  const extractAndAddEvent = (eventData) => {
    if (eventData && eventData.includes(DATA_PREFIX)) {
      messageQueue.push(eventData);
      startProcessing();
    }
  };
  
  try {
    let done = false;
    
    while (!done) {
      const readResult = await reader.read();
      done = readResult.done;
      
      if (done) {
        let eventEndIndex;
        while ((eventEndIndex = buffer.indexOf(EVENT_SEPARATOR)) !== -1) {
          const eventData = buffer.substring(0, eventEndIndex + EVENT_SEPARATOR.length);
          buffer = buffer.substring(eventEndIndex + EVENT_SEPARATOR.length);
          extractAndAddEvent(eventData);
        }
        
        if (buffer.length > 0) {
          if (buffer.includes(DATA_PREFIX)) {
            extractAndAddEvent(buffer);
          }
          buffer = '';
        }
        
        // 标记流结束，但等待所有消息处理完毕
        isStreamDone = true;
        startProcessing();
        
        // 等待所有消息处理完毕
        if (!isProcessing && messageQueue.length === 0) {
          postToMain('onEnd');
          endPromiseResolve();
        } else {
          // 等待processNextMessage处理完所有消息后调用endPromiseResolve
          await endPromise;
        }
        
        break;
      }
      
      if (!readResult.value) continue;
      
      const chunk = decoder.decode(readResult.value, { stream: true });
      buffer += chunk;
      
      let eventEndIndex;
      while ((eventEndIndex = buffer.indexOf(EVENT_SEPARATOR)) !== -1) {
        const eventData = buffer.substring(0, eventEndIndex + EVENT_SEPARATOR.length);
        buffer = buffer.substring(eventEndIndex + EVENT_SEPARATOR.length);
        extractAndAddEvent(eventData);
      }
    }
  } finally {
    reader.releaseLock();
  }
}

/**
 * 创建并处理SSE连接
 */
async function createAndProcessSSE({
  finalUrl,
  url,
  type,
  method,
  headers,
  isGetMethod,
  data,
  messageInterval,
  perMessageNum
}) {
  try {
    const response = await fetch(finalUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        ...headers,
      },
      ...(isGetMethod ? {} : { body: JSON.stringify(data) })
    });
    
    if (!response.ok) {
      throw new Error('HTTP error! status: ' + response.status);
    }
    
    const reader = response?.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流读取器');
    }

    await processEventStream(reader, messageInterval, perMessageNum);
  } catch (error) {
    postToMain('onError', { 
      error: error instanceof Error ? error.message : '流式读取错误' 
    });
  }
}

// Worker入口点
self.onmessage = (event) => {
  const { 
    finalUrl,
    url,
    type,
    tcpRequest,
    method, 
    headers, 
    isGetMethod, 
    data, 
    messageInterval = 100, 
    perMessageNum = 1 
  } = event.data;
  
  createAndProcessSSE({
    finalUrl,
    url,
    type,
    tcpRequest,
    method, 
    headers, 
    isGetMethod, 
    data, 
    messageInterval, 
    perMessageNum
  });
};
`;
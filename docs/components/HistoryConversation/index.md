---
nav:
  title: 组件库
  order: -1
group:
  title: 核心对话组件
  order: 10
order: 1
---

# HistoryConversation 历史会话组件


## 代码演示

<code src="./demo-basic.tsx" ></code>


## 参数

### HistoryConversationProps
⚠️ 注意：由于集成对话组件内部对历史会话组件的点击事件等属性做了封装，在AiChat与WideAiChat组件中可配置的参数与独立使用该组件时不同。
在集成对话组件中使用时，参数配置请查看： [HistoryConversationProps](/components#historyconversationprops)

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| style | 容器样式 | `CSSProperties` | - |
| renderTitle | 自定义标题区域渲染函数 | `() => ReactJSX.Element \| React.Node` | - |
| showSearch | 是否显示搜索框 | `boolean` | `true` |
| searchPlaceholder | 搜索框占位文本 | `string` | `请输入搜索会话` |
| activeConversationId | 当前高亮会话项的conversationId |`string` | - |
| onConversationClick | 点击会话项的回调函数 | `(item:` [ConversationItemProps](/components/history-conversation#conversationitemprops)`) => void` | - | 
| list | 会话数据列表 | <a href="/components/history-conversation#conversationitemprops">ConversationItemProps[ ]</a> | - |
| renderFooter | 自定义底部区域渲染函数 | `() => ReactJSX.Element \| React.Node` | - |


### ConversationItemProps 

| 参数 | 说明 | 类型 | 默认值 | 
|------|------|--------|------|
| conversationId |会话唯一标识 | `string` | - | 
| createTime | 会话创建Unix时间戳（Unix Timestamp），精度为毫秒级 |`number` | - | 
| question | 会话标题/问题 | `string` | - | 
| scenario | 业务场景标识 |`string` | - | 
| disabled | 是否禁用该条目 |`boolean` | - | 


## 覆盖less变量

| 参数 | 说明 |
|------|------|
| @history-conversation-bg | 背景颜色 |
| @history-conversation-active-bg | 高亮会话背景颜色 |
| @history-conversation-active-color | 高亮会话文本颜色 |


---

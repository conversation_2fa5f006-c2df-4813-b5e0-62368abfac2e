/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable compat/compat */
import React, {
    useState,
    useRef,
    useEffect,
    CSSProperties,
    useCallback,
} from "react";
import ReactDOM from "react-dom";
import { TooltipProps } from './interface';

type Placement =
    | "top"
    | "topLeft"
    | "topRight"
    | "bottom"
    | "bottomLeft"
    | "bottomRight"
    | "left"
    | "leftTop"
    | "leftBottom"
    | "right"
    | "rightTop"
    | "rightBottom";

const placementHierarchy: Record<Placement, Placement[]> = {
    top: ["bottom", "right", "left", "topLeft", "topRight", "bottomLeft", "bottomRight"],
    topLeft: ["topRight", "bottomLeft", "bottom", "right", "left"],
    topRight: ["topLeft", "bottomRight", "bottom", "left", "right"],
    bottom: ["top", "left", "right", "bottomLeft", "bottomRight"],
    bottomLeft: ["bottomRight", "topLeft", "top", "right", "left"],
    bottomRight: ["bottomLeft", "topRight", "top", "left", "right"],
    left: ["right", "bottom", "top", "leftTop", "leftBottom"],
    leftTop: ["leftBottom", "rightTop", "bottom", "top"],
    leftBottom: ["leftTop", "rightBottom", "top", "bottom"],
    right: ["left", "bottom", "top", "rightTop", "rightBottom"],
    rightTop: ["rightBottom", "leftTop", "bottom", "top"],
    rightBottom: ["rightTop", "leftBottom", "top", "bottom"],
};

export const Tooltip: React.FC<TooltipProps> = ({
    content,
    placement = "top",
    children,
    mouseEnterDelay = 0.1,
    mouseLeaveDelay = 0.1,
    visible: propVisible,
    onVisibleChange,
    theme,
    autoAdjust = true,
}) => {
    const [internalVisible, setInternalVisible] = useState(false);
    // const [adjustedPlacement, setAdjustedPlacement] = useState(placement);
    const [position, setPosition] = useState<CSSProperties>({});
    const [arrowPosition, setArrowPosition] = useState<CSSProperties>({});
    const triggerRef = useRef<HTMLDivElement>(null);
    const tooltipRef = useRef<HTMLDivElement>(null);
    let enterTimer: NodeJS.Timeout;
    let leaveTimer: NodeJS.Timeout;

    const visible = propVisible !== undefined ? propVisible : internalVisible;

    // 检测元素是否在视口内（包含安全边距）
    const isInViewport = (rect: DOMRect, buffer: number = 2): boolean => {
        return (
            rect.left >= buffer &&
            rect.left + rect.width <= window.innerWidth - buffer &&
            rect.top >= buffer &&
            rect.top + rect.height <= window.innerHeight - buffer
        );
    };

    // 计算 Tooltip 位置（基于触发元素的绝对坐标）
    const calculatePosition = (
        p: Placement,
        triggerRect: DOMRect,
        tooltipRect: DOMRect,
        childrenRect: DOMRect
    ): { position: CSSProperties; arrowPosition: CSSProperties } => {
        const scrollX = window.scrollX;
        const scrollY = window.scrollY;

        // Tooltip 位置
        const positions: Record<Placement, CSSProperties> = {
            top: {
                left: triggerRect.left + scrollX + childrenRect.width / 2 - tooltipRect.width / 2,
                top: childrenRect.top + scrollY - tooltipRect.height - 8, // 8px 为箭头高度
            },
            topLeft: {
                left: childrenRect.left + scrollX - childrenRect.width / 2,
                top: childrenRect.top + scrollY - tooltipRect.height - 8,
            },
            topRight: {
                left: childrenRect.left + scrollX + childrenRect.width - tooltipRect.width + childrenRect.width / 2,
                top: childrenRect.top + scrollY - tooltipRect.height - 8,
            },
            bottom: {
                left: childrenRect.left + scrollX + childrenRect.width / 2 - tooltipRect.width / 2,
                top: childrenRect.top + scrollY + childrenRect.height + 8,
            },
            bottomLeft: {
                left: childrenRect.left + scrollX - childrenRect.width / 2,
                top: childrenRect.top + scrollY + childrenRect.height + 8,
            },
            bottomRight: {
                left: childrenRect.left + scrollX + childrenRect.width - tooltipRect.width + childrenRect.width / 2,
                top: childrenRect.top + scrollY + childrenRect.height + 8,
            },
            left: {
                left: childrenRect.left + scrollX - tooltipRect.width - 8,
                top: triggerRect.top + scrollY + triggerRect.height / 2 - tooltipRect.height / 2,
            },
            leftTop: {
                left: childrenRect.left + scrollX - tooltipRect.width - 8,
                top: triggerRect.top + scrollY,
            },
            leftBottom: {
                left: childrenRect.left + scrollX - tooltipRect.width - 8,
                top: triggerRect.top + scrollY + triggerRect.height - tooltipRect.height,
            },
            right: {
                left: childrenRect.left + scrollX + childrenRect.width + 8,
                top: triggerRect.top + scrollY + triggerRect.height / 2 - tooltipRect.height / 2,
            },
            rightTop: {
                left: childrenRect.left + scrollX + childrenRect.width + 8,
                top: triggerRect.top + scrollY,
            },
            rightBottom: {
                left: childrenRect.left + scrollX + childrenRect.width + 8,
                top: triggerRect.top + scrollY + triggerRect.height - tooltipRect.height,
            },
        };

        // 箭头位置
        // const arrowPositions: Record<Placement, CSSProperties> = {
        //     top: {
        //         left: "50%",
        //         bottom: "-4px",
        //         transform: "translateX(-50%) rotate(45deg)",
        //     },
        //     topLeft: {
        //         left: `${triggerRect.width / 2}px`,
        //         bottom: "-4px",
        //         transform: "translateX(-50%) rotate(45deg)",
        //     },
        //     topRight: {
        //         right: `${triggerRect.width / 2}px`,
        //         bottom: "-4px",
        //         transform: "translateX(50%) rotate(45deg)",
        //     },
        //     bottom: {
        //         left: "50%",
        //         top: "-4px",
        //         transform: "translateX(-50%) rotate(45deg)",
        //     },
        //     bottomLeft: {
        //         left: `${triggerRect.width / 2}px`,
        //         top: "-4px",
        //         transform: "translateX(-50%) rotate(45deg)",
        //     },
        //     bottomRight: {
        //         right: `${triggerRect.width / 2}px`,
        //         top: "-4px",
        //         transform: "translateX(50%) rotate(45deg)",
        //     },
        //     left: {
        //         top: "50%",
        //         right: "-4px",
        //         transform: "translateY(-50%) rotate(45deg)",
        //     },
        //     leftTop: {
        //         top: `${triggerRect.height / 2}px`,
        //         right: "-4px",
        //         transform: "translateY(-50%) rotate(45deg)",
        //     },
        //     leftBottom: {
        //         bottom: `${triggerRect.height / 2}px`,
        //         right: "-4px",
        //         transform: "translateY(50%) rotate(45deg)",
        //     },
        //     right: {
        //         top: "50%",
        //         left: "-4px",
        //         transform: "translateY(-50%) rotate(45deg)",
        //     },
        //     rightTop: {
        //         top: `${triggerRect.height / 2}px`,
        //         left: "-4px",
        //         transform: "translateY(-50%) rotate(45deg)",
        //     },
        //     rightBottom: {
        //         bottom: `${triggerRect.height / 2}px`,
        //         left: "-4px",
        //         transform: "translateY(50%) rotate(45deg)",
        //     },
        // };
        // 计算箭头位置

        const arrowPositions = {
            'top': {
                bottom: '6px',
                left: 'calc(50% - 3px)',
                transform: 'translateX(-50%) rotate(225deg)',
                // 旋转中心设为直角顶点（右下角）
                transformOrigin: '100% 100%'
            },
            'topLeft': {
                bottom: '6px',
                left: `${triggerRect.width / 2 - 8}px`,
                transform: 'rotate(225deg)',
                transformOrigin: '100% 100%'
            },
            'topRight': {
                bottom: '6px',
                right: `${triggerRect.width / 2}px`,
                transform: 'rotate(225deg)',
                transformOrigin: '100% 100%'
            },
            'bottom': {
                top: '-2px',
                left: 'calc(50% - 3px)',
                transform: 'translateX(-50%) rotate(45deg)',
                transformOrigin: '100% 100%'
            },
            'bottomLeft': {
                top: '-2px',
                left: `${triggerRect.width / 2 - 8}px`,
                transform: 'rotate(45deg)',
                transformOrigin: '100% 100%'
            },
            'bottomRight': {
                top: '-2px',
                right: `${triggerRect.width / 2}px`,
                transform: 'rotate(45deg)',
                transformOrigin: '100% 100%'
            },
            'left': {
                right: '6px',
                top: 'calc(50% - 5px)',
                transform: 'translateY(-50%) rotate(135deg)',
                transformOrigin: '100% 100%'
            },
            'leftTop': {
                right: '6px',
                top: '3px',
                transform: 'rotate(135deg)',
                transformOrigin: '100% 100%'
            },
            'leftBottom': {
                right: '6px',
                bottom: '11px',
                transform: 'rotate(135deg)',
                transformOrigin: '100% 100%'
            },
            'right': {
                left: '-2px',
                top: 'calc(50% - 5px)',
                transform: 'translateY(-50%) rotate(-45deg)',
                transformOrigin: '100% 100%'
            },
            'rightTop': {
                left: '-2px',
                top: '3px',
                transform: 'rotate(-45deg)',
                transformOrigin: '100% 100%'
            },

            'rightBottom': {
                left: '-2px',
                bottom: '11px',
                transform: 'rotate(-45deg)',
                transformOrigin: '100% 100%'
            },

        };

        return {
            position: positions[p],
            arrowPosition: arrowPositions[p],
        };
    };

    // 自动调整位置（核心逻辑）
    const calculateAdjustedPlacement = (
        triggerRect: DOMRect,
        tooltipRect: DOMRect,
        childrenRect: DOMRect
    ): Placement => {
        if (!autoAdjust) return placement;

        // 检查候选位置是否在视口内
        const isValidPlacement = (p: Placement): boolean => {
            const { position } = calculatePosition(p, triggerRect, tooltipRect, childrenRect);
            const rect = new DOMRect(
                Number(position.left!),
                Number(position.top!),
                tooltipRect.width,
                tooltipRect.height
            );
            return isInViewport(rect);
        };

        if (isValidPlacement(placement)) return placement;

        // 按优先级顺序检查
        for (const p of placementHierarchy[placement as Placement]) {
            if (isValidPlacement(p)) return p;
        }

        return placement;
    };

    // 更新位置和箭头方向
    const updatePosition = useCallback(() => {
        if (!triggerRef.current || !tooltipRef.current) return;

        const childrenElement = triggerRef.current.firstChild as HTMLElement;
        if (!childrenElement) return;

        const childrenRect = childrenElement.getBoundingClientRect(); // 获取 children 的实际尺寸

        const triggerRect = triggerRef.current.getBoundingClientRect();
        const tooltipRect = tooltipRef.current.getBoundingClientRect();

        const newPlacement = calculateAdjustedPlacement(triggerRect, tooltipRect, childrenRect);
        // setAdjustedPlacement(newPlacement);
        const { position: newPosition, arrowPosition: newArrowPosition } =
            calculatePosition(newPlacement, triggerRect, tooltipRect, childrenRect);

        setPosition(newPosition);
        setArrowPosition(newArrowPosition);
    }, [autoAdjust, placement]);

    // 显示/隐藏控制
    const handleMouseEnter = () => {
        clearTimeout(leaveTimer);
        enterTimer = setTimeout(() => {
            setInternalVisible(true);
            onVisibleChange?.(true);
        }, mouseEnterDelay * 1000);
    };

    const handleMouseLeave = () => {
        clearTimeout(enterTimer);
        leaveTimer = setTimeout(() => {
            setInternalVisible(false);
            onVisibleChange?.(false);
        }, mouseLeaveDelay * 1000);
    };

    // 监听窗口变化和滚动
    useEffect(() => {
        if (visible) {
            updatePosition();
            // window.addEventListener("resize", updatePosition);
            window.addEventListener("scroll", updatePosition, true);
        }
        return () => {
            // window.removeEventListener("resize", updatePosition);
            window.removeEventListener("scroll", updatePosition, true);
        };
    }, [visible, updatePosition]);

    // 监听 children 尺寸变化，必要！！防止首次位置计算不对！
    useEffect(() => {
        if (!triggerRef.current) return;

        const observer = new ResizeObserver(() => {
            if (visible) updatePosition();
        });

        observer.observe(triggerRef.current);
        return () => observer.disconnect();
    }, [visible, updatePosition]);

    return (
        <>
            <div
                className="tooltip-wrapper"
                ref={triggerRef}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                {children}
            </div>
            {visible &&
                ReactDOM.createPortal(
                    <div
                        ref={tooltipRef}
                        className="tooltip"
                        style={{
                            ...position,
                            position: "absolute",
                            backgroundColor: theme?.background,
                            color: theme?.color,
                            borderRadius: theme?.borderRadius,
                            boxShadow: theme?.boxShadow,
                            zIndex: 9999, // 确保层级最高
                            maxWidth: theme?.maxWidth,
                        }}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                    >
                        <div className="tooltip-content">{content}</div>
                        <div
                            className="tooltip-arrow"
                            style={{
                                ...arrowPosition,
                                backgroundColor: theme?.arrowColor || theme?.background || 'rgba(0, 0, 0, 0.85)',
                                position: 'absolute',
                                width: '8px',
                                height: '8px',
                                // 等腰直角三角形，直角在右下角（初始状态）
                                clipPath: 'polygon(0 0, 100% 0, 0 100%)'
                            }}
                        />
                    </div>,
                    document.body
                )}
        </>
    );
};

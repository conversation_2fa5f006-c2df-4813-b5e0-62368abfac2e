import React from 'react';
import clsx from 'clsx';
import CiteIcon from './CiteIcon';
import { Flex } from '../Flex';
import { CitewebCardProps, CitewebCardContentProps } from './interface';

export const CitewebCard: React.FC<CitewebCardProps> = (props) => {
  const { content = {} as CitewebCardContentProps, openPage } = props;

  // 不传默认用浏览器的行为打开链接
  const openWebPage = openPage || window.open;
  const handleClick = () => {
    if (content?.url) {
      openWebPage(content?.url);
    }
  };

  return (
    <div className={clsx('CitewebCard', props?.className)}>
      <Flex align="center">
        <CiteIcon className="CitewebCard-icon" />
        <div className="CitewebCard-content">
          <div className="CitewebCard-title">{content?.title}</div>
          <div
            className="CitewebCard-link"
            onClick={handleClick}
          >
            {content?.url}
          </div>
        </div>
      </Flex>
    </div>
  )
};

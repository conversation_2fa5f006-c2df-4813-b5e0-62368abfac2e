/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-02 19:25:07
 */
import React, { useCallback } from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button } from '../../../src';
import { useSensitive } from '../../../src/hooks/aiAgentChat/index';

/**
 * 敏感词校验演示组件
 * 展示如何使用useFeedback钩子进行敏感词校验
 */
export default function SensitiveDemo() {
  // 初始化反馈钩子
  const { 
    data: sensitiveData,
    loading: sensitiveLoading,
    run: submitSensitive 
  } = useSensitive({
    // 请求配置
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      type: 'http',
      manual: true,
      headers: { 
        empid: '002332', 
        token: 'token', 
        'iv-user': '002332', 
        'deviceId': 'deviceId' 
      },
    },
    // 请求参数
    requestParams: {
      question: '中国共产党',
    },
  });

  // 处理提交反馈
  const handleSubmitSensitive = useCallback(() => {
    submitSensitive();
  }, [submitSensitive]);

  return (
    <DemoPage>
      <DemoSection title="敏感词校验演示">
        <Button 
          onClick={handleSubmitSensitive} 
          loading={sensitiveLoading}
        >
          敏感词校验
        </Button>
        
        <div style={{ color: sensitiveData?.resultData.ifPass ? 'blue' : 'red', marginTop: '8px' }}>
          {sensitiveData?.resultData?.msg}: {sensitiveData?.resultData?.data}
        </div>
        
      </DemoSection>
    </DemoPage>
  );
}
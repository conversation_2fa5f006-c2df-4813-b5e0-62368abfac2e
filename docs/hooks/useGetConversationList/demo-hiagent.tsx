/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, List, ListItem, useGetConversationList } from '@ht/chatui';

export default () => {

  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useGetConversationList({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      manual: true,
      type: 'http',
    },
    requestParams: {
      userId: '016645',
      appKey: 'd07gpo0im975aeu764t0',
    },
  });

  return (
    <div>
      <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>获取会话列表</Button>
      <List bordered>
        {conversationData?.resultData?.list?.map((item: any, index: number) => (
          <ListItem key={index} content={`${item.conversationId}: ${item.question}`} />
        ))}
      </List>
    </div>
    </div>
    )
}

/* eslint-disable import/no-extraneous-dependencies */
import React, { useCallback } from 'react';
import { log } from '@ht/xlog';
// import  {API_PREFIX} from '@lowcode/lc-render';
import LCRender from '@lowcode/lc-render';
import { DemoPage } from '../components';
import AiChat from '../../../src';
import LogoPng from './images/deepseekChat.png';
import { quoteOperationsMocks } from './mock/quoteOperations';
import { skillsMock } from './mock/skill';

//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;

// 魔方卡片数据示例
// let data = {

//   "cardBizType": "447e0542ca8f79b1cbbc", // 当前魔方卡片id
//   "cardCategory": "lowcode-card",
//   "context": { //下转魔方详情页参数，可选，如果需要卡片交互时候选择
//     "tradingCode": "000001"
//   },
//   "lowCodeDetailMeta": { //下转魔方详情页的魔方id，可选，如果需要卡片交互时候选择
//     "appId": "bd908367baee85cf3e7f"
//   },
//   "view": { //魔方卡片涉及的参数props，自定义
//     "annReturnTot": 7.85722786,
//     "companyQuality": {
//       "managerName": "基金管理人", "rankPercent": 69.0
//     },
//     "exchangeCode": "OF",
//     "existing": true,
//     "htTypeCodeii": "002002",
//     "htTypeNameii": "灵活配置基金",
//     "opinionDate": 1726761600000,
//     "secuabbr": "华夏成长混合A",
//     "secucode": 53110,
//     "simpleEvaluateResults": [{
//       "conclusion": "历史收益能力一般", "rankPercent": 34.0
//     },
//     {
//       "conclusion": "风险控制能力一般", "rankPercent": 41.0
//     },
//     {
//       "conclusion": "择券配置能力较差", "rankPercent": 14.0
//     },
//     {
//       "conclusion": "投资独立性优秀", "rankPercent": 100.0
//     }
//     ],
//     "totalScoreScript": {
//       "highlightKeyWords": ["33.42", "较差"], "lowlightKeyWords": [], "script": "本基金近1年综合得分为33.42，超过12%的同类基金，总体表现较差"
//     },
//     "tradingCode": "000001"
//   }
// };

//初始化消息列表
const initialMessages: MessageWithoutId[] = [
  {
    type: 'txt',
    content: { text: 'Hi，我是你的专属智能助理小蜜，有问题请随时找我哦~' },
    // user: { avatar: '//gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg', name: '小小蜜' },
    createdAt: Date.now(),
    hasTime: true,
  },
  {
    type: 'richtext',
    content: {
      // text: '<h2>链接</h2><p>请点击以下链接访问外部网站：</p><a href="https://www.example.com">示例链接</a> <h2>图片</h2><p>这是一张示例图片：</p><img src="https://s3.cn-north-1.amazonaws.com.cn/s3-000045-cfglpic/3c792586-3d29-4ea9-af30-1c9363f2b62d_%E8%B6%85%E7%BA%A7ETF%E4%BA%A7%E5%93%81%E4%BB%8B%E7%BB%8D%E8%AF%BE%E7%A8%8B%E5%B0%81%E9%9D%A2.jpg" alt="示例图片" style="width: 300px; height: 300px;"><h2>PDF文件</h2><p>请点击以下链接下载PDF文件：</p><a href="../assets/test.pdf">示例PDF文件</a>',
      text: '<h1>欢迎来到富文本世界</h1><p>这是一个包含多种标签的富文本示例内容。</p><h2>标题2</h2><p>这里是标题2的内容。</p><h3>标题3</h3><p>这里是标题3的内容。</p><a href="https://www.example.com">这是一个链接</a><p>这是一个包含图片的段落。</p><img src="http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg" alt="示例图片">'
      // text: '<p>文本文本文本</p><p><a href="http://www.baidu.com" rel="noopener noreferrer" target="_blank">链接链接链接</a></p><p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAG4AAABVCAIAAAAnhEbCAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAEXRFWHRTb2Z0d2FyZQBTbmlwYXN0ZV0Xzt0AAApfSURBVHic3Vx7bBzFGf9md+986wclFiQUSuLgi3mE4hoQLYGqIXZSqShQCqJtiKD8Uak0DUG0SA1qQ1pFKJSKtErVF02JIpUmDQRCLVClSm2JEwghqkoSEscJVE1ArfKwHdtn393uTv+Y29l57d3e3e56zz9Z1nl2dueb337zvWbOCGMMMxFkXohtAQAAhBDGGLktCCHav/QJsTdVATQjqZQnJXDHsikD1cSmVsM9CQerjwghJS8IoZKSMj/0Um3jzkAqAQDx6kaJA0ZhSSP7A3XwCDOSSk/jKvFCO5AP9fAIAEY9NycWAUnBGAts1oMZQiXrZ2RSlFdJI8tmnZgJC5z6GfKjjEmQ6pZw0fBUUh4pN35sCreUOoekktDoVFJSqFeWPbXfLRAqj9DoVIJP/CgzhKX2cHmEhqay5Ddqujd0HqGhqQwex5SSxTDi8DJo7GAoePxY7S01oIG1MiDi4REai8rgwSBbAaKNkfIIDUQlTU6C94+TR2gUKqtNTgTaYuARGoLK2pSr/vpjtUg6lX48KvWU1CbIhUjjHiUSvSHB5tdCRYdASS7ZvYF4eYSEUwn8JozHEb2q2ufCsZNIkPQFLusdl0dLK30a9SLpVAKUdlPpTlYp/2P2uTw2w9hXqBmJW+C+Zs4VlLvkVjSmiz4WydJKr/4ov2BJ43DCtCBZVAK7kOVjAbzqJUETWSSISi7KkVrknvXUK6NAgqgEN4RkK94ym6WWePPrIEhWvRIzHxBlVt5ftWycz2MNIdNU8ogvXJj63VYAMB9/NGKRPUTrwa1DR/IvbKvnCdrcKzNrVgGvevn+NyYeedT49PUXvf6K8i7n9Ecjty4GgPZTQ/WMXhWi1Urn44/zO3fV8wSjp9tcs0rQzYSsaAFxLPD08i+Zq77Ftjjnh613D6b6liDN11g754fHVjxEPvuu9CQhDiq19nZ94bXe35adW/lwce9bqLU1882H/e5CZ86QDxXsZjKAMZ4Gt5N7+hnCI8o05f/4J7mDviBr3Hwj24JVW9vJAamkxE3l1PadU8+/AAB4fHziyaeUfTIPrWSpJAWh+OuPAUF4xLFSiXHuF7+e+slzAJBe2ms+8RgpSVB2in/9W27jT1FLc/qeu4RbSwfIeR7xxETxrf3qoVzjIHfQZl2sX3N1/bMpDeTyCPFRaVkTa9flt+/UrrjcuPnGwu5+/frrzMe+Q9nMv/xq7tlNyDTbtm0xbuo"></p>'
     },
  },
//   {
//     type: 'markdown',
//     content : {
//       text: `### 基础语法

// 1.**标题**

// # 标题 1
// ## 标题 2
// ### 标题 3
// #### 标题 4
// ##### 标题 5
// ###### 标题 6

// 2.**段落和换行**

// 段落之间留空行
// 可以在行尾加两个空格来强制换行。

// 3.**列表**

// - 无序列表

// - 项目 1
// - 项目 2
//   - 子项目 2.1
//   - 子项目 2.2
// - 项目 3

// - 有序列表

// 1. 项目 1
// 2. 项目 2
//    1. 子项目 2.1
//    2. 子项目 2.2
// 3. 项目 3

// 4.**引用**

// > 这是引用的文本。

// 5.**粗体和斜体**

// **粗体文本**
// *斜体文本*

// 6.**链接**

// [链接文本](https://tongyi.aliyun.com/)

// 自动识别连接： https://chatbot.console.aliyun.com/ChatSDK
// 链接截断验证：https://chatbot.console.aliyun.com/ChatSDK?test=测试，结束

// 7.**图片**

// ![替代文本](https://dashscope-cn-beijing.oss-cn-beijing.aliyuncs.com/code-interpreter/temp_files/code-output-20240131-120401.943328.png?OSSAccessKeyId=LTAI5tKHB4j1Cmo6kRtd5Ac8&Expires=1706704441&Signature=WWyDLwits154vO%2FaU3BFtkBc5fg%3D)


// 这是一个图文混排的例子
// ![替代文本](https://gw.alicdn.com/imgextra/i1/O1CN01TinEt01x1vHLqHZGz_!!6000000006384-2-tps-1280-960.png)


// 8.**PDF文件**

// Prompt的类别有哪些？
// ![如何写好Prompt.pdf](https://files.alicdn.com/tpsservice/f6648a7e019575dfa4b708f0c635b4f2.pdf)

// 9.**水平线**

// ---

// 10.**代码**

// - 行内代码

// 这是 \`行内代码\` 的例子。

// - 代码块

// \`\`\`javascript
// function example() {
//   console.log("Hello, World!");
// }
// \`\`\`

// 11.**任务列表**

// - [x] 已完成任务
// - [ ] 未完成任务

// 12.**表情符号**

// :smile: :heart: :rocket:

// 13.**注释**

// <!-- 这是注释 -->

// 14.**定义型列表**

// 苹果
// : 一种水果，有很多种品种。

// 桔子
// : 另一种水果，橙色的。

// 15.**LaTeX 公式**

// $E=mc^2$

// 16.**下标和上标**

// H~2~O，X^2^

// 17.**自动链接**

// <https://www.example.com>

// 18.**内部链接**

// [跳到文档底部](#文档底部)

// 19.**代码注释**

// \`代码注释\`

// 20.**支持HTML**

// <details>
//   <summary>点击展开</summary>
//   这是一个可以展开的内容。
// </details>

// 21.**定义标题 ID**

// ### 标题 {#custom-id}

// 22.**表格**

// | 姓名   | 年龄 | 职业         |
// |--------|------|--------------|
// | 张三   | 25   | 工程师       |
// | 李四   | 30   | 设计师       |

// 23. **at**

// 在Markdown中使用@符号来表示"at"的意思。
// 注意Markdown组件需要透传一个mentionList属性，该属性是一个数组，包含所有需要@的人或团队的名称。

// hello @tongyi-ui

// @OKR设定专家 @3D头像设计师 @🙈齐天大圣 @资深作家

// 24. **识别html链接**

// <a href="tel:0516-96777" target="_self">0516-96777</a>`
//     },
//   },
//   //魔方卡片
//   {
//     type: 'card',
//     content: {
//       url: 'http://lowcode.fe.htsc/app/447e0542ca8f79b1cbbc/editor',
//       data: data
//     },
//   },
//   {
//     type: 'image',
//     content: {
//       picUrl: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg',
//     },
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是一个word文件这是一个word文件.docx",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/外籍股权激励账户开立.docx",
//       // "size": 102400,
//     }
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是第二个word文件.docx",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E5%A4%96%E7%B1%8D%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E8%B4%A6%E6%88%B7%E5%BC%80%E7%AB%8B.docx",
//       "size": 102400,
//     }
//   },

  {
    type: 'file',
    content: {
      "name": "这是一个PDF文件信诚新双盈分级债券型证券投资基金更新招募说明书摘要.pdf",
      "url": "https://aorta.htzq.com.cn/pdf_finchina/FUND/2020/2020-5/2020-05-28/1922768.pdf",
      "size": 102400,
    },
    needFeedback: true,
  },
  {
    type: 'file',
    content: {
      "name": "这是一个PDF文件这是Tailor服务记录.pdf",
      "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E4%BB%80%E4%B9%88%E6%98%AF%E6%9C%8D%E5%8A%A1%E8%AE%B0%E5%BD%95.pdf",
      "size": 102400,
    },
    needFeedback: true,
  },

  {
    type: 'citeweb',
    content: {
     "title": "web-htsc",
      "url": "mastergo.htsc.com.cn" //支持点击跳转
    },
    needFeedback: true,
  },
  {
      type: 'citetext',   
      content: {
      "title": "引用文本",
      "text": "牛首山位于中国江苏省南京市浦口区牛首山位于中国江苏省南京市浦口区牛首山位于中国江苏省南京市浦口区"
    },
    needFeedback: true,
  }
];


export default () => {
  //自定义卡片方法，renderMessageContent传入aichat
  //定义卡片的type及卡片UI,与后端约定卡片消息格式
  // function renderMessageContent(msg: any) {
  //     const { type, content } = msg;
  
  //     // 根据消息类型来渲染
  //     switch (type) {
  //       case 'txt': //自定义卡片类型
  //         return <p>{content?.text}</p>; //自定义卡片UI
  //       default:
  //         return null;
  //     }
  //   }

  // 公共全局配置
  const config = {
    //系统id：表示当前系统标识
    appId: 'web-assistant',

    //用户id：代表当前系统唯一用户id，动态获取
    userId: '002332',

    //场景id
    // sceneId: 'scene',
    
    //可选，用于魔方环境判断，测试环境调试
    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      // rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // 走tcp请求，针对页面搭建里配置的数据源接口
      // requestMobile: 'AortaApp'
      // 走tcp请求，针对getAppById和getSchemaById方法
      // requestHandlerType: 'aortaReq',
      // 系统来源
      host: 'AROTA',
    },

    //接口请求 method默认post
    requests: {

      /**
      * 基础URL，接口请求前缀，拼接在下述方法的url前缀，如业务线的公共前缀，支持传入函数或字符串
      */
      baseUrl: 'aichrome',
      // baseUrl(){
      //   return Math.random().toString()
      // },

      //初始引导接口
      init: {  
        // type: 'http',//请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
        // action:'27006',//移动端如果走tcp，需要传接口请求action号
        // paramsKey：'MS__REQUEST__PAYLOAD'，//移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
        url: '/ai/orchestration/session/createSession',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
        
        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选
        // requestTransfer: (input: object) => {
        //   return new Promise((resolve, reject) => {
        //     try {
        //       const parsedInput = {
        //         ...input,
        //         customerInput1: '123',
        //       }
        //       resolve(parsedInput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        // responseTransfer: (output: object) => {
        //   return new Promise((resolve, reject) => {
        //     const parsedOutput = {
        //       ...output,
        //       customeroutput1: '123',
        //     }
        //     try {
        //       resolve(parsedOutput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        // requestTransfer: (input: object) => { //同步写法
        requestTransfer:async (input: object) => { //支持异步写法
          const parsedInput = {
            ...input,
            customerInput1: '123',
          }
          return parsedInput;
        },
        responseTransfer: (output: object) => {
          const parsedOutput = {
            ...output,
            customeroutput1: '123',
          }
          return parsedOutput;
        },
      },

      //问答接口
      send: {
        url: '/chat/workflow/chrome',
        isAibag: true,
        // url: '/aorta/operation/api/ai/desktop/HiAgentAIChat',
        stream: true,
        messageInterval:50,
        // headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        requestTransfer:async (input: object) => { //支持异步写法
          const parsedInput = {
            ...input,
            // agentId: "translate",
          }
          return parsedInput;
        },
      },

      //查询历史消息详情接口
      history: {
        url: '/ai/orchestration/session/getHistoryMessages',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        // pageSize: 6,//支持分页，默认100，希望一次性尽量展示所有，6表示3个问答对，一个问题1条，一个答案1条: 6,
      },

      //快捷问题接口
      // quickReply: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryQuickReplies',
      //   headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      // },

      //点赞点踩接口
      score: {
        url: '/ai/orchestration/session/feedback',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //停止生成接口
      stop: {
        url: '/ai/orchestration/session/interruptSession',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

      //关联问题接口
      // related: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryRelatedQuestions',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

      // 历史会话列表
      historyConversation: {
        url: '/ai/orchestration/session/getHistorySessions',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      deleteHistoryConversation: {
        url: '/ai/orchestration/session/deleteSession',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // 阅读风险提示
      // riskRead: {
      //   url: '/mcrm/api/groovynoauth/chatBar/updateUserBehaviorByType',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 查询是否阅读了风险提示
      // queryRiskRead: {
      //   url: '/mcrm/api/groovynoauth/chatBar/queryUserBehaviorByType',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 敏感词校验
      // sensitive: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 点踩反馈标签列表
      // feedbackTagList: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryAllAiTag',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 删除消息
      deleteMessage: {
        url: '/ai/orchestration/session/deleteMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },
    },
    // 支持使用时传入指定方法并替换内部行为
    bridge: {
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },
    // 水印，可选，如果传了会话页面背景展示使用信息
    waterMark: {
      show: true,
      text: `张三       002332`,
    },
    //全局logo替换，涉及欢迎页，导航，会话页面
    robot: {
      logo: LogoPng,
      // showMsgLogo:true // 是否在消息答案左侧展示logo头像
    }
  }

  // 导航栏参数
  const navbarProps = {

    // open?: boolean,//是否展示navbar，默认true  

    // 返回按钮设置项
    // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
    // returnButtonIcon?: string;//返回按钮图标路径
    // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

    // 标题区域设置
    showLogo: false,//是否展示logo,默认为true            
    // logo: LogoPng,// logo图标的地址 
    title: 'AI智能助手', //头部标题文案，展示于logo右侧,默认为空字符串
    // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

    // 历史会话按钮设置项
    // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
    // historyButtonIcon?: string,//历史会话按钮图标路径
    // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
    // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

    //新建会话按钮设置项
    // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
    // newButtonIcon?: string,//新建会话按钮图标路径               
    // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

    // 关闭按钮设置项
    // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
    // closeButtonIcon?: string;//关闭按钮图标路径
    // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数 
    // wrapstyle: {
    //   paddingTop: '20px',
    // }
  }

  // 支持自定义欢迎页面
  // const renderWelcome = () => {
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: '100%' }}>这是欢迎页</div>
  //   )
  // }
  // const renderQuickReplies = () => {
  //   return (
  //     <div style={{ marginLeft: 20, marginBottom: 10 }}>11111</div>
  //   )
  // }

  // 埋点方法
  const onReportLog = useCallback((params: any) => {
    console.log(params);
  }, []);

  const RenderHistoryConversationFooter = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的底部自定义模块</div>
  }

  const RenderHistoryConversationBrand = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的品牌区域自定义模块</div>
  }

  return (
    <DemoPage>
      <div style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }} id="chat-container">
        <AiChat
          navbar={navbarProps} //标题栏参数配置
          // renderNavbar={() => null} //支持自定义的navbar
          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          initialMessages={initialMessages} //初始化消息
          // renderMessageContent={renderMessageContent} //自定义卡片渲染
          config={config} // 通用全局配置
          onReportLog={onReportLog} // 自定义埋点方法
          // quickReplies={[{
          //   title: 'string',//页面展示的快捷问题
          //   content: 'realString',//点击后实际发送的问题
          //   url: '',//如果配置，点击后跳转链接
          //   isHighlight: true,
          //   isNew: true,
          //   img: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg'
          // }]}
          messageContainerConfig={{
            showQuote: true,
            showDeleteMessage: true,
          }}

          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推              
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75    
            navbar: {
              // 配置和上面的navbarProps相同，优先在历史会话页生效
              logo: LogoPng,
              showCloseButton: true,
              showNewButton: true,
              wrapstyle: {
                paddingTop: '20px',
              },
            },
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域   
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”
            renderBrand: RenderHistoryConversationBrand, //支持传入自定义brand区域
            renderFooter: RenderHistoryConversationFooter, //支持传入自定义底部区域  
            showSearch: false,
            showDeleteConversation: true, // 是否支持删除会话
            showRename: false, // 是否支持重命名
          }}

          // 欢迎页配置
          welcome={{
            // open: false, //是否展示欢迎页，默认true
            riskTip: "内容由AI大模型生成，请谨慎识别", //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别"
            title: 'Hi～我是 AI助手',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            // showsubTitle: false,
            // logo: '',
            // openMessages 表示欢迎页初始问题，可选
            // openMessages: [{type: 'text', content: '欢迎使用问TA' }, {type:'list', content: '如何做好投资？'}],
            openMessages: [{type: 'list', content: '如何投资ETF？' }, {type:'list', content: '如何做好价值投资？'}, {type:'list', content: '如何看待中美关系？'}],
            // 欢迎页标题配置，配置和上面的navbarProps相同，优先在欢迎页生效
            // navbar: {
              // 配置和上面的navbarProps相同，优先在欢迎页生效
              // title: '欢迎页标题',
              // wrapstyle: {
              //   paddingTop: '20px',
              // },
            // },
            // 支持自定义navbar
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}
          // 是否展示反馈弹窗
          // showFeedbackModal={true}
          // feedbackModalConfig={{
          //   title: 'string',
          //   inputPlaceholder: 'string',
          //   showLabels: true,
          // }}

          
          composerConfig={{
            
            // placeholder:'请输入问题',
            // text:'初始化问题',
            // showInternetSearch: false,
            // aboveNode:(<div >I am aboveNode~</div>),//reactNode
            // belowNode:(<div >I am belowNode~</div>),//reactNode
            uploadConfig: {
              // action: '/webassist/fileService/uploadFile', 
              action: 'http://webassist.sit.saas.htsc/fileService/uploadFile', 
              // action: '/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=11111111&Expire=720h',
              // fileList: fileList,
              // onChange: onChange,
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // fileList: fileList,
              // onChange: onChange,
              headers: {
                // 'X-Content-Sha256': 'c975c2049dfe87d8d3c6002de962426ca96e504c7402abd01d21cb8fedf028f5',
                // 'Content-Type': 'application/json',
                'Iv-User': '002332'
              },
              // beforeUpload,
              // headers: uploadHeaders // 动态headers
              // customRequest,
              // popoverConfig: {
              //   content: popoverContent,
              // },
              // padUrlPrefix: (path: any) => `http://10.102.80.243/llmpf/api/proxy/down?Action=Download&Version=2022-01-01&IsAnonymous=true&Path=${encodeURIComponent(path)}`,
              getDownloadAction: 'http://webassist.sit.saas.htsc/fileService/getDownloadUrl',
              downloadAction: 'http://webassist.sit.saas.htsc/fileService/downloadFile',
            },
            quoteOperations: quoteOperationsMocks,
            // aboveNode: <div>1111</div>,
            skill: skillsMock,
            quickNewConversation: true,
            quickOpenHistory: true,
          }}

          // 是否展示大模型消耗的tokens数量，默认true，如果是hiagent则需要
          showToken={false}
          // 是否在答案结束后展示合规话术‘内容有Ai生产，balabala...’,默认true
          showHallucination={true}
          // composerConfig={{
          //   // 输入框默认值
          //   text: '今日金价'
          // }}
          renderStopAnswer={(msg:any) => {
            console.log('msg', msg);
            return <div>自定义停止生成</div>
          }}
        />
      </div>
    </DemoPage>
  );
};

/**
 * 全量埋点配置
 */

export interface ILogParams {
  id: string;
  page_id: string;
  page_title?: any;
  btn_title?: {
    [key: string]: any;
    useSendBeacon?: boolean;
  };
  btn_id?: string;
}

export const LogPointConfigMap = new Map([
  // HOME对话页
  [
    'handleStopAnswer',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_STOP_ANSWER',
      btn_title: {
        btn_label: '智能助手-停止生成',
      },
    },
  ],
  [
    'handleNewConversation',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_NEW_CONVERSATION',
      btn_title: {
        btn_label: '智能助手-开启新对话',
      },
    },
  ],
  [
    'handleFeedBackGood',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_UP',
      btn_title: {
        btn_label: '智能助手-点赞文本',
      },
    },
  ],
  [
    'handleFeedBackBad',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_DOWN',
      btn_title: {
        btn_label: '智能助手-点踩文本',
      },
    },
  ],
  [
    'handleFeedBackGoodCancel',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_UP',
      btn_title: {
        btn_label: '智能助手-取消点赞',
      },
    },
  ],
  [
    'handleFeedBackBadCancel',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_DOWN',
      btn_title: {
        btn_label: '智能助手-取消点踩',
      },
    },
  ],
  [
    'handleAnswerCopy',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_COPY',
      btn_title: {
        btn_label: '智能助手-复制文本',
        id: '', // 内容id
      },
    },
  ],
  [
    'handleRiskOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_RISK_OPEN',
      btn_title: {
        btn_label: '智能助手-查看风险提示',
      }
    },
  ],
  [
    'handleRiskClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_RISK_CLOSE',
      btn_title: {
        btn_label: '智能助手-关闭风险提示',
      },
    },
  ],
  [
    'handleRiskAgree',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_RISK_AGREE',
      btn_title: {
        btn_label: '智能助手-同意风险提示',
      },
    },
  ],
  [
    'toggleEnableInternetSearchOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_INTERNE_SEARCH_OPEN',
      btn_title: {
        btn_label: '智能助手-联网搜索',
      },
    },
  ],
  [
    'toggleEnableInternetSearchClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_INTERNE_SEARCH_CLOSE',
      btn_title: {
        btn_label: '智能助手-关闭联网搜索',
      },
    },
  ],
  // 历史会话
  [
    'toggleHistoryOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_HISTORY_LIST_PAGE',
      page_title: '智能助手历史会话列表页',
      btn_id: 'AICHAT_HISTORY_LIST_PAGE_OPEN',
      btn_title: {
        btn_label: '智能助手-打开历史会话列表',
      },
    },
  ],
  [
    'toggleHistoryClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_HISTORY_LIST_PAGE',
      page_title: '智能助手历史会话列表页',
      btn_id: 'AICHAT_HISTORY_LIST_PAGE_OPEN',
      btn_title: {
        btn_label: '智能助手-关闭历史会话列表',
      },
    },
  ],
  [
    'selectHistoryConversation',
    {
      id: 'button_click',
      page_id: 'AICHAT_HISTORY_LIST_PAGE',
      page_title: '智能助手历史会话列表页',
      btn_id: 'AICHAT_HISTORY_LIST_PAGE_ITEM_OPEN',
      btn_title: {
        btn_label: '智能助手-查看历史会话',
        id: '', // 会话id
      },
    },
  ],
  [
    'Composer.send',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_COMPOSER_SEND',
      btn_title: {
        btn_label: '输入框-发送',
      },
    },
  ],
  [
    'Composer.upload',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_COMPOSER_UPLOAD',
      btn_title: {
        btn_label: '输入框-上传文件',
      },
    },
  ],
  [
    'handleDeleteMessage',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_DELETE_MESSAGE',
      btn_title: {
        btn_label: '智能助手-删除消息',
      },
    },
  ],
]);
import React, { useState } from 'react';
import { Message, toast } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';

export default () => {
  // 三条消息的受控状态
  const [messages, setMessages] = useState([
    {
      _id: '1',
      messageId: '1',
      type: 'text',
      content: { text: '希望我的回答对您有所帮助！' },
      position: 'left',
      needFeedback: true,
      feedbackResult: undefined,
    },
    {
      _id: '2',
      messageId: '2',
      type: 'text',
      content: { text: '希望我的回答对您有所帮助！' },
      position: 'left',
      needFeedback: true,
      feedbackArray: ['good', 'bad'],
      feedbackResult: undefined,
    },
    {
      _id: '3',
      messageId: '3',
      type: 'text',
      content: { text: '希望我的回答对您有所帮助！' },
      position: 'left',
      feedbackArray: [],
      feedbackResult: undefined,
    },
    {
      _id: '4',
      messageId: '4',
      type: 'text',
      content: { text: '请问怎么删除问题？' },
      position: 'left',
      needFeedback: true,
      showDeleteMessage: true,
    },
    {
      _id: '5',
      messageId: '5',
      type: 'text',
      content: { text: '请问怎么删除回答？' },
      position: 'right',
      feedbackArray: [],
      feedbackResult: undefined,
      showDeleteMessage: true,
    },
  ]);

  // 统一处理点赞/点踩
  const handleFeedBack = (idx: number) => (score: any) => {
    setMessages((prev) => {
      const next = [...prev];
      next[idx] = { ...next[idx], feedbackResult: score };
      return next;
    });
    toast.success(score === 'good' ? '点赞成功' : '点踩成功');
  };

  // 统一处理复制
  const handleCopy = (text: string) => {
    toast.success('复制成功，交互可以自定义', 1000, 'center');
    console.log('复制内容：', text);
  };

  // 处理删除消息
  const handleDeleteMessage = (message: any) => {
    
    console.log('删除消息：', message);
  };

  return (
    <div className="message-demo">
      {messages.map((msg, idx) => (
        <Message
          key={msg._id}
          {...msg}
          onFeedBack={handleFeedBack(idx)}
          copyText={handleCopy}
          config={{}}
          updateMsg={() => {}}
          handleDeleteMessage={handleDeleteMessage}
        />
      ))}
    </div>
  );
}; 
.InputWrapper {
  position: relative;

  &.has-counter {
    padding-bottom: 20px;

    & + .HelpText {
      margin-top: -20px;
    }
  }
}

.Input {
  display: block;
  box-sizing: border-box;
  width: @input-width;
  min-height: @input-min-height;
  margin: @input-margin;
  padding: @input-padding;
  border: @input-border;
  border-radius: 0;
  font-family: @input-font-family;
  font-size: @input-font-size;
  line-height: @input-line-height;
  color: #333;
  background: @input-bg;
  resize: @input-resize;
  -webkit-tap-highlight-color: transparent;
  transition: .2s ease-in-out;
  -webkit-appearance: none;

  &:focus {
    outline: none;
    // border-color: @brand-1;
  }
  &:focus:not([disabled]):not([readonly]) {
    & ~ .Input-line {
      &:before,
      &:after {
        width: 50%;
      }
    }
  }
  &::placeholder {
    color: @gray-4;
  }
  &--filled {
    border-color: transparent;
    background-color: @gray-6;
  }
  &--flushed {
    padding: 2px @gutter;
    border-width: 0 0 1px;
    border-radius: 0;
    background: none;

    &:focus {
      box-shadow: @brand-1 0px 1px 0px 0px;
    }
  }
}

.Input-counter {
  // absolute 在iOS下有坑，在消息列表里的输入框，输入文字后，页面卡片下移
  position: relative;
  z-index: 1;
  float: right;
  margin-right: @gutter;
  color: @gray-3;
  font-size: @font-size-xs;
}

/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-02 13:58:01
 */
import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button } from '../../../src';
import { useCreateConversation } from '../../../src/hooks/aiAgentChat/index';

export default () => {
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useCreateConversation({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '002332',
    },
  });

  const { data: conversationData2, loading: conversationLoading2, run: conversationRun2 } = useCreateConversation({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      manual: true,
      type: 'http',
    },
    requestParams: {
      userId: '016645',
    },
  });

  return (
    <DemoPage>
      <DemoSection title="自定义服务">
        <Button onClick={() => conversationRun()} loading={conversationLoading}>创建会话</Button>
        <div>
          <span>{conversationData?.resultData?.conversationId}</span>
        </div>
      </DemoSection>

      <DemoSection title="HiAgent">
        <Button onClick={() => conversationRun2()} loading={conversationLoading2}>创建会话</Button>
      </DemoSection>
      <div>
        <span>{conversationData2?.resultData?.conversationId}</span>
      </div>
    </DemoPage>
  )
};

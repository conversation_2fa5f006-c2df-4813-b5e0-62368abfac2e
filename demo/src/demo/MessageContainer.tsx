import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { MessageContainer } from '../../../src';

export default () => {
  const msgRef = React.useRef(null);

  return(
    <DemoPage>
    <DemoSection title="文本气泡">
      <MessageContainer
        ref={msgRef}
        messages={[{
          _id: 1,
          messageId: 1,
          type: 'text',
          content: { text: '你好' },
          position: 'left',
        }, {
          _id: 2,
          messageId: 2,
          type: 'text',
          content: { text: 'Hello' },
          position: 'right',
        }]}
        showBackBottom={true}
        config={{}}
        updateMsg={() => {}}
      />
    </DemoSection>
  </DemoPage>
  );
};

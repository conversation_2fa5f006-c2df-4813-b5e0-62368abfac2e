/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, List, ListItem, useGetConversationList } from '@ht/chatui';

export default () => {

  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useGetConversationList({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      appId: 'aorta',
      userId: '002332',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>获取会话列表</Button>
      <List bordered>
        {conversationData?.resultData?.list?.map((item: any, index: number) => (
          <ListItem key={index} content={`${item.conversationId}: ${item.question}`} />
        ))}
      </List>
    </div>
    )
}
import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { Navbar } from '../../../src';

export default () => {
  return (
    <DemoPage>
       <DemoSection title="聊TA移动端效果">
        <Navbar  />
      </DemoSection>  
      <DemoSection title="聊TA PC端效果">
        <Navbar  showReturnButton={false} logoAndTitlePosition='left' title='问TA' showCloseButton={true}/>
      </DemoSection>
      <DemoSection title="投行云移动端效果">
        <Navbar historyButtonPosition='left' showReturnButton={false} title='IBanker投行助理' logo="//alime-base.oss-cn-beijing.aliyuncs.com/avatar/alime-base.oss-cn-beijing-internal.aliyuncs.com1636689421751-小蜜头像.png"/>
      </DemoSection>
      {/* <DemoSection title="默认参数">
        <Navbar  />
      </DemoSection>
      <DemoSection title="全部显示效果">
        <Navbar title="客服小蜜" showReturnButton={true} showCloseButton={true} />
      </DemoSection>
      <DemoSection title="聊TA移动端">
        <Navbar showLogo={false}/>
      </DemoSection>
      <DemoSection title="自定义logo">
        <Navbar
          logo="//alime-base.oss-cn-beijing.aliyuncs.com/avatar/alime-base.oss-cn-beijing-internal.aliyuncs.com1636689421751-小蜜头像.png"
          showNewButton={false}
        />
      </DemoSection>
      <DemoSection title="只显示标题">
        <Navbar showLogo={false} showReturnButton={false} showHistoryButton={false} showNewButton={false} title='我是标题'/>
      </DemoSection> */}

    </DemoPage>
  );
};

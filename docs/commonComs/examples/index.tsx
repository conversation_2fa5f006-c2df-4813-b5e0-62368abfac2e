
import React from 'react';
import ArrRightIcon from '../../../public/home/<USER>/arr_right.svg';
import ArrLeftIcon from '../../../public/home/<USER>/arr_left.svg';

import './index.css';

const Features = () => {
  return (
    <div className="dumi-site-examples">

      <div className='line-wrapper'>
        <div className='title-wrapper'>
          <img src="/home/<USER>" alt="聊TA"  className='title-icon'/>
          <div className='title-text'>聊TA 智能投顾助手 PC端</div>        
        </div>
        <div className='desc'>在投资顾问（投顾）业务中，高效获取市场信息、快速响应客户需求是关键。为提升投顾人员工作效率推出Ai对话助手功能，通过侧边悬浮窗无缝接入业务管理端，提供实时智能支持，辅助完成客户服务、数据分析、合规查询等日常工作。  </div>
        <div className='img-wrapper'>
          <img className='img-pc' src="/home/<USER>" alt="聊TA欢迎页"/>
          <div className='img-desc'><img className="icon" src={ArrLeftIcon} />{`自定义欢迎页`}</div>
        </div>   
        <div className='img-wrapper'>
          <div className='img-desc'>{`会话页`}<img className="icon" src={ArrRightIcon} /></div>
          <img className='img-pc' src="/home/<USER>" alt="聊TA会话页"/>   
        </div>    
      </div>

      <div className='line-wrapper'>
        <div className='title-wrapper'>
          <img src="/home/<USER>" alt="快速创建"  className='title-icon'/>
          <div className='title-text'>聊TA 智能投顾助手 APP端</div>        
        </div>
        <div className='desc'>移动端提供更轻量化的服务，更紧凑的布局，更顺滑的手势，以及虚拟键盘输入等移动端特性适配方案。 </div>
        <div className='img-wrapper' style={{ alignItems: 'start'}}>
          <div style={{ marginTop: '100px' }}>
            <div className='img-desc'>{`业务领域知识库`}</div>
            <img style={{ display: 'block', marginTop: '16px' }} className='img-app' src="/home/<USER>" alt="聊TA会话页"/>   
          </div>
          <div>
            <img style={{ display: 'block', marginBottom: '16px' }} className='img-app' src="/home/<USER>" alt="聊TA历史会话页"/>
            <div className='img-desc'>{`历史会话抽屉`}</div>   
          </div>
          <div style={{ marginTop: '100px' }}>
            <div className='img-desc'>{`下拉上滑手势操作`}</div>
            <img style={{ display: 'block', marginTop: '16px' }} className='img-app' src="/home/<USER>" alt="聊TA会话页"/>   
          </div>
        </div>  
      </div>
      <div className='line-wrapper'>
        <div className='title-wrapper'>
          <img src="/home/<USER>" alt="投行云"  className='title-icon'/>
          <div className='title-text'>投行云 iBanker投行助手 PC端宽屏模式</div>        
        </div>
        <div className='desc'>iBanker投行助理旨在打造投行自己的大模型智能助理，以简富/移动端为渠道，为投行人员提供内网私有化部署的大模型底层能力，提供安全的问答助理、撰写助理等。  </div>
        <div className='img-wrapper'>
          <img style={{ height: 'auto' }} className='img-pc' src="/home/<USER>" alt="投行云PC端宽屏"/>
          <div className='img-desc'><img className="icon" src={ArrLeftIcon} />{`宽屏对话界面`}</div>
        </div>     
      </div> 
    </div>
  );
};

export default Features;
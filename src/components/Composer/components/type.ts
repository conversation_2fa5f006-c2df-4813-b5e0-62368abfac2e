import { SelectProps } from "@ht/sprite-ui";
import type { UploadFile } from "../wide/Upload"
// 输入框类型
export type ComposerType = 'common' | 'web' | 'translate' | 'text';

// 输入框用户可定制属性
export type ComposerCustomProps = {
  icon?: React.ReactNode; // 输入框标题栏图标
  title: string; // 输入框标题
  agentId?: string; // 智能体
};

// 输入框属性
export interface ComposerProps extends ComposerCustomProps {
  onclose: () => void; // 右上角关闭回调
  onSend: (otherOperations?: object) => void; // 发送
  text: string; // 输入框文本
  setText: (text: string) => void; // 设置文本
  inputCardList?: InputCard[];
  setInputCardList?: (list: InputCard[]) => void;
}

// 快捷技能 定义
export type Skill = {
  key: string; //
  disabled?: boolean; // 是否禁用
  icon?: React.ReactNode; // 图标
  label: string; // 文案
  question?: string; // 发送时的命令
  agentId?: string; // 发送时可指定智能体ID
  children?: Skill[]; // 折叠子项,有子项时，点击会展开子项
  expandIcon?: React.ReactNode; // 折叠图标
  customRender?: React.ReactNode; // 自定义渲染
  onClick?: (skill: Skill) => void; // 技能点击回调
  translateOptions?: {        
    src_lang: {
      defaultValue?: string,
      options: SelectProps['options'],
    },
    tgt_lang: {
      defaultValue?: string,
      options: SelectProps['options'],
    },
  },
  [key: string]: any; // 技能扩展字段
};

// 快捷操作 定义
export type Operation = {
  disabled?: boolean; // 是否禁用
  icon?: React.ReactNode; // 图标
  label: string; // 文案
  question: string; // 发送时的命令
  agentId?: string; // 发送时可指定智能体ID
  customRender?: React.ReactNode; // 自定义渲染
  onClick?: (operation: Operation) => void; // 操作点击回调
};

export type InputType = 'text' | 'web' | 'image' | 'file';
export type InputContent = InputText | InputWeb | InputImage | InputFile;
export type InputText = string;
export type InputWeb = {
  title: string;
  url: string;
  favicon?: string;
  content?: string; //网页dom信息
};
export type InputImage = {
  path: string;
  name: string;
  size: number;
  url: string;
};

export interface InputFile extends UploadFile {
  icon: string;
  // name: string;
  // size: string;
  // url: string;
};

// 输入卡片定义
export type InputCard = {
  type: InputType; // 类型
  isCite: boolean; // 是否是引用
  content: InputContent; // 内容
};

export type CiteInfoProps = {
  // 引用类型
  type: InputType;
  // 引用内容
  content: InputContent
};

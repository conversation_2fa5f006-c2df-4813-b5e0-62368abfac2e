@keyframes slideInRight {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right-item {
  animation: slideInRight 0.5s ease-in-out both;
}

each(range(2, 11), {
  .slide-in-right-item:nth-child(@{value}) {
    animation-delay: ((@value - 1) * 0.1s);
  }
});


@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.A-fadeIn {
  animation: 0.6s fadeIn;
}

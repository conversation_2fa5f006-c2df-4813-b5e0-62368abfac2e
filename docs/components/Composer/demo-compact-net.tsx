import React, { useState } from 'react';
import { Composer } from '@ht/chatui';

export default () => {
  const [enableInternetSearch, setEnableInternetSearch] = useState(false);

  const toggleEnableInternetSearch = () => {
    setEnableInternetSearch(!enableInternetSearch);
  };

  const onSend = async (type: string, content: string) => {
    return new Promise<boolean>((resolve) => {
      console.log('type', type, 'content', content);
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
  };

  return (
    <Composer
      text="ceshi"
      inputType="text"
      onSend={onSend}
      showInternetSearch={true}
      showLLMSwitch={true}
      enableInternetSearch={enableInternetSearch}
      toggleEnableInternetSearch={toggleEnableInternetSearch}
    ></Composer>
  );
};

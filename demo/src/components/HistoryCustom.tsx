import React from 'react';
import { But<PERSON> } from '@ht/ioned';
import type { HistoryConversationOptions, ChatContextProps } from '../../../src';


export default function HistoryCustom (props: HistoryConversationOptions) {
    const { chatContext } = props;
    console.log('propsHistoryCustom', props);
    const { selectHistoryConversation, handleNewConversation, handleStopAnswer } = (chatContext || {}) as ChatContextProps;
    return <div>
        <Button onClick={() => handleNewConversation()}>创建会话</Button>
        <Button onClick={() => selectHistoryConversation('cvhltd8ldcepcd7dcs00')}>切换会话</Button>
        <Button onClick={() => handleStopAnswer()}>停止发送</Button>
    </div>;
}
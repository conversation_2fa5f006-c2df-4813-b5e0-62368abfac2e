.translateComposer {
  // width: 536px;
  // height: 200px;
  max-width: 100%;
  max-height: 200px;
  min-height: 118px;
  border-radius: 12px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 0;

  /* 中性色/0%-FFFFFF */
  background: #ffffff;
  box-sizing: border-box;

  /* 中性色/15%-DDDEE0 */
  border: 1px solid #dddee0;

  /* 输入框投影 */
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 6%);

  .header {
    width: 100%;
    height: 48px;
    border-radius: 12px 12px 0 0;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    align-self: stretch;

    /* 中性色/4%-F6F6F7 */
    background: #f6f6f7;
    box-sizing: border-box;

    /* 中性色/15%-DDDEE0 */
    border-width: 1px 1px 0;
    border-style: solid;
    border-color: #dddee0;

    .title {
      display: flex;
      flex-direction: row;
      gap: 8px;

      span {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
      }
    }

    .handle {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;

      .close {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
    }
  }
  .content {
    width: 100%;
    height: 100%;
    background-color: #ffffff;

    .language {
      width: 100%;
      height: 44px;
      padding-top: 12px;
      padding-left: 12px;
      display: flex;
      gap: 8px;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }

    textarea {
      border: none;
      resize: none;
    }
  }

  .footer {
    width: 100%;
    height: 44px;
    flex-shrink: 0;
    padding-right: 12px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
}

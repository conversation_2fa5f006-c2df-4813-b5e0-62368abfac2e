export const quoteOperationsMocks = {
  text: [
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '翻译',// 文案
      question: '翻译翻译',// 发送时的命令 --》 拼装
      agentId: 'translate',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '总结',// 文案
      question: '总结',// 发送时的命令 --》 拼装
      agentId: 'summary',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '扩写',// 文案
      question: '扩写',// 发送时的命令 --》 拼装
      agentId: 'text-expander',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '缩写',// 文案
      question: '缩写',// 发送时的命令 --》 拼装
      agentId: 'text-condenser',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '润色',// 文案
      question: '润色',// 发送时的命令 --》 拼装
      agentId: 'text-polisher',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '修正拼写与语法',// 文案
      question: '修正拼写与语法',// 发送时的命令 --》 拼装
      agentId: 'grammar-corrector',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }
  ],
  web: [
    {
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '总结',// 文案
    question: '总结',// 发送时的命令 --》 拼装
    agentId: 'summary',//发送时可指定智能体ID --》拼装
    customRender: '',//自定义渲染
    },
    {
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '澄清关键概念',// 文案
    question: '澄清关键概念',// 发送时的命令 --》 拼装
    agentId: 'default',//发送时可指定智能体ID --》拼装
    customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '澄清关键概念',// 文案
      question: '澄清关键概念',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '识别假设',// 文案
      question: '识别假设',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '分析对比',// 文案
      question: '分析对比',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '了解作者',// 文案
      question: '了解作者',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '查找相关新闻',// 文案
      question: '查找相关新闻',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }
  ],
  image: [
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '图片解释',// 文案
      question: '图片解释',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '图片文字提取',// 文案
      question: '图片文字提取',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }
  ],
  file: [
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '总结文档内容',// 文案
      question: '总结文档内容',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '生成简短摘要',// 文案
      question: '生成简短摘要',// 发送时的命令 --》 拼装
      agentId: 'default',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }
  ],
};
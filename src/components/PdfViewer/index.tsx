import React, { useRef } from 'react';
import CloseIcon from './images/close.svg';

export interface PdfViewerProps {
  fileUrl: string;
  handleClose: () => void;
}

export const PdfViewer: React.FC<PdfViewerProps> = (props) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const renderNoData = () => {
    return (
      <div className='noDatabox'>
        暂无数据
      </div>
    );
  }


  const {
    fileUrl: pdfUrl,
    handleClose
  } = props;
  const isShowNoData = !pdfUrl;
  // const appName = 'chatui';
  // const viewer = 'https://aorta.htzq.com.cn/mobMain/static/public/pdf/web/viewer.html'
  // TODO 生成环境切换为生产域名：http://aorta.htzq.com.cn
  const viewer = 'https://aorta.htzq.com.cn/file-preview/index.html#/filePreview';
  
  return (
    <div className="PDFWrap">
      <div className='closeWrap'>
        <img src={CloseIcon} className="PDFWrap-close" onClick={() => handleClose()} />
      </div>

      {!isShowNoData && (
        <iframe
          ref={iframeRef}
          id="previewPdf"
          src={`${viewer}?file=${encodeURIComponent(pdfUrl)}`}
          className='iframe'
        />
      )}
      {isShowNoData && renderNoData()}
    </div>
  );

}

@import '~@ht/chatui/es/styles/index.less';

@history-conversation-bg: #fff;
@chat-bg: #fff;

.CustomWelcome {
  padding: 24px;
  > .title {
    height: 25px;
    font-size: 18px;
    font-weight: 500;
    color: #000000;
    line-height: 25px;
    margin-bottom: 10px;
  }
  > .card {
    height: 112px;
    background: rgba(217, 217, 217, 20%);
    border-radius: 2px;
    padding: 8px 10px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    > .header {
      margin-bottom: 16px;
      height: 20px;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      > .title {
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #25396f;
        line-height: 20px;
      }

      > .extra {
        span {
          height: 20px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007aff;
          line-height: 20px;
        }
      }
    }
    > .content {
      > .list {
        > .item {
          height: 30px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #007aff;
          line-height: 30px;
        }
      }
    }
  }
}

.wide-screen-demo {
  .conversationWrap {
    > .MobileNavBar {
      box-shadow: none;
      margin-bottom: 16px;
      > .titleLeft {
        flex-basis: 0px;
        flex-grow: 1;
        flex-shrink: 1;
        width: calc(100% - 40px);
      }
      > .newButtonIcon {
        display: inline-block;
        flex-basis: 28px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }

  .quickNav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    > .new_session_btn {
      height: 24px;
      border-radius: 2px;
      border: 1px solid #007aff;
      width: 100%;
      > .new_session_btn_wrap {
        display: flex;
        align-items: center;

        > span {
          margin-left: 8px;
        }
      }
    }
  }

  .searchBarWrap {
    > .searchHeader {
        padding: 0;
      > .searchBox {
        margin-left: 0px;
        padding: 0px;
        height: 24px;
        border-radius: 2px;
        border: 1px solid #dee8f5;
        box-shadow: none;
        background-color: #fff;

        input {
            font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #7C8DB5;
line-height: 17px;
        }
      }
    }
  }

  .contentWrap {
    padding: 0;
    
  }
}

---
nav:
  title: 指南
  order: -1
group:
  title: 
  order: 1
order: 7
---

# 快速开始

## 示例1：接入窄屏AiChat组件，对接标准接口协议
<p>后端接口单独实现，出入参按标准协议实现</p>
```
yarn add @ht/chatui
```

```
import React from 'react';
import AiChat from '@ht/chatui';

import '@ht/chatui/dist/index.css';
import styles from './index.less';
import LCRender from '@lowcode/lc-render';

//默认按需引入，如果涉及自定义卡片才需要
//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;

function Chat() {
// 公共全局配置
  const config = {
    //系统id：表示当前系统标识,各个系统自定义
    appId: 'system',

    //用户id：代表当前系统唯一用户id，动态获取
    userId: '002332',

    //可选，用于魔方环境判断，测试环境调试
    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      // host: 'AROTA',
    },

    //本示例以标准接口协议为例子
    //接口请求参数， method默认post，
    requests: {

      /**
      * 基础URL，接口请求前缀，拼接在下述方法的url前缀，如聊他业务线的公共前缀'fspa'
      */
      baseUrl: '',

      //初始引导接口参数配置,其他接口可选参数参考init
      init: {
        // type: 'http',//可选，下同，请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
        // action:'27006',//可选，，下同，移动端如果走tcp，需要传接口请求action号
        // paramsKey：'MS__REQUEST__PAYLOAD'，//可选，，下同，移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选，下同
        //可选，下同，请求入参转换回调函数，支持业务线入参转换
        requestTransfer: (input: object) => {
          const parsedInput = {
            ...input,
            customerInput1: '123',
          }
          return parsedInput;
        },
        //可选，请求出参转换回调函数，支持业务线出参转换
        responseTransfer: (output: object) => {
          const parsedOutput = {
            ...output,
            customeroutput1: '123',
          }
          return parsedOutput;
        },
      },

      //问答接口
      send: {
        url: '/aorta/operation/api/ai/desktop/HiAgentAIChat',
        stream: true,//表示流式输出
        messageInterval:50,//可选，控制数据帧在页面显示的间隔，单位毫秒，默认30ms
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //查询历史消息详情接口
      history: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        // pageSize: 6,//支持分页，默认100，希望一次性尽量展示所有，6表示3个问答对，一个问题1条，一个答案1条
      },

      //点赞点踩接口
      score: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //停止生成接口，可选
      // stop: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

      // 历史会话列表
      historyConversation: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // // 敏感词校验,可选
      // sensitive: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // // 点踩反馈标签列表，不传则不展示点踩原因输入弹框，可选
      // feedbackTagList: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryAllAiTag',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

    },
    // 支持使用时传入指定方法并替换内部行为
    bridge: {
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },

    // 水印，可选，如果传了会话页面背景展示使用信息
    // waterMark: {
    //   show: true,
    //   text: `张三       002332`,
    // },
 
    //全局logo替换，涉及欢迎页，导航，会话页面
    // robot: {
    //   logo: LogoPng,
    //   showMsgLogo:false // 是否在消息答案左侧展示logo头像,默认false
    // }
  }

  // 导航栏参数,导航栏默认在pc和app有一套自己的样式，可以通过下述参数进行自定义
  const navbarProps = {
    // open?: boolean,//是否展示navbar，默认true

    // 返回按钮设置项
    // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
    // returnButtonIcon?: string;//返回按钮图标路径
    // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

    // 标题区域设置
    showLogo: false,//是否展示logo,默认为true            
    // logo: LogoPng,// logo图标的地址 
    title: 'AI智能助手', //头部标题文案，展示于logo右侧,默认为空字符串
    // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

    // 历史会话按钮设置项
    // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
    // historyButtonIcon?: string,//历史会话按钮图标路径
    // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
    // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

    // 新建会话按钮设置项
    // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
    // newButtonIcon?: string,//新建会话按钮图标路径
    // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

    // 关闭按钮设置项
    // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
    // closeButtonIcon?: string;//关闭按钮图标路径
    // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数
    // wrapstyle: {
    //   paddingTop: '20px',
    // }
    // logo: LogoPng,
  }

  // 支持自定义欢迎页面
  // const renderWelcome = () => {
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: '100%' }}>这是欢迎页</div>
  //   )
  // }
  // const renderQuickReplies = () => {
  //   return (
  //     <div style={{ marginLeft: 20, marginBottom: 10 }}>11111</div>
  //   )
  // }
  
  // 埋点方法
  const onReportLog = useCallback((params: any) => {
    const { id, btn_title } = params;
    const { btn_label, ...res } = btn_title;
    logCommon({
      type: id === 'button_click' ? 'Click' : '',
      payload: {
        name: btn_label,
        ...res,
      },
    });
  }, []);

  const handleReturnButtonClick = useCallback(() => {
    logCommon({
      type: 'Click',
      payload: {
        name: '智能助手-关闭助手',
      },
    });
    // closePage();
  }, []);

  return (
    <div className={styles.AiChatWrapper}>
      <AiChat
        navbar={navbarProps} //标题栏参数配置
          // renderNavbar={() => null} //支持自定义的navbar
          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={initialMessages} //初始化消息
          config={config}
          onReportLog={onReportLog}
          // quickReplies={[{
          //   title: 'string',//页面展示的快捷问题
          //   content: 'realString',//点击后实际发送的问题
          //   url: '',//如果配置，点击后跳转链接
          //   isHighlight: true,
          //   isNew: true,
          //   img: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg'
          // }]}

          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75
            title: '历史会话列表', //历史会话列表标题
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”
            // renderBrand: RenderHistoryConversationBrand, //支持传入自定义brand区域
            // renderFooter: RenderHistoryConversationFooter, //支持传入自定义底部区域
            showSearch: false,
          }}

          // 欢迎页配置
          welcome={{
            // open?: boolean //是否展示欢迎页，默认true
            riskTip: "内容由AI大模型生成，请谨慎识别", //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别"
            title: 'Hi～我是 AI助手',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            // showsubTitle: false,
            // logo: '',
            // openMessages 表示欢迎页初始问题，可选
            // openMessages: [{type: 'text', content: '欢迎使用问TA' }, {type:'list', content: '如何做好投资？'}],
            openMessages: [{type: 'list', content: '如何投资ETF？' }, {type:'list', content: '如何开通两融账户？'}],
            // 欢迎页标题配置，配置和上面的navbarProps相同，优先在欢迎页生效
            // navbar: {
              // 配置和上面的navbarProps相同，优先在欢迎页生效
              // title: '欢迎页标题',
              // wrapstyle: {
              //   paddingTop: '20px',
              // },
            // },
            // 支持自定义navbar
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}

          // 是否展示反馈弹窗
          // showFeedbackModal={true}
          // feedbackModalConfig={{
          //   title: 'string',
          //   inputPlaceholder: 'string',
          //   showLabels: true,
          // }}
          // 是否展示大模型消耗的tokens数量，默认true，如果是对接hiagent，则一般需要
          showToken={false}
          // 是否在答案结束后展示合规话术‘内容有Ai生产，balabala...’,默认true
          showHallucination={true}
      />
    </div>
  );
}

```

## 示例2：接入宽屏WideAiChat组件，对接泰为hiagent智能体接口协议

```
后端接口是在泰为平台创建的智能体发布成的接口，后端不需要额外封装，主要区别在于config中requests的参数设置

import React from 'react';
import {WideAiChat，AiChatHandle} from '@ht/chatui';

import '@ht/chatui/dist/index.css';
import styles from './index.less';
import LCRender from '@lowcode/lc-render';

//默认按需引入，如果涉及自定义卡片才需要
//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;

function Chat() {
// 消息列表
  // const msgRef = React.useRef(null);
  const ref = React.useRef<AiChatHandle>(null);

  const config = {
    //租户id：表示当前系统
    appId: 'xSystem',

    //用户id：代表当前系统唯一用户id
    userId: '002332',

    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },
    onFeedback: (...params) => {
      console.log('点赞接口', params);
    },
    //接口请求 method默认post
    requests: {
      /**
       * 基础URL
       */
      baseUrl: '/hiAgent',
      // 如果对接泰为智能体接口，需要设置platform='hiAgent'
      platform: 'hiAgent',
      // 对应泰为智能体的appkey，联系泰为平台获取
      appKey: 'd0e4lcgim975aeu7usa0',

      // //初始引导接口
      // init: {
      //   url: '',
      //   headers: { empid: '002332', token: 'token', deviceId: 'deviceId' },
      //   // requestTransfer: (input: object) => {
      //   //   return new Promise((resolve, reject) => {
      //   //     try {
      //   //       const parsedInput = {
      //   //         ...input,
      //   //         customerInput1: '123',
      //   //       };
      //   //       resolve(parsedInput);
      //   //     } catch (error) {
      //   //       reject(error);
      //   //     }
      //   //   });
      //   // },
      //   // responseTransfer: (output: object) => {
      //   //   return new Promise((resolve, reject) => {
      //   //     const parsedOutput = {
      //   //       ...output,
      //   //       customeroutput1: '123',
      //   //     };
      //   //     try {
      //   //       resolve(parsedOutput);
      //   //     } catch (error) {
      //   //       reject(error);
      //   //     }
      //   //   });
      //   // },
      // },

      //问答接口
      send: {
        url: '/chat_query',
        stream: true,
        // messageInterval:30, //前端展示数据帧间隔,默认30
      },

      //查询历史详情接口
      history: {
        url: '/get_conversation_messages',
        pageSize: 100,
      },
      //点赞点踩接口
      score: {
        url: '/feedback',
      },

      //停止生成接口
      stop: {
        url: '/stop_message',
      },

      // 历史会话列表
      historyConversation: {
        url: '/get_conversation_list',
      },
     
    },
    bridge: {
      // 原生bridge
      // 打开新页面
      openWebPage: (url: any) => {
        console.log('打开新页面', url);
      },
      // 调用原生方法打开页面预览文件支持word\pdf
      openFileViews: (url: any) => {
        console.log('预览文件', url);
      },
    },
    
    robot: {
      logo: 'http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/ailogo.9262eff2.svg',
      showMsgLogo:true // 是否在消息答案左侧展示logo头像,默认false
    },
  //泰为智能体的前置输入变量，可选，如果涉及需要获取后传入
    // VariableConfigs:
    //   [
    //     {
    //       Key: "key",
    //       Name: "命中",
    //       Required: true,
    //       VariableType: "Enum",
    //       EnumValues: [
    //         "枚举1",
    //         "枚举2",
    //         "最大可以10"
    //       ],
    //       TextMaxLength: 32
    //     },
    //     {
    //       Key: "productCodes",
    //       Name: "productCodes",
    //       Required: true,
    //       VariableType: "Text",
    //       TextMaxLength: 32
    //     }, {
    //       Key: "code",
    //       Name: "文本枚举值",
    //       Required: true,
    //       VariableType: "Paragraph"
    //     }
    //   ]
  };

  const renderFooterVersion = () => {
    return  <div className="ChatFooter-Version">内容由 AI 生成，无法确保信息的真实准确，仅供参考</div>;
  } 


  const renderBrand = () => {
    const handleNewConversation = () => {
      ref.current?.chatContext?.handleNewConversation?.();
      console.log('创建新会话');
    }
    return <div className="quickNav">
      <Button
        onClick={handleNewConversation}
        className="new_session_btn"
      >
        <div className="new_session_btn_wrap">
          <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/addchat.09d0d5dd.svg"></img>
          <span>开始新会话</span>
        </div>
      </Button>
    </div>;
  };

  const renderFooter = () => {
    return <div
      className="feedback"
      onClick={() => {
        message.info('反馈')
      }}
    >
      <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/feedback.e05ace4f.svg"></img> <span>问题反馈</span>
    </div>;
  }

  const renderTitle = () => {
    return '标题';
  }

  // const [uploadHeaders, setUploadHeaders] = useState({});
  // const [loading, setLoading] = useState(false);
  // const [progress, setProgress] = useState(0);

  // // 计算文件SHA-256哈希
  // const calculateFileHash = async (file: any) => {
  //   try {
  //     const buffer = await file.arrayBuffer();
  //     const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  //     const hashArray = Array.from(new Uint8Array(hashBuffer));
  //     return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  //   } catch (error) {
  //     console.error('计算哈希出错:', error);
  //     throw new Error('文件哈希计算失败');
  //   }
  // };

  // beforeUpload处理函数
  // const beforeUpload = async (file: any) => {
  //   try {
  //     // 计算文件哈希
  //     const fileHash = await calculateFileHash(file);

  //     // 设置上传headers
  //     setUploadHeaders({
  //       'X-Content-Sha256': fileHash,
  //       // 可以添加其他headers
  //       // 'Authorization': 'Bearer token123'
  //       'Content-Type': 'application/json',
  //     });

  //     // 返回true继续上传
  //     return true;
  //   } catch (error: any) {
  //     message.error(error.message);
  //     return false; // 阻止上传
  //   }
  // };

  // 计算 SHA-256 哈希
  const calculateHash = async (file: any): Promise<string> => {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  };


  // 自定义上传实现（使用 XMLHttpRequest）
  const customRequest = async ({ file, onSuccess, onError, onProgress }: ComposerCustomRequestOptions) => {
    // setLoading(true);
    // setProgress(0);

    try {
      // 计算文件哈希
      const fileHash = await calculateHash(file);

     // 创建 XMLHttpRequest
      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          // setProgress(percent);
          onProgress({ percent }, file);
        }
      };

      // 处理完成事件
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch {
            response = xhr.responseText;
          }
          onSuccess(response, file);
        } else {
          onError(new Error(`上传失败: ${xhr.statusText}`), file);
        }
        // setLoading(false);
      };

      xhr.onerror = () => {
        onError(new Error('上传过程中发生错误'), file);
        // setLoading(false);
      };

      xhr.ontimeout = () => {
        onError(new Error('请求超时'), file);
        // setLoading(false);
      };

      // 5. 打开连接
      xhr.open('POST', `/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=${fileHash}&Expire=720h`, true);

      // 设置请求头
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.setRequestHeader('X-Content-Sha256', fileHash);

      // 6. 发送请求
      xhr.send(file);

    } catch (error) {
      // setLoading(false);
      onError(error as Error, file);
    }
  };

  const popoverContent = (
    <div>
      <div>1. 最多支持10个大小不超过10MiB 的文档/图片/音频</div>
      <div>2. 上传文件仅支持以下格式：pdf, doc</div>
    </div>
  );

  return (
      <div
        className="wide-screen-demo"
        style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }}
        id="chat-container"
      >
        <WideAiChat
          ref={ref}
          navbar={{
            onClose: () => {
              console.log('这里写关闭当前页面的方法');
            },
          }}

          renderFooterVersion={renderFooterVersion}

          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          initialMessages={[]}
          config={config}
          renderNavbar={() => null}
          renderQuickReplies={() => null}
          showPushHistory={false} // 不需要在AiChat内推出历史会话面板了
          // renderFooterVersion={() => null}

          // renderComposer={WideComposer}
          // renderWelcome={CustomWelcome}
          welcome={{
            title: '基础平台技术文档助理',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            showsubTitle: false,
            riskTip: ' ',
            // showRiskTip: false,
            openMessages: [
              { type: 'text', content: '您好！我是基础平台技术文档助理，专注于数科基础平台领域的技术文档支持，可以协助您进行技术文档的编写与修订，以及技术方案的评审。您可以上传PDF格式的文档进行交流。' },
              { type: 'list', content: '如何撰写一份高质量的技术文档？' },
              { type: 'list', content: '在技术方案评审中需要注意那些关键点？' },
              { type: 'list', content: '能否提供一些基础平台技术文档的模板？' },
            ],
            // navbar: {
            // 配置和上面的navbarProps相同，优先在欢迎页生效
            // title: '欢迎页标题',
            // wrapstyle: {
            //   paddingTop: '20px',
            // },
            // },
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}

          historyConversation={{
            showLogo: true,
            title: '智能助理智能助理智能助理智能助理智能助理',
            showCloseButton: false,
            showSearch: false,
            // renderBrand,
            // renderFooter,
            // renderTitle,
          } as any}
          composerConfig={{
            showInternetSearch: false,
            uploadConfig: {
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // action: '/llmpf/api/proxy/up?Action=UploadRaw&Version=2022-01-01&Id=11111111&Expire=720h',
              // fileList: fileList,
              // onChange: onChange,
              // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
              // fileList: fileList,
              // onChange: onChange,
              // headers: {
              //   'X-Content-Sha256': 'c975c2049dfe87d8d3c6002de962426ca96e504c7402abd01d21cb8fedf028f5',
              //   'Content-Type': 'application/json',
              // },
              // beforeUpload,
              // headers: uploadHeaders // 动态headers
              customRequest,
              popoverConfig: {
                content: popoverContent,
              },
              padUrlPrefix: (path) => `http://10.102.80.243/llmpf/api/proxy/down?Action=Download&Version=2022-01-01&IsAnonymous=true&Path=${encodeURIComponent(path)}`,
            }
          }}
          showToken={false} //是否展示大模型消耗token数量
          showHallucination={false}
        />
      </div>
   
  );

}


```

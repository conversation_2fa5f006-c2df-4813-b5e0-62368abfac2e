/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React from 'react';
import { Button, useDeleteConversation } from '@ht/chatui';

export default () => {

  const { data: conversationData, loading: conversationLoading, run: conversationRun, error: conversationError } = useDeleteConversation({
    requestConfig: {
      platform: 'custom',
      url: '', // 删除地址
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '002332',
      conversationId: '3038468f-b55a-48ea-a9ab-83405c0253f9',
    },
  });

  return (
    <div>
      <Button onClick={() => conversationRun()} loading={conversationLoading}>删除会话</Button>
      {conversationData && !conversationError ? (
        <span style={{ padding: '10px', color: 'green'}}>会话创建成功</span>
      ) : ''}
    </div>
    )
}
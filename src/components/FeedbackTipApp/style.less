.FeedbackTipAppWrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 12;
  }

  .FeedbackTipApp {
    position: absolute;
    left: 0;
    bottom: 0;
    // width: 60vw;
    // height: 90vh;
    width: 100%;
    background: #FFFFFF;
    border-radius: 20px 20px 0 0;
    z-index: 13;
    transition: bottom 0.3s;
    background-origin: border-box;
    overflow: hidden;

    &-icon {
      width: 18px;
      height: 18px;
      margin-right: 8px;
    }

    &-title {
      display: flex;
      position: relative;
      // border-radius: 20px 20px 0 0;
      width: 100%;
      height: 78px;
      padding: 30px 20px 24px 20px;
      background: @feedback-tip-title-bg;
      border-radius: 20px 20px 0 0;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
      background-origin: border-box;
      overflow: hidden;
    }

    &-titleText {
      font-size: @feedback-tip-titleText-font-size;
      font-weight: normal;
      color: @feedback-tip-titleText-font-color;
      line-height: 24px;
      background: @feedback-tip-titleText-bg;
      -webkit-background-clip: text;
      -webkit-text-fill-color: @feedback-tip-titleText-font-color;

    }

    &-titleClose {
      width: 14px;
      height: 14px;
      cursor: pointer;
      position: absolute;
      top: 21px;
      right: 15px;
    }

    &-content {
      padding: 0 20px;
      // height: 670px;
      height: calc(100% - 158px);
      overflow-y: auto;
      margin-bottom: 20px;
    }

    &-labels {
      display: flex;
      flex-wrap: wrap;
      /* 关键属性：允许换行 */
      // gap: 12px 12px;
      /* 可选：设置子元素间距 */
      // margin-top: 15px;
      margin-top: 3px;
    }


    &-contentText {
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      margin-top: 6px;
    }

    &-labelItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 12px;
      margin-right: 12px;
      padding: 4px 12px;
      height: 30px;
      background: #F5F7F9;
      border-radius: 15px;
      border: 1px solid #CCCCCC;
      cursor: pointer;

      font-size: 13px;
      font-weight: 400;
      color: #777777;
      line-height: 18px;

      &:last-child {
        padding-bottom: 20px;
      }
    }

    &-labelItemActive {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 12px;
      margin-right: 12px;
      padding: 4px 12px;
      height: 30px;
      background: rgba(62, 116, 247, .15);
      border-radius: 15px;
      border: 1px solid transparent;
      cursor: pointer;

      font-size: 13px;
      font-weight: 500;
      color: #3E74F7;
      line-height: 18px;
    }

    &-labelItem-input {
      margin-top: 13px;
      height: 96px;
      background: #F5F7F9;
      border-radius: 8px;
    }
  }

  @media (min-width: 1919px) {
    .FeedbackTipApp {
      width: 460px;
    }
  }

  @media (min-height: 1079px) {
    .FeedbackTipApp {
      height: 406px;
    }
  }

  .FeedbackTipFooter {
    float: right;
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 40px;
  }

  .FooterOkBtn {
    width: 100%;
    height: 44px;
    margin-left: 16px;
    margin-right: 16px;
    background: #3E74F7;
    border-radius: 22px;
    text-align: center;
    padding: 11px 0;
    cursor: pointer;

    font-size: 16px;
    color: #FFFFFF;
    line-height: 22px;
  }

  .FooterOkBtnDisable {
    width: 100%;
    height: 44px;
    margin-left: 16px;
    margin-right: 16px;
    background: #CCCCCC;
    border-radius: 22px;
    text-align: center;
    padding: 11px 0;
    cursor: not-allowed;
    font-size: 16px;
    color: #FFFFFF;
    line-height: 22px;
  }

  /* 整个滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    /* 垂直滚动条宽度 */
    height: 6px;
    /* 水平滚动条高度 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
    /* 轨道背景色 */
    // border-radius: 6px;
    /* 轨道圆角 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #E5E7EE;
    /* 滑块背景色 */
    border-radius: 3px;
    /* 滑块圆角 */
    border: 3px solid #f1f1f1;
    /* 滑块边框 */
    width: 6px;
    height: 45px;
    background: #E5E7EE;
    border-radius: 3px;
  }

  /* 滚动条滑块悬停效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
    /* 悬停时滑块背景色 */
  }
}

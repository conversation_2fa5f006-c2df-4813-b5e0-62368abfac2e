import React, { useState } from 'react';
import { PushDivProps } from './interface';

export const PushDiv = (props: PushDivProps) => {
  const { pushPosition = 'right', pushPercent = 75 } = props;
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  const toggleHistory = () => {
    setIsHistoryOpen(!isHistoryOpen);
  };

  const closeHistory = () => {
    setIsHistoryOpen(false);
  };

  const historyPanelStyle = pushPosition === 'left'
    ?
    {
      left: 0,
      transform: 'translateX(-100%)', /* 初始隐藏在左边 */
      width: `${pushPercent}%`, /* 历史面板展开宽度 */
    }
    : {
      right: 0,
      transform: 'translateX(100%)', /* 初始隐藏在右边 */
      width: `${pushPercent}%`,/* 历史面板展开宽度 */
    }

  /* 历史面板展开时复位 */
  const historyPanelPushStyle = isHistoryOpen ? { transform: 'translateX(0)' } : {}

  /* 历史面板展开时，用transform实现视觉偏移 */
  const chatPannelPushStyle = pushPosition === 'left'
    ? {
      transform: `translateX(${pushPercent}%)`, /* 向左推相应宽度 */
    }
    : {
      transform: `translateX(-${pushPercent}%)`, /* 向右推相应宽度 */
    };

  return (
    <div className="push-div-container">
      {/* 左侧历史对话列表 */}
      <div className="history-hanel" style={{ ...historyPanelStyle, ...historyPanelPushStyle }}>
        <h2>历史对话板板</h2>
        <ul>
          <li onClick={closeHistory}>对话 1</li>
          <li onClick={closeHistory}>对话 2</li>
          <li onClick={closeHistory}>对话 3</li>
        </ul>
      </div>

      {/* 右侧对话问答区域 */}
      <div className="chat-panel" style={isHistoryOpen ? chatPannelPushStyle : {}}>
        {/* 遮罩层 */}
        {isHistoryOpen && <div className={`overlay ${isHistoryOpen ? 'overlay-show' : ''}`} onClick={closeHistory}></div>}
        <div >
          <button onClick={toggleHistory}>
            展开历史面板
          </button>
          <h2>对对话问答对话问答对话问答对话问答对话问答话问答</h2>
        </div>
        <div>
          <p>这里是对这里是对话内容这里是对话内容这里是对话内容这里是对话内容这里是对话内容这里是对话内容这里是对话内容这里是对话内容这里是对话内容话内容...</p>
        </div>
      </div>
    </div>
  );
};
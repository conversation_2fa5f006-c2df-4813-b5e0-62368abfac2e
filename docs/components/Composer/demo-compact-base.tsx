import React from 'react';
import { Composer } from '@ht/chatui';

export default () => {
  const onSend = async (type: string, content: string) => {
    return new Promise<boolean>((resolve) => {
      console.log('type', type, 'content', content);
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
  };

  return <Composer text="ceshi" inputType="text" onSend={onSend}></Composer>;
};

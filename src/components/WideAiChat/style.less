.WideAiChatLayout {
    display: flex;
    flex-direction: row;
    position: relative;
    height: 100%;
    border-radius: inherit;
    border-top:  1px solid #DEE8F5;

    &.onlyChatContainer {
      > .WideAiChatSider {
        display: none;
      }
      > .WideAiChatContent {
        padding: 0;
        margin: 0;
      }
    }
    > .WideAiChatSider {
      width: 230px;
      display: flex;
      flex-direction: column;
      height: 100%;
      border-right: 1px solid #DEE8F5;     
      background-color: #f9fbff;
      
    }
    > .WideAiChatContent {
      flex: 1;
      height: 100%;
      padding: 0;
      margin: 0;
    }

    .PushDivContainer {
      justify-content: center;
    }

    .ChatWrap {
      min-width: auto;
      // max-width: 800px;

      .MessageList {
        padding-left: max(calc(50% - 462px), 24px);
        padding-right: max(calc(50% - 462px), 24px);
      }

      .ChatConfig {
        padding-left: max(calc(50% - 462px), 24px);
        padding-right: max(calc(50% - 462px), 24px);
      }

      .GuidePagePC {
        padding-left: max(calc(50% - 462px), 24px);
        padding-right: max(calc(50% - 462px), 24px);
      }

      .ChatFooter {
        padding-left: max(calc(50% - 462px), 24px);
        padding-right: max(calc(50% - 462px), 24px);
      }
    }
}
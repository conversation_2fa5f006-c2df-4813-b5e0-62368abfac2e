/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useEffect } from 'react';
import ReactDOM from "react-dom";
import { Input } from '../Input';
import { FeedbackTipPcProps, FeedbackLabelProps } from './interface';
import CloseIcon from './images/closeIcon.png';
// import TitlePg from './images/title.png';

export const FeedbackTipApp = (props: FeedbackTipPcProps) => {
  const { onClose, onSubmit, title, labels, inputPlaceholder, showLabels = true } = props;

  // const labels = [
  //   { id: 'err1', label: '没有理解问题' },
  //   { id: 'err2', label: '上下文错误' },
  //   { id: 'err3', label: '编造事实' },
  //   { id: 'err4', label: '推理错误' },
  //   { id: 'err5', label: '内容不完整' },
  //   { id: 'err6', label: '内容重复' },
  //   { id: 'err7', label: '时效性差' },
  //   { id: 'err8', label: '格式错误' },
  // ]
  const defaultTitle = '很抱歉，让您有了不好的感受';
  const defaultPlaceholder = '如果您愿意的话，欢迎说出您的想法帮助我改进~';

  const [labelList, setLabelList] = useState<FeedbackLabelProps[] | undefined>([]);
  const [selectLabels, setSelectLabels] = useState<FeedbackLabelProps[]>([]);

  const [inputValue, setInputValue] = useState();


  useEffect(() => {
    setLabelList(labels);
  }, [labels]);

  const handleLabelCilck = (e: FeedbackLabelProps) => {
    if (selectLabels?.find((l) => l.id === e.id)) {
      setSelectLabels(selectLabels?.filter((l) => l.id !== e.id));
      return;
    } else {
      setSelectLabels([...selectLabels, e]);
    }
  }

  const handleInputChange = (e: any) => {
    setInputValue(e);
  }
  const handleClose = () => {
    onClose && onClose();
  }

  const handleSubmit = () => {
    const shrinkValue = (inputValue || '').trim();
    if (!selectLabels?.length && !shrinkValue) return;
    onSubmit && onSubmit(selectLabels, shrinkValue);
    onClose && onClose();
  }

  return (
    <>
      {ReactDOM.createPortal(
        <div className="FeedbackTipAppWrap">
          <div className='FeedbackTipAppWrap-mask'></div>
          <div className="FeedbackTipApp">
            <div className="FeedbackTipApp-title">
              {/* <img src={TitlePg} alt='' className="FeedbackTipApp-titleText" /> */}
              <div className="FeedbackTipApp-titleText">
                {title || defaultTitle}
              </div>
              <img src={CloseIcon} alt='' className="FeedbackTipApp-titleClose" onClick={handleClose} />
            </div>
            <div className="FeedbackTipApp-content" >
              <div>
                {labelList?.length ? '请点击下列标签进行具体反馈：' : '请反馈具体问题：'}
              </div>
              <div className="FeedbackTipApp-labels">
                {showLabels && labelList?.length &&
                  labelList?.map((item, index) => {
                    const hasSelected = selectLabels?.find((l) => l.id === item.id);
                    return (
                      <div key={index} className={hasSelected ? "FeedbackTipApp-labelItemActive" : "FeedbackTipApp-labelItem"} onClick={() => handleLabelCilck(item)}>
                        {item?.label}
                      </div>
                    );
                  })
                }
              </div>
              <div>
                <Input
                  className='FeedbackTipApp-labelItem-input'
                  rows={1}
                  autoSize
                  enterKeyHint="send"
                  onChange={handleInputChange}
                  value={inputValue}
                  maxLength={500}
                  showCount={false}
                  // ref={inputRef}
                  placeholder={inputPlaceholder || defaultPlaceholder}
                />
              </div>

            </div>
            <div className='FeedbackTipFooter'>
              <div className={selectLabels?.length || inputValue ? "FooterOkBtn" : "FooterOkBtnDisable"} onClick={handleSubmit}>
                提交
              </div>
            </div>

          </div>
        </div>,
        document.body
      )}
    </>
  );
};

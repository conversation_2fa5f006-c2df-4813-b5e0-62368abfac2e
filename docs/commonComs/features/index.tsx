
import React from 'react';
import './index.css';

const Features = () => {
  return (
    <div className="dumi-site-features">
      <div className='item'>
        <span className='title'>对话式体验设计</span>
        <span className='desc'>统一、高效、沉浸式对话设计</span>
      </div>  
      <div className='item'>
        <span className='title'>一键唤醒</span>
        <span className='desc'>接入即享内置完整对话系统，随用随聊</span>
      </div>
      <div className='item'>
        <span className='title'>响应式</span>
        <span className='desc'>提供宽、窄屏完整对话窗口，灵活适应多场景</span>
      </div>
      <div className='item'>
        <span className='title'>可观测</span>
        <span className='desc'>一键接入用户侧可观测能力，助力模型调优与问题诊断</span>
      </div>
      <div className='item'>
        <span className='title'>灵活定制</span>
        <span className='desc'>支持对话组件灵活配置,消息卡片自定义,基于hooks可自定义使用场景</span>
      </div>
    </div>
  );
};

export default Features;
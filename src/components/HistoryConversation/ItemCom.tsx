/*
 * @Author: 020446 <EMAIL>
 * @Date: 2025-07-22 20:37:09
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-08-14 14:45:47
 */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import clsx from 'clsx';
import { ConversationItemProps } from './interface';
import DropdownMenu from './DropdownMenu';
import showMoreIcon from './images/more.svg'

// interface ConversationsItemComponent {
//     conversationId: string;
//     question: string;
//     createTime: number;
//     activeConversationId?: string;
//     disabled?: boolean;
//     menuConfig?: any;
//     onConversationClick: () => void;
// }

// const stopPropagation: React.MouseEventHandler<HTMLSpanElement> = (e) => {
//     e.stopPropagation();
// };

const ConversationsItem: React.FC<ConversationItemProps> = (props) => {
    const {
        conversationId,
        question,
        // createTime,
        disabled,
        activeConversationId,
        // menuConfig,
        onConversationClick,
        onConversationRename,
        onConversationDelete,
        showDeleteConversation,
        showRename,
        ...rest
    } = props;

    const [tooltipShow, setTooltipShow] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);

      useEffect(() => {
          const titleEle = document.getElementById(`${conversationId}`);
        if (titleEle) {
          if (titleEle.scrollWidth > titleEle.clientWidth) {
            setTooltipShow(true);
          } else {
            setTooltipShow(false);
          }
        }
      }, []);

    const toggleMenu = () => {
      setIsMenuOpen(!isMenuOpen);
    };

    const itemCls = clsx(
        { ['conversationItem']: true },
        { ['conversationItemActive']: activeConversationId === conversationId },
        { ['conversationItemDisabled']: disabled },
    );

    return (
        <div className={itemCls} >
          <div 
            id={conversationId} 
            className="itemLabel"
            title={tooltipShow ? question : ''} 
            onClick={() => onConversationClick({conversationId, question, ...rest})}
          >
            {question}
        </div>
        {
          (showDeleteConversation || showRename) && (
            <div className='itemMenuWrap'>  
              <div 
                className="itemMenu"
                onClick={toggleMenu}
              >
                <img src={showMoreIcon} />
              </div>
              {isMenuOpen && (
                <DropdownMenu
                  conversationItem={{conversationId, question, ...rest}} 
                  isOpen={isMenuOpen} 
                  showDeleteOption={showDeleteConversation}
                  showRenameOption={showRename}
                  onClose={() => setIsMenuOpen(false)} 
                  onRename={onConversationRename}
                  onDelete={onConversationDelete}
                />
                )
              }
            </div>
          )
        }
        </div>
    );
};

export default ConversationsItem;

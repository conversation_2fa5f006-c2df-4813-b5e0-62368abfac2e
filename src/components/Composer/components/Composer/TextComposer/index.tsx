import { Input } from '@ht/sprite-ui';
import React from 'react';
import type { ComposerProps } from '../../type';

import SendButton from '../components/SendButton';
import CloseSvg from '../svg/close.svg';
import DefaultIcon from './defaultIcon.svg';
import './style.less';

export interface TextComposerProps extends ComposerProps {
  [key: string]: any; // 其他扩展属性
}
export default function TextComposer(props: TextComposerProps) {
  const { icon, title, onclose, text = '', setText, onSend, ...rest } = props;
  const onSendBtnClick = async () => {
    console.log('TextComposer.onSend');
    onSend();
  };

  const onInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };
  return (
    <div className="textComposer">
      <div className="header">
        <div className="title">
          {icon || <img src={DefaultIcon} />}
          <span>{title}</span>
        </div>
        <div className="handle">
          <img className="close" src={CloseSvg} onClick={onclose} />
        </div>
      </div>
      <div className="content">
        <Input.TextArea {...rest} rows={5} value={text} onChange={onInputChange} />
      </div> 
      <div className="footer">
        <SendButton hasContent={!!text.trim()} onClick={onSendBtnClick} />
      </div>
    </div>
  );
}

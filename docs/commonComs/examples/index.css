.dumi-site-examples {
    margin-bottom: 20px;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* justify-content: left; */
    align-items: left;
    flex-wrap: wrap;

    .line-wrapper {
      display: flex;
      flex-direction: column;
      align-items: left;
      margin-bottom: 20px;

      .title-wrapper{
        height: 52px;
        display: flex;
        align-items: center;
  
        .title-icon {
          margin-right: 16px;
          height: 52px;
          width: 52px;
        }

        .title-text {
          font-weight: bold;
          font-size: 32px;
          line-height: 34px;
        }
      }
      .desc {
        width: 100%;
        margin-top: 24px;
        margin-bottom: 40px;
        color: #3f434b;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
      }

      .img-pc {
        width: 1070px;
        height: 780px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
      }
      .img-app {
        width: 300px;
        height: 670px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
      }
      .img-wrapper{
        display:flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;
      }

      .img-desc {
        min-width: 168px;
        height: 52px;
        line-height: 52px;
        border-radius: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 10px;
        color: #fff;
        background: #1677ff;
      }
      .icon {
        width: 20px;
        height: 20px;
      }
    }

    .flex-right {
      display: flex;
      flex-direction: column;
      align-items: right;
    }
  }
  
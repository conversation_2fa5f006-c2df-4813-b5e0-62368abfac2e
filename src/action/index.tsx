/*
 * @Author: 020446 tianxia<PERSON><EMAIL>
 * @Date: 2025-08-14 19:31:31
 * @LastEditors: 020446 <EMAIL>
 * @LastEditTime: 2025-08-15 11:01:33
 */

import { useCallback, useRef, useState } from "react";
import { FrontendAction } from "./interface";

  // 创建自定义 hook 用于处理 action 执行
export const useActionHook = (
    actions: Array<FrontendAction<any>>
  ) => {
    const [isProcessing, setIsProcessing] = useState(false);
    const processedActionsRef = useRef<Set<string>>(new Set());

    const executeAction = useCallback(async (actionName: string, actionData: any) => {
      if (isProcessing) return;
      
      // 防止重复执行同一个 action
      const actionKey = `${actionName}_${JSON.stringify(actionData)}`;
      if (processedActionsRef.current.has(actionKey)) return;
      
      setIsProcessing(true);
      processedActionsRef.current.add(actionKey);

      try {
        // 严格匹配 action name
        const matchedAction = actions.find(action => action.name === actionName);
        
        if (!matchedAction) {
          console.warn(`未找到匹配的 action: ${actionName}`);
          return;
        }

        console.log(`开始执行 action: ${actionName}`, actionData);

        // 依次执行 handler 和 render 方法
        let handlerResult;
        if (matchedAction.handler && typeof matchedAction.handler === 'function') {
          handlerResult = await matchedAction.handler(actionData);
          console.log(`Action ${actionName} handler 执行完成:`, handlerResult);
        }

        // if (matchedAction.render && typeof matchedAction.render === 'function') {
        //   const renderResult = matchedAction.render(actionData);
        //   console.log(`Action ${actionName} render 执行完成:`, renderResult);
          
        //   // 如果返回了React组件，显示对话框
        //   if (renderResult && React.isValidElement(renderResult)) {
        //     setActionDialog({
        //       visible: true,
        //       component: React.cloneElement(renderResult, {
        //         ...renderResult.props,
        //         onConfirm: () => {
        //           renderResult.props?.onConfirm?.();
        //           setActionDialog({ visible: false, component: null });
        //         },
        //         onCancel: () => {
        //           renderResult.props?.onCancel?.();
        //           setActionDialog({ visible: false, component: null });
        //         }
        //       })
        //     });
        //   }
        // }

      } catch (error) {
        console.error(`执行 action ${actionName} 时出错:`, error);
      } finally {
        setIsProcessing(false);
        // 延迟清理，避免快速重复执行
        setTimeout(() => {
          processedActionsRef.current.delete(actionKey);
        }, 1000);
      }
    }, [actions, isProcessing]);

    return { executeAction, isProcessing };
  };
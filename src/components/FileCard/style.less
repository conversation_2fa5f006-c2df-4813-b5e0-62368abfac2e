.FileCard {
  // width: 241px;
  // padding: 12px 15px;
  box-sizing: border-box;
  width: 100%;
  // max-width: @card-size-xl;
  max-width: 236px;
  padding: 7px 12px;
  background: @card-bg;
  border-radius: 12px;
  border: 1px solid #f2f2f3;
  border-radius: 8px;
}

.FileCard-icon {
  position: relative;
  width: 36px;
  height: 36px;
  // height: 60px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: @gray-2;

  &[data-type='pdf'] {
    color: @red;
  }
  &[data-type*='doc'] {
    color: @blue;
  }
  &[data-type*='ppt'],
  &[data-type='key'] {
    color: @orange;
  }
  &[data-type*='xls'] {
    color: @green;
  }
  &[data-type='rar'],
  &[data-type='zip'] {
    color: @brand-1;
  }
  .Icon {
    font-size: 60px;
  }
}

.fileIcon {
  width: 25px;
  height: 32px;
}

.FileCard-name {
  // margin-bottom: 4px;
  // width: 111px;
  // width: 90%;
  max-height: 44px;
  font-size: 14px;
  color: #1d222c;
  line-height: 22px;
  font-weight: @font-weight-bold;
}

.FileCard-ext {
  position: absolute;
  left: 20px;
  bottom: 15px;
  transform-origin: left bottom;
  transform: scale(0.5);
  max-width: 50px;
  font-size: @font-size-md;
  font-weight: 700;
  text-transform: uppercase;
}

.FileCard-meta {
  // width: 90%;
  color: @gray-3;
  font-size: 14px;
  color: #6c6f76;
  line-height: 22px;

  & > a,
  & > span {
    margin-right: 10px;
  }
  a {
    color: @link-color;
    text-decoration: none;
  }
}

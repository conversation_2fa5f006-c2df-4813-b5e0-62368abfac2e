/*
 * @Author: 016645
 * @Description: 停止消息生成功能
 * @Date: 2025-04-02 19:45:14
 */
import { useState, useEffect, useCallback } from 'react';
import { fetchData, fetchMS } from '../utils';
import { StopMessage } from '../types';
import  getBaseUrl   from '../../../utils/getBaseUrl';

/**
 * 停止消息返回数据类型
 */
export interface Data {
  code: string;
  msg: string;
  resultData: {};
}

/**
 * 停止消息生成的自定义Hook
 * @param options - 配置选项
 * @returns 包含停止消息方法和状态的对象
 */
const useStopMessage = (options: StopMessage) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    url = '',          // 自定义url
    type = 'http',     // 请求类型：http或tcp
    action = '',       // TCP请求action
    tcpRequest,        // TCP请求
    method = 'post',   // 请求方法
    paramsKey = '',    // 参数包裹的key
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,           // 请求头
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取完整url
  const newBaseUrl = getBaseUrl(baseUrl);
  let fullUrl = '';
  if(url) {
    fullUrl = (`${newBaseUrl.replace(/\/+$/, '')}/${url.replace(/^\/+/, '')}`);
  } else {
    fullUrl = (`${newBaseUrl.replace(/\/+$/, '')}/stop_message`);
  }

  /**
   * 停止消息生成方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);

    // 解构请求参数
    const { 
      userId,
      taskId,
      appKey,
    } = Object.assign({}, requestParams, customParams);
    
    try {
      // 初始化请求数据
      const initRequestData = {
        UserID: userId,
        TaskID: taskId,
        AppKey: appKey,
      };
      
      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? await requestTransfer(initRequestData) 
        : initRequestData;
        
      // 根据请求类型发送请求
      let responseData: any = null;

      if (type.toLowerCase() === 'tcp') {
        if(tcpRequest) {
          // 自定义TCP请求
          responseData = await tcpRequest({
            url: fullUrl,
            params: requestData,
          });
        } else {
          // 通用TCP请求
          responseData = await fetchMS({
            action, 
            url: fullUrl, 
            method, 
            paramsKey, 
            data: requestData, 
            headers
          });
        }
      } else {
        // HTTP请求
        responseData = await fetchData({
          url: fullUrl, 
          data: requestData,
          headers: { ...headers, Apikey: appKey },
        });
      }
      
      const resultData = {
        code: '0',
        msg: '删除会话成功',
        resultData: responseData,
      };

      // 应用响应转换器（如果提供）
      const result = responseTransfer 
        ? responseTransfer(resultData) 
        : resultData;
      console.log('result', result);
      // 更新数据状态
      setData(result);
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('停止消息生成失败')
      );
    } finally {
      // 完成后设置loading状态
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    requestParams,
    headers,
    action,
    method,
    paramsKey,
    tcpRequest,
    type,
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useStopMessage;
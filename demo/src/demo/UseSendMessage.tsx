/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-02 13:58:01
 */
import React, { useState } from 'react';
import { DemoPage, DemoSection } from '../components';
import { Button, Input } from '../../../src';
import { useSendMessage } from '../../../src/hooks/aiAgentChat/index';

export default () => {
  const [value1, setValue1] = useState('你是谁');
  const [value2, setValue2] = useState('你是谁');
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useSendMessage({
    requestConfig: {
      platform: 'custom',
      url: '/aorta/operation/api/ai/desktop/HiAgentAIChat',
      type: 'http',
      manual: true,
      headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
    },
    requestParams: {
      userId: '016645',
      question: value1,
      conversationId: 'cvhm0igldcehfetohfgg',
      stream: true,
    },

  });

  const { data: conversationData2, loading: conversationLoading2, run: conversationRun2 } = useSendMessage({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      type: 'http',
      manual: true,
    },
    requestParams: {
      userId: '016645',
      question: value2,
      stream: true,
    },
  });
  return (
    <DemoPage>
      <DemoSection title="自定义服务">
        <Input value={value1} onChange={setValue1} placeholder="请输入..." />
        <Button onClick={() => conversationRun()} loading={conversationLoading}>发送消息</Button>
        <div>
          {conversationData?.resultData?.list?.map((item: any) => (
            <span>{item.content.text}</span>
          ))}
        </div>
      </DemoSection>
      <DemoSection title="HiAgent">
        <Input value={value2} onChange={setValue2} placeholder="请输入..." />
        <Button onClick={() => conversationRun2()} loading={conversationLoading2}>发送消息</Button>
        <div>
          {conversationData2?.resultData?.list?.map((item: any) => (
            <span>{item.content.text}</span>
          ))}
        </div>
      </DemoSection>
    </DemoPage>
  )
};

@placeholderColor: @grey-9;

.searchHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 40px;
  box-sizing: border-box;

  .searchBox {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 40px;
    margin-left: 10px;
    padding: 0 20px 0 20px;
    border-radius: 8px;
    background-color: #F6F6F7;
    border: 0.5px solid #DDDEE0;
    box-sizing: border-box;

    .searchIcon {
      flex: 0 0 auto;
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .searchInput {
      flex: 1 1 auto;
      height: 30px;
    }

    .input {
      width: 100%;
      font-size: 15px;
      color: @placeholderColor;
      height: 30px;
      line-height: 30px;
      border: none;
      background-color: transparent;
      -webkit-appearance: none;

      // 隐藏搜索框自带的清除按钮
      &::-webkit-search-cancel-button {
        display: none;
        -webkit-appearance: none;
      }

      &::placeholder {
        font-size: 15px;
        color: @placeholderColor;
      }
      &::-webkit-placeholder {
        font-size: 15px;
        color: @placeholderColor;
      }
      &:-ms-placeholder {
        font-size: 15px;
        color: @placeholderColor;
      }
    }
    .input:focus{
      border: none;
      outline: none;
    }

    .deleteIcon {
      flex: 0 0 auto;
      width: 15px;
      height: 15px;
      // background-image: url('./images/del.png');
      // background-size: cover;
    }
  }

  .cancel {
    font-size: 28px;
    color: #fff;
    flex: 0 0 auto;
    height: 60px;
    line-height: 60px;
    padding-left: 20px;
  }
}

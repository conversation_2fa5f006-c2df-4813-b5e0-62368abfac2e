import React from 'react';
import WarningIcon from './images/warning.svg'

export interface BubbleProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: string;
  content?: string | undefined;
  children?: React.ReactNode;
  unsend?: boolean;
}

export const Bubble = (props: BubbleProps) => {
  const { type = 'text', content, children, unsend = false, ...other } = props;
  return (
    <div className={`Bubble ${type} ${unsend && 'Unsend'}`} data-type={type} {...other}>
      {unsend ? <div className="Bubble-unsend"><img src={WarningIcon} /></div> : null}
      {content && <p>{content}</p>}
      {children}
    </div>
  );
};

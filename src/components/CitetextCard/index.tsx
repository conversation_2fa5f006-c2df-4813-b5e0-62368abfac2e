import React from 'react';
import clsx from 'clsx';
import { CitetextCardProps, CitetextCardContentProps } from './interface';

export const CitetextCard: React.FC<CitetextCardProps> = (props) => {
  const { content = {} as CitetextCardContentProps } = props;

  return (
    <div className={clsx('CitetextCard', props?.className)}>
      <div className="CitetextCard-title">{content?.title}</div>
      <div className="CitetextCard-text">
        {content?.text}
      </div>
    </div>
  )
};

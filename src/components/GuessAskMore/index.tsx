import React, { <PERSON> } from 'react';

export type GuessAskMoreProps = {
  className?: string;
  questionList: { title: string, content: string, url?: string }[];
  onSend?:(type: string, content: string, params?: object) => Promise<boolean>;
  /**
    * 打开链接的方法，默认使用浏览器的打开方式
  */
  openPage?: (url: string) => void;
};

export const GuessAskMore: FC<GuessAskMoreProps> = (props) => {
  const { questionList, onSend, openPage } = props;

  // 不传默认用浏览器的行为打开链接
  const openWebPage = openPage || window.open;

  const send = (text: string, url: string | undefined) => {
    if (url) {
      openWebPage(url);
    } else {
      if (onSend) {
        onSend('text', text, { allowChange: false });
      }
    }
  };

  return (
    <div className='guessAskMoreContent'>
      {/* <div className='listTitle'>您可能想要了解：</div> */}
      {questionList?.map(item => (
        <div key={item?.content} className='listItem' onClick={() => send(item?.content, item?.url)}>
          {item?.title || item?.content}
          <span className="arrow">{'>'}</span>
        </div>
      ))}
    </div>
  );
};


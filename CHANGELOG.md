# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [2.4.2](https://github.com/alibaba/ChatUI/compare/v2.3.1...v2.4.2) (2022-09-01)


### Features

* sync 2.4.2 ([477f605](https://github.com/alibaba/ChatUI/commit/477f605b34a588511ff745b46b2bbeea5277b8b3))

### [2.3.1](https://github.com/alibaba/ChatUI/compare/v2.3.0...v2.3.1) (2022-07-20)


### Bug Fixes

* [MessageContainer] scrollToEnd when focus ([320b2ec](https://github.com/alibaba/ChatUI/commit/320b2ec04ccfcf41c58406e1b60ca6532f919561))

## [2.3.0](https://github.com/alibaba/ChatUI/compare/v2.2.2...v2.3.0) (2022-07-19)


### Features

* [Modal] support `btnVariant` ([0d86cde](https://github.com/alibaba/ChatUI/commit/0d86cde80cd49b4568aa807806d622d164d8cc47))

### [2.2.2](https://github.com/alibaba/ChatUI/compare/v2.2.1...v2.2.2) (2022-07-19)


### Bug Fixes

* [QuickReplies] update styles ([5c622b1](https://github.com/alibaba/ChatUI/commit/5c622b1a478129d5b08537953216634622ee3bfa))
* [Search] fix type ([794a482](https://github.com/alibaba/ChatUI/commit/794a482040878b678470f4936001b34b1508e206))
* [SystemMessage] fix padding ([1abbbf3](https://github.com/alibaba/ChatUI/commit/1abbbf30841a757111db89543e53801ead83f2b2))
* [Typing] remove useless style ([062cf95](https://github.com/alibaba/ChatUI/commit/062cf9514c3a4da002ca914f494381333e706adf))
* update brand-3 ([0fbeb8e](https://github.com/alibaba/ChatUI/commit/0fbeb8e3e5040d0bb9eed7f38a835573f53b414b))

### [2.2.1](https://github.com/alibaba/ChatUI/compare/v2.2.0...v2.2.1) (2022-07-12)


### Bug Fixes

* [Button] fix hover ([56aaaae](https://github.com/alibaba/ChatUI/commit/56aaaaecec667eb2bca9e4a2e2051d8f3bcb5fa1))
* [css] update `--red`'s value ([1ead654](https://github.com/alibaba/ChatUI/commit/1ead654a9063a0023e95cf8a373aa9bc20035997))
* [Toast] fix icon ([e23f952](https://github.com/alibaba/ChatUI/commit/e23f9521211a185037eb43059ed6e4da0c881271))

## [2.2.0](https://github.com/alibaba/ChatUI/compare/v2.1.3...v2.2.0) (2022-07-06)


### Features

* [Message] support `user.url` ([069664f](https://github.com/alibaba/ChatUI/commit/069664f3ff5c7ae5f937cfd377341f767eed2535))
* [Stepper] support `inverted` ([29eacf4](https://github.com/alibaba/ChatUI/commit/29eacf4b7f172fa11dca7e3db6c57cc3bd0142a5))


### Bug Fixes

* [Message] use circle avatar ([e397592](https://github.com/alibaba/ChatUI/commit/e3975929a2876be6252408e9cbf8d2b606c7c009))
* [Typing] fix color ([d49a0cf](https://github.com/alibaba/ChatUI/commit/d49a0cf832ba64d542d16612e37205fc546be191))

### [2.1.3](https://github.com/alibaba/ChatUI/compare/v2.1.2...v2.1.3) (2022-06-29)


### Bug Fixes

* [MessageContainer] `scrollToEnd` support `force` ([ea8566b](https://github.com/alibaba/ChatUI/commit/ea8566be1285d9ceb5b9969d3e1bbc2a34190fb9))

### [2.1.2](https://github.com/alibaba/ChatUI/compare/v2.1.1...v2.1.2) (2022-06-28)


### Bug Fixes

* [BackBottom] ([5177e30](https://github.com/alibaba/ChatUI/commit/5177e308fd4c99600042abfe3dc1a6d2fbec1e35))

### [2.1.1](https://github.com/alibaba/ChatUI/compare/v2.1.0...v2.1.1) (2022-06-27)


### Bug Fixes

* [MessageContainer] fix showBackBottom ([2afb8e9](https://github.com/alibaba/ChatUI/commit/2afb8e95a3d5f74d18aaec770c7236c621ce296f))

## [2.1.0](https://github.com/alibaba/ChatUI/compare/v2.0.0...v2.1.0) (2022-06-27)


### Features

* [BackBottom] ([28810a9](https://github.com/alibaba/ChatUI/commit/28810a9e375be708681be6bf8687c54525578cf5))


### Bug Fixes

* [Radio] fix styles ([b71d4d3](https://github.com/alibaba/ChatUI/commit/b71d4d37d78c10c92d16944fdd608d74f92ed065))
* [Toolbar] fix hover ([a6a97e7](https://github.com/alibaba/ChatUI/commit/a6a97e7a92cb01f42ba9fe7301765dcdedd3c0dd))

### [1.4.1](https://github.com/alibaba/ChatUI/compare/v1.4.0...v1.4.1) (2022-04-22)


### Bug Fixes

* [InfiniteScroll] fix handleOffset ([3ec9b83](https://github.com/alibaba/ChatUI/commit/3ec9b831130803f7f5916d5aa1cad9032096066a))

## [1.4.0](https://github.com/alibaba/ChatUI/compare/v2.0.0-beta.0...v1.4.0) (2022-04-19)


### Bug Fixes

* [InfiniteScroll] forwardRef ([5e3e1cc](https://github.com/alibaba/ChatUI/commit/5e3e1ccb054913b099d40d17f5cfd518f7f72cd1))

## [2.0.0](https://github.com/alibaba/ChatUI/compare/v2.0.0-beta.2...v2.0.0) (2022-06-20)


### Features

* [Search] support `showSearch` ([4c9509b](https://github.com/alibaba/ChatUI/commit/4c9509ba8e78b2210ff27b596cd2f66cead22cd7))


### Bug Fixes

* [Composer] fix send button's padding ([8ffbe2e](https://github.com/alibaba/ChatUI/commit/8ffbe2edc4804efa271736f42f58b30232addf9a))
* [Divider] fix color ([ed846e8](https://github.com/alibaba/ChatUI/commit/ed846e8e56f490b5cebcd73670cc99416db4049d))
* [Video] fix display ([8e3ffb8](https://github.com/alibaba/ChatUI/commit/8e3ffb891c9241a2793c9e8b893664522829b04a))
* fix hover ([031ada1](https://github.com/alibaba/ChatUI/commit/031ada10c12fcb8ac59d09418a0ab16c973b1089))

## [2.0.0-beta.2](https://github.com/alibaba/ChatUI/compare/v2.0.0-beta.1...v2.0.0-beta.2) (2022-06-06)


### Bug Fixes

* [Button] fix `.Btn--text`'s `font-size` ([8514431](https://github.com/alibaba/ChatUI/commit/8514431f6d291c12357c9db3abf539d46503bd0e))
* [Card] fix size ([3328857](https://github.com/alibaba/ChatUI/commit/3328857b03dd593b832594677f50639e42f398ef))
* [Tabs] fix pointer ([b3fe423](https://github.com/alibaba/ChatUI/commit/b3fe423e55088ffa396b87671de1f91fa278e038))

## [2.0.0-beta.1](https://github.com/alibaba/ChatUI/compare/v2.0.0-beta.0...v2.0.0-beta.1) (2022-05-16)


### Features

* [Button] support `--btn-primary-*` css vars ([e120f38](https://github.com/alibaba/ChatUI/commit/e120f3872bd14bfb6cbfbc6635fc8b0e11bee3b1))
* [Popup] support `bgColor` ([b3ca197](https://github.com/alibaba/ChatUI/commit/b3ca197a485a7b0c76b601364e2add0df2fd57d0))


### Bug Fixes

* [Button] fix hover & border-color ([6ed10e3](https://github.com/alibaba/ChatUI/commit/6ed10e3e6e9f894e38e687178f6291d83d4f3229))
* [Button] fix padding ([7a1a594](https://github.com/alibaba/ChatUI/commit/7a1a5941a29749e2d48b11d9b531a85fbc842c8e))
* [Card]  use `flex: 1` to button ([1fcd90f](https://github.com/alibaba/ChatUI/commit/1fcd90fd38f91f689f6342d495b47bd0b76ac417))
* [Navbar] apply `min-height` to .Navbar-main ([de28610](https://github.com/alibaba/ChatUI/commit/de28610cda52bf236de36089f5f114a51b3f118c))
* [Tabs] fix textWidth ([515917f](https://github.com/alibaba/ChatUI/commit/515917f881304fa0aa0265080ef83c9f006612dd))

## [2.0.0-beta.0](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.7...v2.0.0-beta.0) (2022-03-30)


### Bug Fixes

* [Bubble] min-width ([bcad4bb](https://github.com/alibaba/ChatUI/commit/bcad4bb8c05af67383ad4ca590f7d64ba7f35404))
* [Popup] abs ([5f32761](https://github.com/alibaba/ChatUI/commit/5f32761e942e506116e13485ab5c168858b4474c))

## [2.0.0-alpha.7](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.6...v2.0.0-alpha.7) (2022-03-30)


### Features

* [Toast] ui5.1 ([12f6a31](https://github.com/alibaba/ChatUI/commit/12f6a31d596be89dfca9e6ac9cbb146c7294a570))

## [2.0.0-alpha.6](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.5...v2.0.0-alpha.6) (2022-03-29)


### Bug Fixes

* ui5.1 ([30a51c9](https://github.com/alibaba/ChatUI/commit/30a51c93487ab3e6153fa1fa73b47de7736c0287))

## [2.0.0-alpha.5](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.4...v2.0.0-alpha.5) (2022-03-28)


### Bug Fixes

* ui5.1 ([78a2d04](https://github.com/alibaba/ChatUI/commit/78a2d049a95d8744e0e77d0b25d057db7eb5ab94))

## [2.0.0-alpha.4](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.3...v2.0.0-alpha.4) (2022-03-22)


### Bug Fixes

* ui5.1 ([a468ef9](https://github.com/alibaba/ChatUI/commit/a468ef977ebe6f7a7cf68c710baeb97c5e0c27b1))

## [2.0.0-alpha.3](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.2...v2.0.0-alpha.3) (2022-03-15)


### Bug Fixes

* [DEMO] fix styles ([b0225e0](https://github.com/alibaba/ChatUI/commit/b0225e0415718032b7922f01692c4f8e32713d3a))
* [List] update styles ([f85c911](https://github.com/alibaba/ChatUI/commit/f85c91118c98337046b754adb774b01ea07c5cd1))
* [Modal] fix actions ([5ce52ed](https://github.com/alibaba/ChatUI/commit/5ce52edf5aae3d9f7952dbc33f26655aff6076b7))
* [SystemMessage] update styles ([f40e313](https://github.com/alibaba/ChatUI/commit/f40e3134f87284f9b6cae85a366091177885b192))
* rm storybook ([709e1fc](https://github.com/alibaba/ChatUI/commit/709e1fceaf1ac46056aaf239e22d654259e6d5fb))

## [2.0.0-alpha.2](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.1...v2.0.0-alpha.2) (2022-03-04)


### Bug Fixes

* [Button] remove `xl` size, add `--link-color` ([75f6b01](https://github.com/alibaba/ChatUI/commit/75f6b012e130ddb9462cc6edabb5204d117c7780))
* [IconButton] size ([f506f7e](https://github.com/alibaba/ChatUI/commit/f506f7e6c014db9836afbe1abc5bea82482d50f2))
* [MessageList] font-size ([615a8e1](https://github.com/alibaba/ChatUI/commit/615a8e1ee34b194a141bb3a29ebca80aa433f982))
* [Popup] fix actions ([b8fbd9b](https://github.com/alibaba/ChatUI/commit/b8fbd9bf9a282012fe9d039cd882594aace16f87))

## [2.0.0-alpha.1](https://github.com/alibaba/ChatUI/compare/v2.0.0-alpha.0...v2.0.0-alpha.1) (2022-03-01)


### Features

* pxtorem ([d92d5a6](https://github.com/alibaba/ChatUI/commit/d92d5a69649e7e9ab516a2771748bb3c7599da96))


### Bug Fixes

* [Modal] fix safe area ([6beae88](https://github.com/alibaba/ChatUI/commit/6beae88720f0f794db75b62fc6bceba058352bef))
* ui 5.1 ([62b6863](https://github.com/alibaba/ChatUI/commit/62b686366924ad4bb57111dcb426cadb87c44a5a))

## [2.0.0-alpha.0](https://github.com/alibaba/ChatUI/compare/v1.3.0...v2.0.0-alpha.0) (2022-02-22)


### Features

* ui 5.1 ([70a2627](https://github.com/alibaba/ChatUI/commit/70a2627f8ddae0e4e5395ed44c08a3f3ed4d7d2a))

## [1.3.0](https://github.com/alibaba/ChatUI/compare/v1.2.4...v1.3.0) (2022-01-25)


### Features

* [Notice] support rich-text ([55160f0](https://github.com/alibaba/ChatUI/commit/55160f0f047138a68e97fdd99bd2356a178c734e))


### Bug Fixes

* [Modal] fix autoFocus ([f902b50](https://github.com/alibaba/ChatUI/commit/f902b50309642cd65148ca2f724fb67ce4579a6d))
* [Step] fix StepProps ([e8b5a0c](https://github.com/alibaba/ChatUI/commit/e8b5a0cf8af858db94eceef3c52ebcbd4c6b5274))

### [1.2.4](https://github.com/alibaba/ChatUI/compare/v1.2.3...v1.2.4) (2022-01-11)


### Bug Fixes

* [Bubble] rest props ([2ada2a6](https://github.com/alibaba/ChatUI/commit/2ada2a6b4f52540233e8b9f0eb9321ca23da8429))
* [Goods] fix price ([c9893fc](https://github.com/alibaba/ChatUI/commit/c9893fc9a0944a359c634255005f19171a38a09b))

### [1.2.3](https://github.com/alibaba/ChatUI/compare/v1.2.2...v1.2.3) (2021-12-06)


### Bug Fixes

* [ScrollView] don't use `useLocale` ([dc74295](https://github.com/alibaba/ChatUI/commit/dc742950e71df7a8d7e4b659fe91a4f5405f897d))

### [1.2.2](https://github.com/alibaba/ChatUI/compare/v1.2.1...v1.2.2) (2021-11-15)


### Bug Fixes

* [Composer] scrollIntoView when touch is supported ([ecbad5d](https://github.com/alibaba/ChatUI/commit/ecbad5d0cb065b4ed7d552101c203b927bc07da4))
* [dependencies] update dompurify to 2.3.3 ([f7209b6](https://github.com/alibaba/ChatUI/commit/f7209b6f670937cd9d653373e50b61c534cc654c))
* [Goods] apply `cover` to the image ([03f5694](https://github.com/alibaba/ChatUI/commit/03f569400a913b3e2ec84ec06d81c527bb7cae90))
* [Goods] fix desc ([ff864a8](https://github.com/alibaba/ChatUI/commit/ff864a8cf0bac2a7c1e804d27f11a55c0840dba9))

### [1.2.1](https://github.com/alibaba/ChatUI/compare/v1.2.0...v1.2.1) (2021-09-26)


### Bug Fixes

* [Chat] use `100vh`, except the Safari ([05a6401](https://github.com/alibaba/ChatUI/commit/05a64018c460753fb17168a7627484b2c8f4846c))

## [1.2.0](https://github.com/alibaba/ChatUI/compare/v1.1.3...v1.2.0) (2021-09-18)


### Features

* [Chat] Scroll to the bottom while focusing ([e8c4107](https://github.com/alibaba/ChatUI/commit/e8c410736d4ab2f3892b586383fa252c75ea5361))

### [1.1.3](https://github.com/alibaba/ChatUI/compare/v1.1.2...v1.1.3) (2021-09-14)


### Bug Fixes

* [Price] fallback for `Intl.Numberformat.prototype.formatToParts` ([ae12bd5](https://github.com/alibaba/ChatUI/commit/ae12bd5aaea77670553e611198b968e5d032143e))

### [1.1.2](https://github.com/alibaba/ChatUI/compare/v1.1.1...v1.1.2) (2021-08-03)


### Bug Fixes

* [List] fix border ([184d554](https://github.com/alibaba/ChatUI/commit/184d55462f51918e15087ecc1c9a4147d96e7a92))
* [QuickReplies] use `name` as `itemKey` ([5bf61c1](https://github.com/alibaba/ChatUI/commit/5bf61c14fe548fee150fdee1e2348e94ca31e301))

### [1.1.1](https://github.com/alibaba/ChatUI/compare/v1.1.0...v1.1.1) (2021-07-15)


### Bug Fixes

* [LocaleProvider] add fallback ([8948912](https://github.com/alibaba/ChatUI/commit/89489127aee5cbf97a26450e648cd5568152ea67))
* [ScrollView] a11y ([8adf043](https://github.com/alibaba/ChatUI/commit/8adf04337d0721956a8e587bd068c28a5dfae72f))

## [1.1.0](https://github.com/alibaba/ChatUI/compare/v1.0.2...v1.1.0) (2021-07-13)


### Features

* add `MessageStatus` ([b145305](https://github.com/alibaba/ChatUI/commit/b145305026b47a5ba0eff4e92e66a2e323cc9fa7))


### Bug Fixes

* [Chat] remove `body`'s margin ([2b8deed](https://github.com/alibaba/ChatUI/commit/2b8deedf346c8f663c163cc7fb4561d141b5772d))
* [PullToRefresh] update fallback's padding ([f8456f2](https://github.com/alibaba/ChatUI/commit/f8456f22fe15c7cf4edf8303edd01803417ae355))

### [1.0.2](https://github.com/alibaba/ChatUI/compare/v1.0.1...v1.0.2) (2021-06-30)

### [1.0.1](https://github.com/alibaba/ChatUI/compare/v1.0.0...v1.0.1) (2021-06-30)


### Bug Fixes

* [QuickReplies] clearTimeout after unmount ([1602f33](https://github.com/alibaba/ChatUI/commit/1602f336c4b8b106b6f5b5aab5a20201c7a03e84))

## [1.0.0](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.7...v1.0.0) (2021-06-28)

## [1.0.0-beta.7](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.6...v1.0.0-beta.7) (2021-06-24)


### Bug Fixes

* [Goods] update demo ([f3f3f2a](https://github.com/alibaba/ChatUI/commit/f3f3f2ab1c4a850f86baa94e019dcddd3169d3be))
* [Modal] clear `.S--modalOpen` after close modal ([4caab40](https://github.com/alibaba/ChatUI/commit/4caab40de51efb0d4719f957775b8382e6e792e6))

## [1.0.0-beta.6](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.5...v1.0.0-beta.6) (2021-06-16)


### Bug Fixes

* [Chat] fix the style for safe-area ([af797e9](https://github.com/alibaba/ChatUI/commit/af797e99b5ff33f9743b9d3aecb5dace4bf4ec24))
* [Input] add `-webkit-appearance: none` ([5f683f8](https://github.com/alibaba/ChatUI/commit/5f683f895af234f5f08565d2f659ec5fd80e5bd4))
* [Input] fix the `variant` variable ([dc76d84](https://github.com/alibaba/ChatUI/commit/dc76d84486a13b63ca5edec29ad93f2355156e82))
* [Modal] remove `.S--modalOpen` after close modal ([45ccf0a](https://github.com/alibaba/ChatUI/commit/45ccf0a67a82b0795b79ec497e80fcfec67c47aa))
* [Notice] fix the style of the bullhorn icon ([d208c5a](https://github.com/alibaba/ChatUI/commit/d208c5a2fad2b91433cba78231d7ddedd8de8055))
* [RateActions] fix the active styles ([395616d](https://github.com/alibaba/ChatUI/commit/395616d9c7ab15d6ba37726134f2fcb73b471ea6))

## [1.0.0-beta.5](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.4...v1.0.0-beta.5) (2021-05-28)


### Features

* [Input] support `variant` ([f39bed8](https://github.com/alibaba/ChatUI/commit/f39bed8019e5d412f0903d3100a34eb614416ee9))
* [root.less] add `--font-size-xx` & `--radius-xx` ([062e74d](https://github.com/alibaba/ChatUI/commit/062e74dfa821d2a2ff4aee094f78afea74e1d015))
* [Search] set `enterKeyHint` to `search` ([1eea387](https://github.com/alibaba/ChatUI/commit/1eea3876f33b361b099375bfc5f20ab70aefd547))
* [Select] add `Select` ([9b52bf0](https://github.com/alibaba/ChatUI/commit/9b52bf0b8aa201045bf0405330ca63d0fa93b1d9))

## [1.0.0-beta.4](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.3...v1.0.0-beta.4) (2021-05-26)


### Bug Fixes

* [Composer] fix the value of `ComposerInput` ([7dc769a](https://github.com/alibaba/ChatUI/commit/7dc769a7668ebe7c8c086298dc75bbd64f357383))

## [1.0.0-beta.3](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.2...v1.0.0-beta.3) (2021-05-25)


### Features

* [Chat] support `inputOptions` ([3743eaf](https://github.com/alibaba/ChatUI/commit/3743eaf3f6f3a5764bb7d790397a19ea7af85d0c))
* [Goods] add `locale` prop for the price ([45dff02](https://github.com/alibaba/ChatUI/commit/45dff028b77cd4a06eecd5370d3fcd1b5b20adb7))
* [Input] support `showCount` ([a092097](https://github.com/alibaba/ChatUI/commit/a092097d2eaa135a57f0a403dce170d2e2e21c9f))


### Bug Fixes

* [Search] execute onCancel when click clear button ([464fc04](https://github.com/alibaba/ChatUI/commit/464fc04d06dd9206927d65d4b0b9c2cb65d7d30b))

## [1.0.0-beta.2](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.1...v1.0.0-beta.2) (2021-05-24)


### Features

* [Price] support `locale` ([731ad59](https://github.com/alibaba/ChatUI/commit/731ad59d60058d869b72bc15b8ff1e31751550db))
* [Time] export `Time` ([1e6ad64](https://github.com/alibaba/ChatUI/commit/1e6ad6476eaa6b0c64226c1332c20cc2ba57e9e9))


### Bug Fixes

* [Recorder] use passive listeners ([5cb0d7a](https://github.com/alibaba/ChatUI/commit/5cb0d7a2277e7dd651de06ec41acc89be2e9aad4))
* [SendConfirm] remove `S--modalOpen` after close SendConfirm ([f0b95da](https://github.com/alibaba/ChatUI/commit/f0b95da19b307a669e224e95c766486e76183522))
* [useMount] update `useEffect`'s deps ([d44f686](https://github.com/alibaba/ChatUI/commit/d44f6864a18654acde787d8ef5cd127640e9eeb8))
* reset `p` `ul` `ol` `h1-5` ([ef8c817](https://github.com/alibaba/ChatUI/commit/ef8c817aebcadbe83226ee7af00acc27a968d3c4))

## [1.0.0-beta.1](https://github.com/alibaba/ChatUI/compare/v1.0.0-beta.0...v1.0.0-beta.1) (2021-05-21)


### Bug Fixes

* [useClickOutside] fix types ([0ca4d86](https://github.com/alibaba/ChatUI/commit/0ca4d86826cb0d10be032a48267ec28fcd49862f))
* [useMessages] fix message id of the `initialMsgs` ([f5ee9eb](https://github.com/alibaba/ChatUI/commit/f5ee9ebcbae318971245cb6e1f73b4d7054b436c))
* use passive listeners ([d534cf9](https://github.com/alibaba/ChatUI/commit/d534cf9c0990242b63939378e9be0390905864cf))

## [1.0.0-beta.0](https://github.com/alibaba/ChatUI/compare/v1.0.0-alpha.2...v1.0.0-beta.0) (2021-05-19)


### Features

* [style] remove reboot.less ([0992f81](https://github.com/alibaba/ChatUI/commit/0992f8108086796f45049ea02ab07cf2ac72290c))


### Bug Fixes

* [Goods] update `.Goods-name`'s `font-size` to `[@font-size-sm](https://github.com/font-size-sm)` ([11c222a](https://github.com/alibaba/ChatUI/commit/11c222a01609f03b543cb3ad576f75594bbaf8da))
* [Message] adjust the spacing ([a8fb76b](https://github.com/alibaba/ChatUI/commit/a8fb76b259749aa7008983e213714c92a69ac3c7))
* types ([d06821e](https://github.com/alibaba/ChatUI/commit/d06821e599308ad1f70d6789baaa21507a3fe10e))

## [1.0.0-alpha.2](https://github.com/alibaba/ChatUI/compare/v1.0.0-alpha.1...v1.0.0-alpha.2) (2021-05-14)


### ⚠ BREAKING CHANGES

* [RateActions] change `good/bad` to `up/down`

### Features

* [Button] support `icon` prop ([dc03585](https://github.com/alibaba/ChatUI/commit/dc035851ddec48ce92bddbf40ccfbdb1da379976))
* [Goods] support `children` ([f0f7ef0](https://github.com/alibaba/ChatUI/commit/f0f7ef03c0f7da254488f1183c5beedc0561cc6e))
* [Navbar] support all IconButton's props ([4096ee3](https://github.com/alibaba/ChatUI/commit/4096ee30febbe85b82232fe908965661e91466f3))
* [Portal] `container` support ref object ([a372694](https://github.com/alibaba/ChatUI/commit/a372694f33becd776e80760c6a1f314cc276b799))
* [Toast]  add `aria` attrs ([8bcfe25](https://github.com/alibaba/ChatUI/commit/8bcfe252cf82c31a2f9f00c5b0674b03bbb8586b))
* [useLocale] support fallback ([f0aebb5](https://github.com/alibaba/ChatUI/commit/f0aebb5bd4d87a3d00863323c252c94560c75df2))
* add `useForwardRef` ([669531a](https://github.com/alibaba/ChatUI/commit/669531aa726855df06f68f665d5c9e947a101b36))
* UI 5.0 ([9d981c0](https://github.com/alibaba/ChatUI/commit/9d981c0e834601bf3b592153944b8b04a7e13bc2))


### Bug Fixes

* [Message] apply `user.name` to avatar ([3346bf9](https://github.com/alibaba/ChatUI/commit/3346bf94a0953d08db516ebf293d563f948b5ac2))
* [Notice] update the logic of checking the number of lines ([1fcd117](https://github.com/alibaba/ChatUI/commit/1fcd117762057dce7bb284168f0dd6ee4deb037a))
* [RateActions] set default title ([fb4d2aa](https://github.com/alibaba/ChatUI/commit/fb4d2aa8927d867c52170d3c84289154c68e0130))
* types ([4223bd6](https://github.com/alibaba/ChatUI/commit/4223bd6854631513d7426ee997a19f4e7419bcb5))

## [1.0.0-alpha.1](https://github.com/alibaba/ChatUI/compare/v1.0.0-alpha.0...v1.0.0-alpha.1) (2021-05-06)


### Features

* add Search ([cab51f0](https://github.com/alibaba/ChatUI/commit/cab51f054217e57d3adff49b7debcf4c7306d5bb))
* UI 5.0 ([36de579](https://github.com/alibaba/ChatUI/commit/36de5790042bf1b5543fdb0e27d412a1cb0d3c65))


### Bug Fixes

* [Popover] use `useClickOutside` with `mousedown` ([42fb5b7](https://github.com/alibaba/ChatUI/commit/42fb5b7270956b962fa7f64b70175909ac97de65))

## [1.0.0-alpha.0](https://github.com/alibaba/ChatUI/compare/v0.3.2...v1.0.0-alpha.0) (2021-04-29)


### Features

* [RichText] export DOMPurify ([730b626](https://github.com/alibaba/ChatUI/commit/730b626be77f4303108e3f272d646dcb75240fef))
* UI 5.0 ([898ba2d](https://github.com/alibaba/ChatUI/commit/898ba2de2fe3bec44a7bf6eb3cf77a9f23620399))


### Bug Fixes

* [useMessages] support `msg.hasTime` ([bd01305](https://github.com/alibaba/ChatUI/commit/bd013056ac7cdf52b43e030da9670c1c9e07a8fb))

### [0.3.3](https://github.com/alibaba/ChatUI/compare/v0.3.2...v0.3.3) (2021-05-14)

* [Composer] fix the condition of render `SendConfirm` ([0aea136](https://github.com/alibaba/ChatUI/commit/0aea1367866903d06d500753d7aa5aa107d6c198))
* [Popover] use `useClickOutside` with `mousedown` ([a8119d6](https://github.com/alibaba/ChatUI/commit/a8119d69704583a1babaa8848b5acfa46674aa75))

### [0.3.2](https://github.com/alibaba/ChatUI/compare/v0.3.1...v0.3.2) (2021-04-14)


### Features

* [useMessages] add `resetList` method ([5371ea5](https://github.com/alibaba/ChatUI/commit/5371ea52a60bde902cc300b28e245bc769266471))


### Bug Fixes

* [importScript] add `crossorigin` to script tag ([935d074](https://github.com/alibaba/ChatUI/commit/935d074b5838d214782ec8854b079f6512abc22e))
* [Modal] fix  `tabIndex` ([b729ad4](https://github.com/alibaba/ChatUI/commit/b729ad43f8132ac299b57dad632ea92e4fa7655e))

### [0.3.1](https://github.com/alibaba/ChatUI/compare/v0.3.1-beta.2...v0.3.1) (2021-03-30)


### Bug Fixes

* [Bubble] explicitly make the text selectable ([3271079](https://github.com/alibaba/ChatUI/commit/3271079bc9d62d0a2b18205810a499ef8f4a08b7))

### [0.3.1-beta.2](https://github.com/alibaba/ChatUI/compare/v0.3.1-beta.1...v0.3.1-beta.2) (2021-03-09)

### [0.3.1-beta.1](https://github.com/alibaba/ChatUI/compare/v0.3.1-beta.0...v0.3.1-beta.1) (2021-03-09)


### Features

* [LazyComponent] export `LazyComponentOnLoadParams` ([3bfd3fe](https://github.com/alibaba/ChatUI/commit/3bfd3fe686c272a39e7f4d7648902efce4727b0f))
* [useComponents] add `hasComponent` method ([d933955](https://github.com/alibaba/ChatUI/commit/d9339559234c9e4515f1a90660d916fb647cb477))


### Bug Fixes

* [ComponentsProvider] support updating `props.components` ([524401d](https://github.com/alibaba/ChatUI/commit/524401d18e95a3bb786ef19c246c01bb118fd39c))

### [0.3.1-beta.0](https://github.com/alibaba/ChatUI/compare/v0.3.0...v0.3.1-beta.0) (2021-03-08)


### Bug Fixes

* [LazyComponent] add `onLoad` event to decorator, and resolve circular dependencies ([e7d76d1](https://github.com/alibaba/ChatUI/commit/e7d76d12e01732c018ebbe26a36ffea9683d3f52))

## [0.3.0](https://github.com/alibaba/ChatUI/compare/v0.3.0-beta.5...v0.3.0) (2021-03-01)

## [0.3.0-beta.5](https://github.com/alibaba/ChatUI/compare/v0.3.0-beta.4...v0.3.0-beta.5) (2021-01-27)


### Features

* [RichText] add `data-cui-href` when the `href` of the link is removed ([29fde15](https://github.com/alibaba/ChatUI/commit/29fde15b80b3532c57ff948deeb8b02a3332c8a3))

## [0.3.0-beta.4](https://github.com/alibaba/ChatUI/compare/v0.3.0-beta.3...v0.3.0-beta.4) (2021-01-22)


### Bug Fixes

* [Composer] the input box displays one row by default ([2d6d636](https://github.com/alibaba/ChatUI/commit/2d6d6364ce6e4b4459e4cd7d81a9c1ecdce461f9))

## [0.3.0-beta.3](https://github.com/alibaba/ChatUI/compare/v0.3.0-beta.1...v0.3.0-beta.3) (2021-01-19)


### ⚠ BREAKING CHANGES

* rename `.Carousel-indicators` to `.Carousel-dots`

### Features

* [Carousel] apply `will-change: transform` to `.Carousel-inner` ([4961f54](https://github.com/alibaba/ChatUI/commit/4961f54b872fc4acabc270441cfac0a99534f27d))
* [Carousel] export `CarouselHandle` ([05d9eeb](https://github.com/alibaba/ChatUI/commit/05d9eebd3e662773b49e021eb24ff3915aa40c25))
* [Carousel] move the dots to the middle bottom of the slide ([1d71888](https://github.com/alibaba/ChatUI/commit/1d71888e1bcd3d8db738f573671626e21b0df24c))
* [RichText] set `a` element owning target to `target=_blank` ([cb83871](https://github.com/alibaba/ChatUI/commit/cb83871a4b08c91b132349751307234586ce5af1))
* add `ComponentsProvider`, `LazyComponent` ([53b3802](https://github.com/alibaba/ChatUI/commit/53b38021a862264ec3918878a0a03caff037760f))


### Bug Fixes

* [Carousel] pause when hover dot ([424840d](https://github.com/alibaba/ChatUI/commit/424840dcf292dfb8e8c27335f4f757fa81d2aaae))
* [Carousel] separate touch and mouse events ([a533584](https://github.com/alibaba/ChatUI/commit/a533584bc738d5c73e02e18139b848fc24da9bae))
* [ErrorBoundary] update `FallbackProps` ([62fe62c](https://github.com/alibaba/ChatUI/commit/62fe62cd76be2348ae08fa9bfa8bfc6f55b1b423))

## [0.3.0-beta.2](https://github.com/alibaba/ChatUI/compare/v0.3.0-beta.1...v0.3.0-beta.2) (2021-01-18)


### ⚠ BREAKING CHANGES

* rename `.Carousel-indicators` to `.Carousel-dots`

### Features

* [Carousel] apply `will-change: transform` to `.Carousel-inner` ([4961f54](https://github.com/alibaba/ChatUI/commit/4961f54b872fc4acabc270441cfac0a99534f27d))
* [Carousel] move the dots to the middle bottom of the slide ([1d71888](https://github.com/alibaba/ChatUI/commit/1d71888e1bcd3d8db738f573671626e21b0df24c))
* [RichText] set `a` element owning target to `target=_blank` ([cb83871](https://github.com/alibaba/ChatUI/commit/cb83871a4b08c91b132349751307234586ce5af1))
* add `ComponentsProvider`, `LazyComponent` ([53b3802](https://github.com/alibaba/ChatUI/commit/53b38021a862264ec3918878a0a03caff037760f))


### Bug Fixes

* [Carousel] pause when hover dot ([424840d](https://github.com/alibaba/ChatUI/commit/424840dcf292dfb8e8c27335f4f757fa81d2aaae))

## [0.3.0-beta.1](https://github.com/alibaba/ChatUI/compare/v0.3.0-beta.0...v0.3.0-beta.1) (2021-01-18)


### Features

* [Carousel] support `clickDragThreshold` props ([08b6916](https://github.com/alibaba/ChatUI/commit/08b6916acc0b4fc617e03caed4b9b4f26c25b37d))

## [0.3.0-beta.0](https://github.com/alibaba/ChatUI/compare/v0.2.2...v0.3.0-beta.0) (2021-01-17)


### Features

* [Carousel] refactor with hooks ([73a564b](https://github.com/alibaba/ChatUI/commit/73a564b2eeb6c28df3f20a28f1a968498e347ceb))
* [ErrorBoundary] support `onError` props ([9bb1b73](https://github.com/alibaba/ChatUI/commit/9bb1b7392416179ee33687bf98fdba98c79b1be6))
* [utils] export `importScript` and `lazyComponent` ([3770ee3](https://github.com/alibaba/ChatUI/commit/3770ee34c61f1ae62a94fbc42d60c7fd9fa9e39e))
* [utils] export `mountComponent` ([735e869](https://github.com/alibaba/ChatUI/commit/735e869a0140e7bb9dedcc5c0862be4f5d68b33c))

### [0.2.2](https://github.com/alibaba/ChatUI/compare/v0.2.2-beta.0...v0.2.2) (2021-01-08)


### Features

* [ErrorBoundary] support `FallbackComponent` props ([a1915a7](https://github.com/alibaba/ChatUI/commit/a1915a77552b47c9ce72fc967ecae1052ffb1c94))


### Bug Fixes

* [Video] show the play button when the cover is specified ([3d91644](https://github.com/alibaba/ChatUI/commit/3d91644c7db7089911bfcd9af491d49d993f1404))

### [0.2.2-beta.0](https://github.com/alibaba/ChatUI/compare/v0.2.1...v0.2.2-beta.0) (2021-01-03)


### Features

* [Composer] add `data-action-icon` to `.Composer-actions` ([76add41](https://github.com/alibaba/ChatUI/commit/76add417e6c8a9317c5d303165440988638cef92))
* [Composer] show toolbar toggle button when typing ([11b850b](https://github.com/alibaba/ChatUI/commit/11b850b6170f3f84881e72a8b43eb705ed1c7714))

### [0.2.1](https://github.com/alibaba/ChatUI/compare/v0.2.0...v0.2.1) (2020-12-17)


### Bug Fixes

* [Composer] Revert .ChatFooter's z-index ([9a855b0](https://github.com/alibaba/ChatUI/commit/9a855b0eeee104b8693380679faeba69dbfadf33))
* [RateActions] Reduce .RateActions' z-index ([0ca468e](https://github.com/alibaba/ChatUI/commit/0ca468e0f06406b2e9c02c1cb7e22795201f8c01))

## [0.2.0](https://github.com/alibaba/ChatUI/compare/v0.1.5...v0.2.0) (2020-12-16)


### Features

* [Composer] Change the send button and plus sign to mutually exclusive display ([de48814](https://github.com/alibaba/ChatUI/commit/de4881497cd0588b3e05d45477e978e279075cd4))


### Bug Fixes

* [Input] update `enterKeyHint`'s type ([a880eac](https://github.com/alibaba/ChatUI/commit/a880eac5329c4a674a2ad0b504a75bfc8c4b5b68))

### [0.1.5](https://github.com/alibaba/ChatUI/compare/v0.1.4...v0.1.5) (2020-12-08)


### Features

* **list:** add bordered style ([c30961a](https://github.com/alibaba/ChatUI/commit/c30961aba1a92c27b24acb146398be0e41a5783f))
* **quick-replies:** use `item.code` as the `key` for items ([f0ca636](https://github.com/alibaba/ChatUI/commit/f0ca636dc70491886389d3cc48b8a18692eb64b2))
* **scroll-view:** add props `itemKey` to specify keys for items ([5de57ca](https://github.com/alibaba/ChatUI/commit/5de57ca6f98374a79dd21b658cb9a9435d1a6116))


### Bug Fixes

* change ChatFooter's z-index ([646cbb5](https://github.com/alibaba/ChatUI/commit/646cbb54ed4c667fad8a9873c93dac4cf800b8f8))

### [0.1.4](https://github.com/alibaba/ChatUI/compare/v0.1.3...v0.1.4) (2020-11-24)


### Bug Fixes

* **tooltip:** fix tooltip's z-index ([bfdd1a2](https://github.com/alibaba/ChatUI/commit/bfdd1a255388abdbf443fdbb24ed6efcc7c9bf0e))

### [0.1.3](https://github.com/alibaba/ChatUI/compare/v0.1.2...v0.1.3) (2020-11-16)


### Features

* **modal:** auto focus by default ([1fe056e](https://github.com/alibaba/ChatUI/commit/1fe056eefe5f95ae4f87b486df732aa8eca8c622))


### Bug Fixes

* change `enterkeyhint` to `enterKeyHint` ([270f8fc](https://github.com/alibaba/ChatUI/commit/270f8fc419920d54aa9d7288486cb82d3244798a))

### [0.1.2](https://github.com/alibaba/ChatUI/compare/v0.1.1...v0.1.2) (2020-11-05)

### 0.1.1 (2020-10-22)

* optimize `useMessages` ([38fd498](https://github.com/alibaba/ChatUI/commit/38fd498f4f96e4e6129523f1936794269bcedba3))
* add `tabIndex` to ModalBase

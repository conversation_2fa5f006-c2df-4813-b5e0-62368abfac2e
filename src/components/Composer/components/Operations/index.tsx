import React from 'react';
import { Button } from '@ht/sprite-ui';
import type { Operation } from '../type';

import './style.less';

export interface OperationsProps {
  operations: Operation[];
  // onOperationClick: (operation: Operation) => void;
}

export default function Operations(props: OperationsProps) {
  const { operations } = props;

  const OperationItemRender = (operation: Operation) => {
    if (operation.customRender) {
      return operation.customRender;
    }

    const onOperationClick = () => {
      if (operation.onClick && !operation.disabled) {
        console.log('onOperationClick:', operation);
        operation.onClick(operation);
      }
    };
    return (
      <Button
        className="operationButton"
        disabled={operation.disabled}
        onClick={onOperationClick}
      >
        {operation.icon}
        {operation.label}
      </Button>
    );
  };
  return (
    <div className="operations">
      {operations.map((item) => OperationItemRender(item))}
    </div>
  );
}

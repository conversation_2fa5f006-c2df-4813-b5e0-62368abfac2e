import { UploadFile, UploadProps } from './wide/Upload';
import { InputProps } from '../Input';
import  { type SelectProps } from './wide/Select';
import type { Skill, InputType as InputCardType, InputContent, Operation, ComposerType, ComposerCustomProps } from "./components/type"
import {  RecorderProps } from '../Recorder';
import { ILogParams } from '../../LogPointConfigMap';

export type InputType = 'voice' | 'text';

export type ComposerProps = {
  // wideBreakpoint?: string;
  isWide?: boolean;
  text?: string;
  inputOptions?: InputProps;
  placeholder?: string;
  inputType?: InputType;
  onInputTypeChange?: (inputType: InputType) => void;
  recorder?: RecorderProps;
  onSend?: (type: string, content: string, payload?: object, transformedFiles?: UploadFile[]) => Promise<boolean>;
  onImageSend?: (file: File) => Promise<any>;
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  onChange?: (value: string, event: React.ChangeEvent<Element>) => void;
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;

  toolbar?: ToolbarItemProps[];
  onToolbarClick?: (item: ToolbarItemProps, event: React.MouseEvent) => void;
  onAccessoryToggle?: (isAccessoryOpen: boolean) => void;

  extraAction?: React.ReactNode;
  showStopAnswer?: boolean;
  onStopAnswer?: () => void;
  showInternetSearch?: boolean;
  enableInternetSearch?: boolean;
  toggleEnableInternetSearch?: (enable: boolean) => void;
  aboveNode?: React.ReactNode;
  quickNewConversation?: boolean;
  quickOpenHistory?: boolean;
  handleNewConversation?: () => void;
  toggleHistory?: () => void;
  belowNode?: React.ReactNode;
   /**
     * log上报回调
     */
  onConsolidateReports?: (params: ILogParams) => void;
  llmConfig?: {
    selectProps?: Omit<SelectProps, 'onChange'>;
    llmOptions?: { value: string | number; label: string; reasoning?: boolean }[];
    onLLMSwitch?: (value: string) => void;
  };
  uploadConfig?: UploadProps;
  // todo
  showPictureUpload?: boolean;
  pictureConfig?: any;

  //2025.08.05 add by 016255
  skills?: Skill[]; // 输入框上方的快捷技能栏
  operationConfig:Record<InputCardType, Operation[]>; // 特殊输入对象（如文件、图片、网页等）下方的操作按钮
  showThinking?: boolean // 是否显示思考按钮，为true时会根据llmConfig.llmOptions.reasoning值是否显示高亮
};



export type ComposerConfigProps = {
  uploadConfig?: UploadProps;
  showInternetSearch?: boolean;
  text?: string;
  aboveNode?: React.ReactNode;
  quickNewConversation?: boolean;
  quickOpenHistory?: boolean;
  handleNewConversation?: () => void;
  toggleHistory?: () => void;
  belowNode?: React.ReactNode;
  placeholder?: string;
  extraAction?: React.ReactNode;
  showThinking?: boolean;
  quoteOperations?: {
    text?: Operation[],
    web?: Operation[],
    image?: Operation[],
    file?: Operation[],
  };
  skill?: Skill[]; // 快捷技能配置
}

export interface ComposerHandleProps {
  setText: (text: string) => void;
  setComposer?: (type: ComposerType, composerProps: ComposerCustomProps) => void;
  setCite?: (type: InputCardType, content: InputContent) => void;
}
@avatar-gap: 4px;

.Message {
  position: relative;

  & + & {
    margin-top: 30px;
  }
  &.left {
    .Message-main {
      & > .Avatar {
        margin-right: @avatar-gap;
      }
    }
    .Bubble {
      // background: #f7f7f7;
      background: @bubble-left-bg;
      padding-left: @bubble-left-padding-left;
      padding-right: @bubble-left-padding-right;
      padding-top: @bubble-left-padding-top;
      padding-bottom: @bubble-left-padding-bottom;
       &.card {
        margin-right: 0;
        overflow-x: auto;
        width: 100%;
        // background: #fff;
        background: @bubble-left-bg;

        .Feedback {
          padding: 15px;
        }

        .HallucinationMarker {
          padding-left: 15px;
        }
      }
      .markdown-body {
        color: @bubble-left-color;
      }
    }
  }
  &.right {
    .Message-main,
    .Message-content {
      flex-direction: row-reverse;
    }
    .Message-main {
      & > .Avatar {
        margin-left: @avatar-gap;
      }
    }
    .Message-author {
      text-align: right;
    }
    .Bubble {
      margin-left: 55px;
      border-radius: 12px;
      background: @bubble-right-bg;
      color: #333;
      padding-left: @bubble-right-padding-left;
      padding-right: @bubble-right-padding-right;
      padding-top: @bubble-right-padding-top;
      padding-bottom: @bubble-right-padding-bottom;
      .markdown-body {
        color: @bubble-right-color;
        
        > p {
          white-space: pre-line;
        }
      }
    }

    .Unsend {
      background: linear-gradient(270deg, rgba(84, 182, 255, 0.5) 0%, rgba(0, 128, 255, 0.5) 46%, rgba(205, 142, 237, 0.5) 100%);
    }

  }
  &.pop {
    display: none;
  }
}

.Message-meta {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
  text-align: center;
}

.Message-main,
.Message-content {
  display: flex;
  align-items: flex-start;
}

.Message-inner {
  flex: 1;
  min-width: 0;
}

.Message-stopped {
  font-size: 12px;
  color: #999;
  line-height: 16px;
  margin-top: 10px;
  padding-left: 15px;
}

.Message-sendError {
  font-size: 14px;
  color: #666;
  line-height: 19px;
  margin-top: 12px;
  display: flex;
  align-items: center;

  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

.feedbackWrapper {
  display: flex;
  align-items: center;
  .messageDelete {
    margin-top: 8px;
    margin-left: 16px;
    cursor: pointer;
    display: inline-block;
    .deleteIcon {
      width: 14px;
      height: 14px;
      border-radius: 0;
    }
  }
}

// 右侧消息的删除按钮右对齐
.Message.right .messageDelete {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.Message-author {
  margin-bottom: 6px;
  color: @gray-2;
  font-size: @font-size-xs;
  line-height: 1.1;
}

// SystemMessage
.SystemMessage {
  padding: 0 15px;
  font-size: @font-size-xs;
  color: @gray-2;
  text-align: center;

  a {
    margin-left: 5px;
  }
}

.SystemMessage-inner {
  display: inline-block;
  padding: 6px 9px;
  border-radius: 6px;
  background: @gray-8;
  text-align: left;
}

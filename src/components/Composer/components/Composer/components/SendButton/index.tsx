import React from 'react';
import Send0 from '../../svg/send0.svg';
import Send1 from '../../svg/send1.svg';
import './style.less';

export interface SendButtonProps {
  hasContent: boolean;
  onClick: () => Promise<void>;
}
export default function SendButton(props: SendButtonProps) {
  const { hasContent, onClick } = props;
  const [disabled, setDisable] = React.useState(false);

  const onButtonClick = async () => {
    if (disabled || !hasContent) {
      return;
    }
    try {
      setDisable(true);
      await onClick();
      setDisable(false);
    } catch (e) {
      console.warn('SendButton.onButtonClick error:', e);
    }
  };
  return (
    <img
      className={hasContent && !disabled ? 'send1' : 'send0'}
      src={hasContent ? Send1 : Send0}
      onClick={onButtonClick}
    />
    // <Button
    //   className='send'
    //   disabled={disabled || !hasContent}
    //   icon={<img src={hasContent ? Send1 : Send0} />}
    //   onClick={onButtonClick}
    // />
  );
}

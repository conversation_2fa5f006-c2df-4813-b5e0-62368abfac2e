.List {
  background: @list-bg;

  &--bordered {
    border: @list-border-width solid @list-border-color;
    border-radius: @list-border-radius;
  }
}

.ListItem {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: @list-item-padding;
  border: 0;
  color: @list-item-color;
  font-size: @list-item-font-size;
  line-height: @list-item-line-height;
  text-decoration: none;
  transition: 0.3s;

  &:focus:not(:focus-visible) {
    outline: 0;
  }
  & + & {
    border-top: @list-border-width solid @list-border-color;
  }
  .Icon {
    color: @list-item-icon-color;
  }
}

button.ListItem {
  width: 100%;
  background: transparent;
  text-align: left;
  appearance: none;
}

a.ListItem,
button.ListItem {
  &:active {
    background: @list-item-active-bg;
  }
}

@media (hover: hover) {
  a.ListItem,
  button.ListItem {
    &:hover {
      background: @list-item-hover-bg;
      background-clip: padding-box;
      cursor: pointer;
    }
  }
}

.ListItem-content {
  flex: 1;
}

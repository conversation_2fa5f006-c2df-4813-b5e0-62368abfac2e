// 内置技能配置code集合
export const innerSikllCodeList = [
  // 翻译 (ComposerType = 'translate')
  'translate',
  // 网页总结 (ComposerType = 'web')
  // 'summary', // TODO:暂不支持
  // 默认智能体(ComposerType = 'web')
  'default',
  // 缩写(ComposerType = 'common')
  'text-condenser',
  // 扩写(ComposerType = 'common')
  'text-expander',
  // 润色(ComposerType = 'common')
  'text-polisher',
  // 修正拼写和语法(ComposerType = 'common')
  'grammar-corrector',
];

// 展示文本内置输入框code集合
export const showTexComposerCodes = [
  'text-condenser',
  'text-expander',
  'text-polisher',
  'grammar-corrector',
  'default'
];
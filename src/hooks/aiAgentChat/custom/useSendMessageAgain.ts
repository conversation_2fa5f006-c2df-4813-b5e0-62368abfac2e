/*
 * @Author: 016645
 * @Description: 对话问答方法
 * @Date: 2025-03-25 09:28:00
 */
import { useState, useEffect, useCallback } from 'react';
import { getUrl, fetchData, fetchSSE, parseSSEDataCustom } from '../utils';
import { SendMessageAgain } from '../types';

/**
 * 重新生成回复返回数据类型
 */
type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId?: string;
    messageId?: string;
    list: Array<{type: string, content: object, taskId?: string}>;
  };
}

/**
 * 重新生成回复并处理响应的自定义Hook
 * @param options - 配置选项
 * @returns 包含重新生成回复方法和状态的对象
 */
const useSendMessageAgain = (initOptions: SendMessageAgain) => {
  // 解构配置参数
  const { requestConfig, requestParams } = initOptions;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    url = '',          // 请求地址
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,           // 请求头
    isAibag = false,
  } = requestConfig;
  
  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // 获取完整url
  const fullUrl = getUrl(baseUrl, url);

  /**
   * 发送消息方法
   */
  const run = useCallback(async (customParams?: any) => {
    // 开始加载
    setLoading(true);
    setError(null);
    let rawData = '';

    // 解构请求参数
    const { 
      appId, 
      userId, 
      conversationId,
      messageId,
      stream,
    } = Object.assign({}, requestParams, customParams);
    
    try {

      // 初始化请求数据
      const initRequestData = {
        appId,
        userId,
        conversationId,
        messageId,
      };
      
      // 应用请求转换器
      const requestData = requestTransfer 
        ? await requestTransfer(initRequestData) 
        : initRequestData;
      
      // 根据stream参数处理流式/非流式请求
      if (stream) {
        // 流式请求处理
        await fetchSSE({
          url: fullUrl,
          data: requestData,
          headers,
        }, {
          onMessage: (resultData) => {
            // 拼接sse接口字符串
            rawData += resultData;
            const responseData = parseSSEDataCustom(rawData, responseTransfer, isAibag);
            
            if (!responseData) return;
            
            setData({
              code: '0',
              msg: 'success',
              resultData: responseData,
            });
          },
          onEnd: () => setLoading(false),
        });
      } else {
        // 非流式请求处理
        const responseData = await fetchData({
          url: fullUrl,
          data: requestData,
          headers,
        });
        
        // 应用响应转换器并更新状态
        const result = responseTransfer 
          ? responseTransfer(responseData) 
          : responseData;
        
        setData(result);
        setLoading(false);
      }
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('发送消息失败')
      );
      setLoading(false);
    }
  }, [
    fullUrl,
    requestTransfer, 
    responseTransfer, 
    headers, 
    requestParams
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useSendMessageAgain;
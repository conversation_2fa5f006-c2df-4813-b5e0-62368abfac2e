.tooltip-wrapper {
  display: inline-block;
  position: relative;
  /* 确保子元素绝对定位相对于此容器 */
}

.tooltip {
  position: absolute;
  /* 相对于 tooltip-wrapper 定位 */
  z-index: 9999;
  min-width: 30px;
  max-width: 150px;
  width: max-content;
  padding: 6px 8px;
  color: var(--tooltip-color, #fff);
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  background-color: var(--tooltip-bg, rgba(0, 0, 0, 0.85));
  border-radius: var(--tooltip-radius, 8px);
  box-shadow: var(--tooltip-shadow, 0 2px 8px rgba(0, 0, 0, 0.15));
  animation: fadeIn 0.2s ease-out;
  word-break: break-all;
}

.tooltip-arrow::before {
  // content: '';
  // position: absolute;
  // width: 8px;
  // height: 8px;
  // background: var(--arrow-color, var(--tooltip-bg, rgba(0, 0, 0, 0.85)));
  // transform: rotate(45deg);
}

/* 箭头定位 */
.tooltip-arrow.top::before {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

.tooltip-content {
  position: relative;
  z-index: 1;
}

.tooltip-arrow {
  // position: absolute;
  // width: 8px;
  // height: 8px;
  // background: rgba(0, 0, 0, 0.77);
  // transform: rotate(45deg);
  // z-index: 0;
}

/* 箭头位置 */
.tooltip-arrow.top {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow.topLeft {
  bottom: -6px;
  left: 16px;
}

.tooltip-arrow.topRight {
  bottom: -6px;
  right: 16px;
}

.tooltip-arrow.bottom {
  top: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow.bottomLeft {
  top: -6px;
  left: 16px;
}

.tooltip-arrow.bottomRight {
  top: -6px;
  right: 16px;
}

.tooltip-arrow.left {
  right: -6px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.tooltip-arrow.leftTop {
  right: -6px;
  top: 16px;
}

.tooltip-arrow.leftBottom {
  right: -6px;
  bottom: 16px;
}

.tooltip-arrow.right {
  left: -6px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.tooltip-arrow.rightTop {
  left: -6px;
  top: 16px;
}

.tooltip-arrow.rightBottom {
  left: -6px;
  bottom: 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

import React from 'react';
import { Message, Icon } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';

export default () => (
  <div className="message-demo">
    <Message
      _id="1"
      messageId="1"
      type="typing"
      content={{ text: '你好' }}
      position="left"
      config={{}}
      updateMsg={() => {}}
      customThinkingStyle={<div><Icon type="bullhorn" style={{marginRight: '8px'}} />正在思考中....</div>}
    />
    <Message
      _id="2"
      messageId="2"
      type="thinking"
      thinkContent={'以下为思考的内容'}
      position="left"
      config={{}}
      updateMsg={() => {}}
      isThinking={true}
      customDeepThinkingStyle={<div><Icon type="bullhorn" style={{marginRight: '8px'}} />正在深度思考中...</div>}
    />
  </div>
); 
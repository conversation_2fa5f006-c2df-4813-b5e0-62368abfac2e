.Divider {
  display: flex;
  align-items: center;
  margin: 12px 0;
  font-size: @font-size-xs;
  color: @gray-3;

  &:before,
  &:after {
    content: '';
    display: block;
    flex: 1;
    border-top: 1px solid @gray-5;
  }
}

.Divider--text-center,
.Divider--text-left,
.Divider--text-right {
  &:before {
    margin-right: @gutter;
  }
  &:after {
    margin-left: @gutter;
  }
}

.Divider--text-left {
  &:before {
    max-width: 10%;
  }
}

.Divider--text-right {
  &:after {
    max-width: 10%;
  }
}

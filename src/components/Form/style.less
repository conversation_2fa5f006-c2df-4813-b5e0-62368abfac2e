.Form {
  background: @white;

  &.is-light {
    // background: @gray-7;

    .FormItem {
      padding: 0;
    }
    .Label,
    .HelpText {
      padding: 0 @gutter;
    }
  }
}

.FormItem {
  position: relative;
  padding: 0 @gutter;

  & + & {
    margin-top: 20px;
  }
  &.required {
    .Label:after {
      content: '*';
      display: inline-block;
      color: @red;
      font-size: @font-size-sm;
      font-family: SimSun,sans-serif;
      line-height: 1;
      vertical-align: middle;
    }
  }
  &.is-invalid {
    .Label,
    .HelpText {
      color: @red;
    }
    .Input {
      border-color: @red;
    }
  }
  .RadioGroup,
  .CheckboxGroup {
    margin-top: 10px;
  }
  .Label + .Input {
    margin-top: 5px;
  }
}

.FormActions {
  display: flex;
  padding: 10px @gutter;
  background: @white;

  .Btn {
    flex: 1;
  }
  .Btn + .Btn {
    margin-left: 6px;
  }
}

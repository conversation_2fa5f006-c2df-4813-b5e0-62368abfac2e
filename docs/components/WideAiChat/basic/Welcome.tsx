import React from 'react';
import './index.less';

export default function CustomWelcome() {
  const mockData = [
    { prefix: '每日咨询', text: '中国一季度GDP数据公布，经济复苏态势稳健', link: '' },
    { prefix: '每日咨询', text: '美联储降息预期推迟，全球市场波动加剧', link: '' },
  ];
  const handleOpenLink = (value: string) => {
    window.open(value, '_blank');
  };
  const renderLink = (value: { text: string; prefix: string; link: string }) => {
    return (
      <div key={value.text} className="item">
        <div
          onClick={() => handleOpenLink(value.link)}
        >{`【${value.prefix}】${value.text}`}</div>
      </div>
    );
  };
  return (
    <div className="CustomWelcome GuidePagePC">
      <div className="title">hello，我是智能助手小萤！</div>
      <div className="card">
        <div className="header">
          <div className="title">2025.2.26资讯信息</div>
          <div className="extra">
            <span>查看全部</span>
          </div>
        </div>
        <div className="content">
          <div className="list">{mockData.map(renderLink)}</div>
        </div>
      </div>
    </div>
  );
}

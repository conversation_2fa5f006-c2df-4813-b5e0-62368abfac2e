.Carousel {
  position: relative;
  overflow: hidden;

  &--draggable {
    .Carousel-inner {
      touch-action: pan-y;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }
  }
  &--rtl {
    direction: rtl;
  }
  &--dragging {
    .Carousel-item {
      pointer-events: none;
    }
  }
  &-inner {
    display: flex;
    will-change: transform;
  }
  &-dots {
    position: absolute;
    z-index: 1;
    bottom: @carousel-dots-bottom;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    list-style-type: none;
    margin: 0;
    padding: 0;
  }
  &-dot {
    display: block;
    width: @carousel-dot-width;
    height: @carousel-dot-height;
    margin: @carousel-dot-margin;
    padding: @carousel-dot-padding;
    border: @carousel-dot-border;
    border-radius: @carousel-dot-border-radius;
    background: @carousel-dot-bg;
    transition: @carousel-dot-transition;
    cursor: pointer;

    &.active {
      background: @brand-1;
    }
  }
}

import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { FileCard } from '../../../src';

// const file = new File(['foo'], 'foo.txt', {
//   type: 'text/plain',
// });

export default () => (
  <DemoPage>
    <DemoSection title="基础用法">
      <FileCard file={{
        "name": "这是一个word文件这是一个word文件.docx",
        "url": "https://c.zhangle.com/airobottest/iwen/material/docs/外籍股权激励账户开立.docx",
        "size": 102400,
      }} />
    </DemoSection>
    <DemoSection title="指定拓展名">
      <FileCard file={{
        "name": "这是第二个word文件.docx",
        "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E5%A4%96%E7%B1%8D%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E8%B4%A6%E6%88%B7%E5%BC%80%E7%AB%8B.docx",
        "size": 102400,
      }} extension="pdf" />
    </DemoSection>
    <DemoSection title="额外内容">
      <FileCard file={{
        "name": "这是一个PDF文件信诚新双盈分级债券型证券投资基金更新招募说明书摘要.pdf",
        "url": "https://aorta.htzq.com.cn/pdf_finchina/FUND/2020/2020-5/2020-05-28/1922768.pdf",
        "size": 102400,
      }}>
        <a href="https://chatui.io/">下载</a>
      </FileCard>
    </DemoSection>
    <DemoSection title="不可预览">
      <FileCard file={{
        "name": "这是一个word文件这是一个word文件.docx",
        "url": "https://c.zhangle.com/airobottest/iwen/material/docs/外籍股权激励账户开立.docx",
        "size": 102400,
      }} preview={false} />
    </DemoSection>
  </DemoPage>
);

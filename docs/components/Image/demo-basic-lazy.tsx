import React from 'react';
import { AiImage } from '@ht/chatui';
import '@ht/chatui/es/styles/index.less';

export default () => {

  const imgs = [
    '//gw.alicdn.com/tfs/TB1yGi2vfb2gK0jSZK9XXaEgFXa-320-240.png',
    '//gw.alicdn.com/tfs/TB1I6i2vhD1gK0jSZFsXXbldVXa-620-320.jpg',
    '//gw.alicdn.com/tfs/TB1GRW3voY1gK0jSZFMXXaWcVXa-620-320.jpg',
    '//gw.alicdn.com/tfs/TB1XCq4veH2gK0jSZFEXXcqMpXa-620-320.jpg',
    '//gw.alicdn.com/tfs/TB1dzG8vbj1gK0jSZFuXXcrHpXa-620-319.jpg',
  ];

  return (
    <div>
      {imgs.map((img) => (
        <div key={img}>
          <p style={{ margin: '120px 0', background: '#eee' }}>placeholder</p>
          <AiImage src={img} style={{ minWidth: '10px', minHeight: '200px' }} lazy fluid key={img} />
        </div>
      ))}
    </div>
  )
}

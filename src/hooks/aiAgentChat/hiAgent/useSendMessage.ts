/*
 * @Author: 016645
 * @Description: hiAgent对话问答方法
 * @Date: 2025-03-25 09:28:00
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { deepMerge, fetchData, fetchSSE, parseSSEData, fetchMS, fetchTcpSSE } from '../utils';
import { SendMessage } from '../types';
import getBaseUrl, { TBaseUrl } from '../../../utils/getBaseUrl';

// 常量定义
const CONSTANTS = {
  DEFAULT_MESSAGE_INTERVAL: 30,
  DEFAULT_PER_MESSAGE_NUM: 1,
  DEFAULT_METHOD: 'post',
  DEFAULT_TYPE: 'http',
  DEFAULT_RESPONSE_MODE: {
    STREAMING: 'streaming',
    BLOCKING: 'blocking',
  },
  API_ENDPOINTS: {
    CHAT_QUERY: '/chat_query',
    CREATE_CONVERSATION: '/create_conversation',
    UPDATE_CONVERSATION: '/update_conversation',
  },
  RESPONSE_CODES: {
    SUCCESS: '0',
  },
  MESSAGE_TYPES: {
    MARKDOWN: 'markdown',
  },
} as const;

// 类型定义
interface ConversationData {
  Conversation?: {
    AppConversationID: string;
  };
  conversation?: {
    appConversationID: string;
  };
}

interface ResponseData {
  parsedAnswer?: string | Array<{ type: string; content: object; taskId?: string }>;
  id?: string;
  task_id?: string;
}

interface FileItem {
  path?: string;
  name?: string;
  size?: number;
  url?: string;
}

interface FileExtends {
  files?: FileItem[];
  Files?: Array<{
    Path?: string;
    Name?: string;
    Size?: number;
    Url?: string;
  }>;
}

interface RequestParams {
  appId: string;
  userId: string;
  conversationId?: string;
  question: string;
  questionExtends?: FileExtends;
  isFirstChat?: boolean;
  stream?: boolean;
  messageInterval?: number;
  inputs?: Record<string, any>;
  perMessageNum?: number;
  appKey: string;
  backUpTalking?: string;
  extendParams?: Record<string, any>;
}

interface SSEHandlers {
  onMessage: (resultData: string) => void;
  onEnd: () => void;
  onError: (err: Error) => void;
}

/**
 * 消息发送返回数据类型
 */
type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId?: string;
    messageId?: string;
    list: Array<{ type: string; content: object; taskId?: string }>;
    endFlag?: boolean;
    relatedQuestionList?: any;
    createTime?: any;
  };
};

/**
 * 构建完整的URL
 */
const buildUrls = (baseUrl: string | TBaseUrl, customUrl?: string, createUrl?: string, updateUrl?: string) => {
  const newBaseUrl = getBaseUrl(baseUrl);
  const baseUrlClean = newBaseUrl.replace(/\/+$/, '');
  
  return {
    fullUrl:  customUrl ? `${baseUrlClean}/${customUrl.replace(/^\/+/, '')}` : `${baseUrlClean}${CONSTANTS.API_ENDPOINTS.CHAT_QUERY}`,
    createConversationUrl: createUrl ? `${baseUrlClean}/${createUrl.replace(/^\/+/, '')}` : `${baseUrlClean}${CONSTANTS.API_ENDPOINTS.CREATE_CONVERSATION}`,
    fullUpdateUrl: updateUrl ? `${baseUrlClean}/${updateUrl.replace(/^\/+/, '')}` : `${baseUrlClean}${CONSTANTS.API_ENDPOINTS.UPDATE_CONVERSATION}`,
  };
};

/**
 * 处理文件数据转换
 */
const processFileData = (questionExtends?: FileExtends): FileExtends | undefined => {
  if (!questionExtends?.files?.length) {
    return questionExtends;
  }

  const processedExtends = { ...questionExtends };
  processedExtends.Files = questionExtends.files.map((item: FileItem) => ({
    Path: item?.path,
    Name: item?.name,
    Size: item?.size,
    Url: item?.url,
  }));
  delete processedExtends.files;
  
  return processedExtends;
};

/**
 * 创建会话
 */
const createConversation = async (
  url: string,
  userId: string,
  appKey: string,
  inputs: Record<string, any>,
  headers: Record<string, any>,
  initOption?: any,
  createConversationMethod?: (data: { url: string; params: any }) => Promise<ConversationData>
): Promise<string | undefined> => {
  const { requestTransfer: initRequestTransfer } = initOption || {};
  
  const initRequestData = {
    AppKey: appKey,
    UserID: userId,
    Inputs: {
      SYS_USERNAME: userId,
      ...inputs,
    },
  };

  const requestData = initRequestTransfer 
    ? await initRequestTransfer(initRequestData) 
    : initRequestData;

  // 如果createConversationMethod存在，则使用createConversationMethod，否则使用fetchData
  const createConversationData: ConversationData = createConversationMethod ? await createConversationMethod({
    url,
    params: requestData,
  }) : await fetchData({
    url,
    data: requestData,
    headers: { ...headers, Apikey: appKey },
  });
  return createConversationData?.Conversation?.AppConversationID || createConversationData?.conversation?.appConversationID;
};

/**
 * 更新会话
 */
const updateConversation = async (
  url: string,
  userId: string,
  conversationId: string,
  appKey: string,
  inputs: Record<string, any>,
  headers: Record<string, any>,
  conversationName?: string
): Promise<void> => {
  await fetchData({
    url,
    data: {
      UserID: userId,
      AppConversationID: conversationId,
      AppKey: appKey,
      Inputs: inputs,
      ...(conversationName && { ConversationName: conversationName }),
    },
    headers: { ...headers, Apikey: appKey },
  });
};

/**
 * 构建请求数据
 */
const buildRequestData = async (
  params: RequestParams,
  conversationId: string,
  questionExtends?: FileExtends,
  requestTransfer?: (data: any) => any
) => {
  const {
    appId,
    appKey,
    userId,
    question,
    stream,
    messageInterval = CONSTANTS.DEFAULT_MESSAGE_INTERVAL,
    perMessageNum = CONSTANTS.DEFAULT_PER_MESSAGE_NUM,
    extendParams,
  } = params;

  const initRequestData = {
    ...extendParams,
    AppKey: appKey,
    UserID: userId,
    AppConversationID: conversationId,
    Query: question,
    QueryExtends: questionExtends,
    ResponseMode: stream ? CONSTANTS.DEFAULT_RESPONSE_MODE.STREAMING : CONSTANTS.DEFAULT_RESPONSE_MODE.BLOCKING,
    messageInterval,
    perMessageNum,
    Apikey: appKey,
    appId: appId,
    conversationId: conversationId,
  };

  return requestTransfer ? requestTransfer(initRequestData) : initRequestData;
};

/**
 * 处理SSE数据
 */
const handleSSEData = (
  rawData: string,
  conversationId: string,
  responseTransfer?: (data: any) => any,
  isAibag?: boolean,
) => {
  try {
    const responseData = parseSSEData({
      rawData,
      conversationId,
      responseTransfer,
      isAibag,
    });

    if (!responseData) {
      throw new Error('无法解析响应数据');
    }

    return {
      code: CONSTANTS.RESPONSE_CODES.SUCCESS,
      msg: 'success',
      resultData: responseData,
    };
  } catch (err) {
    console.error('解析SSE数据时出错:', err);
    throw err instanceof Error ? err : new Error('解析SSE数据时出错');
  }
};

/**
 * 处理最终SSE数据
 */
const handleFinalSSEData = (
  rawData: string,
  conversationId: string,
  responseTransfer?: (data: any) => any,
  isAibag?: boolean,
) => {
  try {
    const finalResponseData = parseSSEData({
      rawData,
      conversationId,
      responseTransfer,
      isAibag,
    });
    
    if (finalResponseData) {
      return {
        code: CONSTANTS.RESPONSE_CODES.SUCCESS,
        msg: 'success',
        resultData: {
          ...finalResponseData,
          endFlag: true,
        },
      };
    }
  } catch (err) {
    console.error('最终解析SSE数据时出错:', err);
  }
  return null;
};

/**
 * 处理非流式响应
 */
const processNonStreamResponse = (
  responseData: ResponseData,
  conversationId: string,
  responseTransfer?: (data: any) => any
): Data => {
  const resultList = typeof responseData?.parsedAnswer === 'string' 
    ? [{ 
        type: CONSTANTS.MESSAGE_TYPES.MARKDOWN, 
        content: { text: responseData.parsedAnswer }, 
        taskId: responseData?.task_id 
      }] 
    : responseData?.parsedAnswer;

  const standardData = {
    code: CONSTANTS.RESPONSE_CODES.SUCCESS,
    msg: 'success',
    resultData: {
      conversationId,
      messageId: responseData?.id,
      list: resultList || [],
    },
  };

  return responseTransfer ? responseTransfer(standardData) : standardData;
};

/**
 * 发送消息并处理响应的自定义Hook
 * @param options - 配置选项
 * @returns 包含发送消息方法和状态的对象
 */
const useSendMessage = (options: SendMessage) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',
    url = '',
    needCreateConversation = true,
    needUpdateConversation = false,
    createConversationUrl: createUrl = '',
    createConversationMethod,
    updateConversationUrl: updateUrl = '',
    type = CONSTANTS.DEFAULT_TYPE,
    action = '',
    tcpRequest,
    method = CONSTANTS.DEFAULT_METHOD,
    paramsKey = '',
    requestTransfer,
    responseTransfer,
    initOption,
    manual = false,
    headers,
    isAibag = false,
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // 记录原始数据
  const rawDataRef = useRef<string>('');
  const cleanupRef = useRef<(() => void) | null>(null);
  const isInterrupted = useRef(false);
  
  // 构建URL
  const { fullUrl, createConversationUrl, fullUpdateUrl } = buildUrls(baseUrl, url, createUrl, updateUrl);

  /**
   * 发送消息方法
   */
  const run = useCallback(async (customParams?: any) => {
    // 清理之前的请求
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
    // 重置中断状态
    isInterrupted.current = false;
    
    setLoading(true);
    setError(null);
    rawDataRef.current = '';

    // 合并参数
    const params: RequestParams = deepMerge(requestParams, customParams);

    const { 
      userId, 
      conversationId: initConversationId, 
      question, 
      questionExtends,
      isFirstChat = false,
      stream,
      inputs = {},
      appKey,
    } = params;

    try {
      let newConversationId = initConversationId;
      // 如果没传会话id，则先创建会话
      if ((!newConversationId || newConversationId === '') && createConversationUrl !== '' && needCreateConversation) {
        const createdConversationId = await createConversation(
          createConversationUrl,
          userId,
          appKey,
          inputs,
          headers || {},
          initOption,
          createConversationMethod
        );
        if (createdConversationId) {
          newConversationId = createdConversationId;
        }
      }
      // 如果isFirstChat为true，则更新会话
      // 如果isAibag为true，则不更新会话
      if (isFirstChat && fullUpdateUrl !== '' && newConversationId && needUpdateConversation) {
        await updateConversation(
          fullUpdateUrl,
          userId,
          newConversationId,
          appKey,
          inputs,
          headers || {},
          question
        );
      }

      // 处理文件数据
      const processedQuestionExtends = processFileData(questionExtends);

      // 构建请求数据
      const requestData = await buildRequestData(
        params,
        newConversationId || '',
        processedQuestionExtends,
        requestTransfer
      );

      const { inputs: newInputs } = requestData;
      
      // 如果需要更新智能体变量，则更新会话
      // 如果isAibag为true，则不更新会话
      if (newInputs && !isFirstChat && fullUpdateUrl !== '' && newConversationId && needUpdateConversation) {
        await updateConversation(
          fullUpdateUrl,
          userId,
          newConversationId,
          appKey,
          newInputs,
          headers || {}
        );
      }
      
      // 根据stream参数处理流式/非流式请求
      if (stream) {
        const sseHandlers: SSEHandlers = {
          onMessage: (resultData) => {
            if (isInterrupted.current) return;
            try {
              rawDataRef.current = resultData;
              const parsed = handleSSEData(rawDataRef.current, newConversationId || '', responseTransfer, isAibag);
              setData(parsed);
            } catch (err) {
              if (isInterrupted.current) return;
              setError(err instanceof Error ? err : new Error('处理SSE数据时出错'));
              setLoading(false);
              cleanupRef.current = null;
            }
          },
          onEnd: () => {
            if (isInterrupted.current) return;
            const finalData = handleFinalSSEData(rawDataRef.current, newConversationId || '', responseTransfer, isAibag);
            if (finalData) {
              setData(finalData);
            }
            setLoading(false);
            cleanupRef.current = null;
          },
          onError: (err: Error) => {
            if (isInterrupted.current) return;
            setError(err);
            setLoading(false);
            cleanupRef.current = null;
          },
        };

        let cleanup = null;
        if (type.toLowerCase() === 'tcp') {
          cleanup = fetchTcpSSE({
            url: fullUrl,
            data: requestData,
            tcpRequest,
          }, sseHandlers);
        } else {
          cleanup = fetchSSE({
            url: fullUrl,
            data: requestData,
            type,
            tcpRequest,
            headers: { ...headers, Apikey: appKey },
          }, sseHandlers);
        }
        
        cleanupRef.current = cleanup;
      } else {
        // 处理非流式请求
        let sendMessageData: any = null;

        if (type.toLowerCase() === 'tcp') {
          if (tcpRequest) {
            sendMessageData = tcpRequest({
              url: fullUrl,
              params: requestData,
            });
          } else {
            sendMessageData = await fetchMS({
              action, 
              url: fullUrl, 
              method, 
              paramsKey, 
              data: requestData, 
              headers
            });
          }
        } else {
          sendMessageData = await fetchData({
            url: fullUrl,
            data: requestData,
            headers: { ...headers, Apikey: appKey },
            responseType: 'text',
          });
        }
        
        const responseData = sendMessageData?.data;
        const result = processNonStreamResponse(responseData, newConversationId || '', responseTransfer);
        
        setData(result);
        setLoading(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('发送消息失败'));
      setLoading(false);
      cleanupRef.current = null;
    }
  }, [
    createConversationUrl, 
    fullUrl, 
    fullUpdateUrl,
    requestTransfer, 
    responseTransfer,
    requestParams,
    headers,
    initOption,
    action,
    method,
    paramsKey,
    tcpRequest,
    type,
    isAibag,
    createConversationMethod,
    needUpdateConversation,
    needCreateConversation,
  ]);

  const stop = useCallback(() => {
    isInterrupted.current = true;
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
  }, []);

  // 在组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    };
  }, []);
  
  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
    stop,
  };
};

export default useSendMessage;
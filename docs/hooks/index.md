---
nav:
  title: hooks
  order: -1
order: -1
---

# 介绍
支持Headless方式的hooks调用,旨在支持接入方已有完整的UI组件的场景.
> 全栈式解决方案中已经内置相关hooks能力,无须关注.

## 使用方式
```js
import { useXXX, .... } from '@ht/chatui';

...

const { data, loading, error, run } = useXXX(options);
```

## Options
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| requestConfig | 请求相关配置 | `object` | 详细参考相应API |
| requestParams | 标题文字 | `object` | 详细参考相应API |

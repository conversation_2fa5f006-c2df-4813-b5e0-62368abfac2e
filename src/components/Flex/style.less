.Flex {
  display: flex;
}

.Flex--inline {
  display: inline-flex;
}

.Flex--center {
  justify-content: center;
  align-items: center;
}

.Flex--d-r {
  flex-direction: row;
}

.Flex--d-rr {
  flex-direction: row-reverse;
}

.Flex--d-c {
  flex-direction: column;
}

.Flex--d-cr {
  flex-direction: column-reverse;
}

.Flex--w-n {
  flex-wrap: nowrap;
}

.Flex--w-w {
  flex-wrap: wrap;
}

.Flex--w-wr {
  flex-wrap: wrap-reverse;
}

.Flex--jc-fs {
  justify-content: flex-start;
}

.Flex--jc-fe {
  justify-content: flex-end;
}

.Flex--jc-c {
  justify-content: center;
}

.Flex--jc-sb {
  justify-content: space-between;
}

.Flex--jc-sa {
  justify-content: space-around;
}

.Flex--ai-fs {
  align-items: flex-start;
}

.Flex--ai-fe {
  align-items: flex-end;
}

.Flex--ai-c {
  align-items: center;
}

.FlexItem {
  flex: 1;
  min-width: 0;
  min-height: 0;
}

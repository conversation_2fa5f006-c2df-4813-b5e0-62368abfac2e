.NavBarApp {
  position: relative;
  text-align: center;
  padding: 8px 20px 0 15px;
  box-shadow: 0 15px 15px -10px #cfe1f4;
  z-index: 1;

  .NavBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .NavbarTitle {
    display: flex;
    font-size: 18px;
    font-weight: bold;
    color: #333333;
    line-height: 24px;
  }

  .CenterLogo {
    position: absolute;
    left: calc(50% - 14px);
    top: calc(50% - 14px);
  }

  .appBack {
    width: 12px;
    height: 20px;
  }

  .Logo {
    width: 28px;
    height: 28px;
  }

  .history {
    margin-right: 12px;
    width: 28px;
    height: 28px;
  }

  .new {
    width: 28px;
    height: 28px;
  }

  .Back {
    position: absolute;
    top: 13px;
    left: 0;
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
}

.NavBarPc {
  padding: 18px 20px 6px 20px;
  position: relative;
  z-index: 1;
  box-shadow: 0 15px 15px -10px #cfe1f4;
  background: @navbar-bg;

  .NavBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .NavbarTitle {
    display: flex;
    font-size: 18px;
    font-weight: bold;
    color: #333333;
    line-height: 24px;
  }

  .Logo {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }

  .history {
    margin-right: 21px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .new {
    margin-right: 21px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .close {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

}

.NavBar {
  position: relative;
}

.conversationWrap {
    display: flex;
    flex-direction: column;
    height: 100vh;
    border-radius: 8px;
    background: @history-conversation-bg;

    .conversation {
        display: flex;
        flex-direction: column;
        .title {
            margin-top: 8px;
        }

        .item {
            margin-top: 4px;
        }
    }
}

.contentWrap {
    flex-grow: 1;
    overflow-y: scroll;
    padding: 0 6px 0px 20px;
    margin-right: 6px;

    /* 整个滚动条 */
        &::-webkit-scrollbar {
            width: 6px;
            /* 垂直滚动条宽度 */
            height: 6px;
            /* 水平滚动条高度 */
        }

        /* 滚动条轨道 */
        &::-webkit-scrollbar-track {
            background: transparent;
            /* 轨道背景色 */
            // border-radius: 6px;
            /* 轨道圆角 */
        }

        /* 滚动条滑块 */
        &::-webkit-scrollbar-thumb {
            /* 滑块背景色 */
            border-radius: 3px;
            /* 滑块圆角 */
            border: 3px solid #B9D1E9;
            /* 滑块边框 */

            width: 6px;
            height: 45px;
            background: #B9D1E9;
            border-radius: 3px;
        }

        /* 滚动条滑块悬停效果 */
        &::-webkit-scrollbar-thumb:hover {
            background: #555;
            /* 悬停时滑块背景色 */
        }
}

// /* 整个滚动条 */
// ::-webkit-scrollbar {
//     width: 6px;
//     /* 垂直滚动条宽度 */
//     height: 6px;
//     /* 水平滚动条高度 */
// }

// /* 滚动条轨道 */
// ::-webkit-scrollbar-track {
//     background: transparent;
//     /* 轨道背景色 */
//     // border-radius: 6px;
//     /* 轨道圆角 */
// }

// /* 滚动条滑块 */
// ::-webkit-scrollbar-thumb {
//     background: #E5E7EE;
//     /* 滑块背景色 */
//     border-radius: 3px;
//     /* 滑块圆角 */
//     border: 3px solid #f1f1f1;
//     /* 滑块边框 */

//     width: 6px;
//     height: 45px;
//     background: #B9D1E9;
//     border-radius: 3px;
// }

// /* 滚动条滑块悬停效果 */
// ::-webkit-scrollbar-thumb:hover {
//     background: #555;
//     /* 悬停时滑块背景色 */
// }

.groupTitle {
    display: flex;
    align-items: center;
    padding: 9px 8px;

    // .titleIcon {
    //     width: 2px;
    //     height: 10px;
    //     background: #CCCCCC;
    //     margin-right: 6px;
    // }

    .titleText {
        font-size: 14px;
        color: @grey-9;
        line-height: 22px;
    }
}

.conversationItem {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    padding: 9px 8px;
    border-radius: 8px;
    color: @history-conversation-active-color;
    &:hover {
        background: @history-conversation-active-bg;
    }

    .itemLabel {
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        font-size: 14px;
        font-weight: 500;    
        line-height: 24px;     
    }

    .itemMenuWrap{

        .itemMenu{
            width: 24px;
            height: 24px;
            border-radius: 6px;
            padding: 0 4px;
            background-color: @history-conversation-active-bg;
            cursor: pointer;
        }
        .menuItemPopover{
            position: absolute;
            right: 0;
            top: 38px;  
            width: 152px;    
            background: #FFFFFF;
            border-radius: 12px;
            padding: 6px;
            z-index: 10; 
            box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.12);
            display: flex;
            flex-direction: column;
            justify-content: center;
            .menuItem {
                width:100%;           
                height: 32px;
                padding: 5px 8px;
                border-radius: 8px;
                cursor: pointer;
                &:hover {
                    background: @history-conversation-active-bg;
                }
                
            }
            .menuItemRed {
                color: @red-1;
                &:hover {
                    background: @history-conversation-active-error;
                }              
            }
            .menuItemIcon{
                margin-right: 8px;
                vertical-align: middle;
            }
        }
    }

    
}

.conversationItemActive {
    background: @history-conversation-active-bg;
    color: @history-conversation-active-color;
}
.conversationItemDisabled {
    background: @history-conversation-disabled-bg;
    color: @history-conversation-active-color;
    .itemLabel{
        color: @history-conversation-disabled-color;
    }
}

.searchBarWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-04-10 10:50:13
 */
import React, { useState, useEffect } from 'react';
import { Button, Input, useSendMessage } from '@ht/chatui';

export default () => {
  const [value, setValue] = useState('你是谁?');
  const { data: conversationData, loading: conversationLoading, run: conversationRun } = useSendMessage({
    requestConfig: {
      platform: 'hiAgent',
      baseUrl: '/hiAgent',
      type: 'http',
      manual: true,
    },
    requestParams: {
      appKey: 'd07gpo0im975aeu764t0',
      conversationId: 'd18ct8mr36pds5qqfirg',
      userId: '016645',
      question: value,
      stream: true,
      isFirstChat: true,
    },
  });
  const [conversationDataNew, setConversationDataNew] = useState<any>({ resultData: { list: [] } });
  const [isEnd, setIsEnd] = useState(false);
  const [ignoreOldData, setIgnoreOldData] = useState(false);
  const [lastProcessedData, setLastProcessedData] = useState<any>(null);

  // 监听loading状态，开始新请求时清空数据并标记忽略旧数据
  useEffect(() => {
    if (conversationLoading) {
      setConversationDataNew({ resultData: { list: [] } });
      setIsEnd(false);
      setIgnoreOldData(true);
      setLastProcessedData(null);
    }
  }, [conversationLoading]);

  useEffect(() => {
    if (!conversationData?.resultData) return;
    
    // 如果需要忽略旧数据且不在loading状态，说明这是旧数据
    if (ignoreOldData && !conversationLoading) {
      setIgnoreOldData(false); // 重置标志，准备接收新数据
      return;
    }

    // 防止重复处理相同数据
    if (lastProcessedData && JSON.stringify(conversationData) === JSON.stringify(lastProcessedData)) {
      return;
    }
    
    setLastProcessedData(conversationData);

    if (conversationData.resultData.endFlag) {
      setIsEnd(true);
    } else if (conversationData.resultData.list) {
      if (isEnd) {
        // 对话结束后的新消息，重置数据
        setConversationDataNew(conversationData);
        setIsEnd(false);
      } else {
        // 追加流式数据
        setConversationDataNew((prev: any) => ({
          ...prev,
          resultData: {
            ...prev.resultData,
            list: [...(prev.resultData?.list || []), ...conversationData.resultData.list],
          }
        }));
      }
    }
  }, [conversationData, isEnd, conversationLoading, ignoreOldData, lastProcessedData]);

  const handleSendMessage = () => {
    conversationRun();
  };

  return (
    <div>
      <Input value={value} onChange={setValue} placeholder="请输入..." />
      <Button onClick={handleSendMessage} loading={conversationLoading}>发送消息</Button>
      <div>
        {conversationDataNew?.resultData?.list?.map((item: any, index: number) => (
          <span key={index}>{item.content.text}</span>
        ))}
      </div>
    </div>
  )
}

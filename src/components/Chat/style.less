& when (@global-style =true) {
  html {
    height: 100vh;

    &[data-safari] {
      height: calc(100vh - calc(100vh - 100%));
    }
  }

  body,
  #root {
    height: 100%;
  }

  body {
    margin: 0;
  }
}

.ChatWrap {
  position: relative;
  height: 100%;
  border-radius: inherit;
}

.img-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.ModalWrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 20px;
  z-index: 999;
  background: #fff;
  text-align: center;
  overflow-y: auto;
  border-radius: inherit;

  &-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 14px;
    height: 14px;
    z-index: 999;
  }
}

.ChatApp {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: @gray-6;
  color: @body-color;
  font-family: @font-family-base;
  line-height: @line-height-base;
  -webkit-tap-highlight-color: transparent;
  background: transparent;
  position: relative;
  background: linear-gradient(180deg, #cfe1f4 0%, #fff 100%);
  border-radius: inherit;
}

.S--focusing {
  --safe-bottom: 0px;
}

@media (hover: none) {
  .S--focusing {
    .MessageList {
      margin-top: 75vh;
    }
  }
}

.ChatFooter {
  position: relative;
  z-index: @zindex-footer;
  padding-bottom: @safe-bottom;
  padding-left: 15px;
  padding-right: 15px;
  background: transparent;
  // padding: 0 15px calc(@safe-bottom + 27px) 15px;

  &-Version {
    padding: 10px 0;
    font-size: 12px;
    color: #ccc;
    line-height: 17px;
    width: 100%;
    text-align: center;
  }
}

.LowcodeLoading {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

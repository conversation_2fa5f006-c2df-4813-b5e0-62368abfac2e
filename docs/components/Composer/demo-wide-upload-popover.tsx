import React from 'react';
import { Composer, ComposerUploadFile, ComposerUploadChangeParam } from '@ht/chatui';

export default () => {
  const onSend = async (type: string, content: string, payload: object) => {
    return new Promise<boolean>((resolve) => {
      console.log('type', type, 'content', content, 'payload', payload);
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
  };

  const [fileList, setFileList] = React.useState<ComposerUploadFile[]>([]);
  const onChange = (evt: ComposerUploadChangeParam) => {
    console.log('fileList', evt);
    setFileList(evt.fileList);
  };

  const popoverContent = (
    <div>
      <div>1. 最多支持10个大小不超过10MiB 的文档/图片/音频</div>
      <div>2. 上传文件仅支持以下格式：pdf, doc</div>
    </div>
  );

  return (
    <Composer
      isWide
      text="ceshi"
      inputType="text"
      onSend={onSend}
      uploadConfig={{
        action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
        fileList: fileList,
        onChange: onChange,
        popoverConfig: {
          content: popoverContent,
        },
      }}
    ></Composer>
  );
};

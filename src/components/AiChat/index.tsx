/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable compat/compat */
import React, { useEffect, useState, useRef, useCallback, useImperativeHandle } from 'react';
// import LCRender from '@lowcode/lc-render';
import { useMemo } from 'react';
// import { initXlog, isSCKDoc, PV_LOADED_PARAMS  } from './xlogInit';
// import { log, setUUID, isReady, logPV } from '@ht/xlog';
import { GuidePage } from '../GuidePage';
import { LocaleProvider } from '../LocaleProvider';
import { Navbar } from '../Navbar/index';
import { ChatContext } from '../../hooks/useChatContext';
import { toast } from '../Toast';
import { FrontendAction } from '../../action/interface'

import {
  MessageContainer,
  // MessageContainerProps,
  MessageContainerHandle,
} from '../MessageContainer';
import { QuickReplies, QuickReplyItemProps } from '../QuickReplies';
import { Composer as DComposer } from '../Composer';
import { ComposerHandleProps } from '../Composer/interface';
import { MessageProps } from '../Message';
// import { Bubble } from '../Bubble';
// import { RichText } from '../RichText';
// import { Card } from '../Card';
// import { Feedback } from '../Feedback';
import isSafari from '../../utils/isSafari';
// import { getLowcodeUrl } from '../../utils/lowcode';
// import FetchClient from '../../utils/fetchClient';
import { postRequest } from '../../utils/requestClient';
// import { GuessAskMore } from '../GuessAskMore';
// import { AiImage } from '../Image';
// import { LoadingSpinner } from '../LoadingSpinner';
// import { FileCard } from '../FileCard';
// import { Video } from '../Video';
// import { MarkDown } from '../MarkDown';
import { RiskTipApp } from '../RiskTipApp';
import { RiskTipPc } from '../RiskTipPc';
// import { HallucinationMarker } from '../HallucinationMarker'
// import { ThinkContent } from '../ThinkContent'
import { getRandomString } from '../../utils';
import { useMessages } from '../../../src';
import { isMobile } from '../../utils/canUse';
import { formatLowcodeContent, getDateFormat } from '../../utils/aiChat';
import { HistoryConversation } from '../HistoryConversation';
import { LogPointConfigMap, ILogParams } from '../../LogPointConfigMap';
import { WaterMark } from '../WaterMark';
import { AiChatHandle, AiChatProps } from './interface';

import { ConversationItemProps } from '../HistoryConversation/interface';
import { ReferenceProps } from '../References/interface';
import { FeedbackLabelProps } from '../FeedbackTipPc/interface';
import { PreprocessNavBarConfig } from './utils';
import { UploadFile } from '../Composer/wide/Upload';
import { getThinkContent } from '../../utils/aiChat';
import { CiteInfoProps } from '../Composer/components/type';
import { innerSikllCodeList, showTexComposerCodes } from './config';

import {
  useCreateConversation,
  useDeleteConversation,
  useStopMessage,
  useGetConversationList,
  useGetMessageList,
  useSendMessage,
  useFeedback,
  useDeleteMessages,
} from '../../hooks/aiAgentChat/index';
import { ChatConfig } from '../ChatConfig';
import type { Skill, ComposerType } from '../Composer/components/type';
import { useActionHook } from '../../action';
import { hasFrontActionFlag } from './utils';

export const AiChat = React.forwardRef<AiChatHandle, AiChatProps>((props, ref) => {
  const {
    // wideBreakpoint,
    locale = 'zh-CN',
    locales,
    navbar,
    renderNavbar,
    welcome,
    renderWelcome,
    loadMoreText,
    renderBeforeMessageList,
    messagesRef,
    onRefresh,
    onScroll,
    initialMessages = [],
    renderMessageContent,
    onBackBottomShow,
    onBackBottomClick,
    quickReplies,
    onQuickReplyClick,
    onQuickReplyScroll,
    renderQuickReplies,
    backBottomButton,
    onInputFocus,
    onInputChange,
    onInputBlur,
    onSend,
    onImageSend,
    inputOptions,
    composerRef,
    inputType,
    onInputTypeChange,
    recorder,
    toolbar,
    onToolbarClick,
    onAccessoryToggle,
    // rightAction,
    Composer = DComposer,
    config,
    renderFooterVersion,
    onReportLog,
    renderComposer: CustomerComposer,
    composerConfig,
    historyConversation,
    showFeedbackModal,
    feedbackModalConfig,
    referencesConfig,
    isWide,
    showPushHistory = true,
    showToken = true,
    showHallucination = true,
    needFeedback = true,
    renderStopAnswer,
    customThinkingStyle,
    customDeepThinkingStyle,
    customThinkingCompleteStyle,
    autoThinkCollapsed = false,
    renderReferencesContent,
    messageContainerConfig,
    actions,
    hallucinationText
  } = props;

  let msgsRef = React.useRef<MessageContainerHandle>(null);

  if (messagesRef) {
    msgsRef = messagesRef;
  }

  // 输入区
  let composerInputRef = React.useRef<ComposerHandleProps>(null);
  if (composerRef) {
    composerInputRef = composerRef;
  }

  const { messages, appendMsg, updateMsg, prependMsgs, setTyping, getTyping, getMessages, resetList, deleteMsg } = useMessages(initialMessages);

  // const [isComponentFirstLoad, setIsComponentFirstLoad] = useState(!localStorage.getItem('hasChatUIComponentLoaded'));

  // 需要用到魔方卡片时，外部需要定义window.LCRender
  //   import LCRender from '@lowcode/lc-render';
  //   window.LCRender = LCRender;
  // const LCRender = window.LCRender || null;

  // config参数解析 start
  const appId = config?.appId;
  const isAorta = config?.appId === 'aorta';
  const userId = config?.userId;
  const sceneId = config?.sceneId;
  const source = config?.source;
  const isDev = config?.isDev || false;//是否测试环境
  window.isLowCodeDev = isDev; //用于设置魔方域名

  // const lowCodeConfig = config?.lowCode;//魔方组件的rootValue
  const requestConfig = config?.requests;
  // const mobileAction = requestConfig?.mobileAction;//公共接口移动端tcp请求的action，tcp需要配置
  // baseUrl支持传函数或字符串
  let interfacePrefix = requestConfig?.baseUrl;
  // if (typeof requestConfig?.baseUrl === 'function') {
  //   interfacePrefix = requestConfig?.baseUrl();
  // }

  // console.log('lowCodeConfig=' + lowCodeConfig);
  const defaultPushPercent = isMobile ? 75 : 100;
  const pushPosition = historyConversation?.pushPosition ? historyConversation?.pushPosition : 'right';
  const pushPercent = historyConversation?.pushPercent ? historyConversation?.pushPercent : defaultPushPercent;

  // 默认请求配置
  // const defaultRequests = {
  //   /**
  //   * 基础URL
  //   */
  //   baseUrl: '',

  //   //初始引导接口
  //   init: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/initialGuide',
  //   },

  //   //问答接口
  //   send: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/chat',
  //   },

  //   //查询历史接口
  //   history: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/historyRecord',
  //     pageSize: 3,
  //   },

  //   //快捷问题接口
  //   quickReply: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/quickNavigation',
  //   },

  //   //点赞点踩接口
  //   score: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/feedBack',
  //   },
  // };
  // 发送问答接口
  const sendConfig = requestConfig?.send;
  // 点赞点踩接口
  const scoreConfig = requestConfig?.score;
  // 停止生成
  const stopConfig = requestConfig?.stop;
  // 删除消息接口
  const deleteMessageConfig = requestConfig?.deleteMessage;
  // 关联问题接口
  // const relatedConfig = requestConfig?.related;
  // 快捷问题
  // const quickReplyConfig = requestConfig?.quickReply;
  // 不传默认用浏览器的行为打开链接
  // const openWebPage = config?.bridge?.openWebPage || window.open;

  // 历史消息接口
  const historyConfig = requestConfig?.history;
  // 初始接口
  const initConfig = requestConfig?.init;
  // 历史会话列表接口
  const historyConversationConfig = requestConfig?.historyConversation;
  // 删除历史会话接口
  const deleteHistoryConversationConfig = requestConfig?.deleteHistoryConversation;
  // 重新编辑历史会话名称接口
  // const renameHistoryConversationConfig = requestConfig?.renameHistoryConversation;
  // 阅读风险提示接口
  const riskReadConfig = requestConfig?.riskRead;
  // 查询是否阅读了风险提示接口
  const queryRiskReadConfig = requestConfig?.queryRiskRead;
  //  敏感词校验
  const sensitiveConfig = requestConfig?.sensitive;
  // 点踩反馈标签查询
  const feedbackLabelConfig = requestConfig?.feedbackTagList;

  // config参数解析 end

  // const fetchClient = new FetchClient(interfacePrefix);

  // 第一次答案返回以后conversationId才有值
  const conversationId = useRef('');

  // 是否切换了会话,初始值应为true
  const changedConversation = useRef(true);

  // 初始化引导页消息
  const [welcomeInfo, setWelcomeInfo] = useState<any>(null);
  // 没有更多历史消息了（初始化时以初始化的接口返回是否有历史消息字段为准）
  const [noMoreHistory, setNoMoreHistory] = useState(true);
  // 消息缓存数据
  const [sendHooksDataCache, setSendHooksDataCache] = useState<any>({});
  // 当前是历史消息的第几页
  const historyPageNum = useRef(1);
  // 历史消息偏移量
  const historyNextOffset = useRef('');
  // 用来记录哪些消息被停止生成了（主要是setState是异步的，导致虽然已经设置了stopped，但是sse接口的循环体内还没有拿到，下一条消息又开始发送了，导致旧消息即使停止生成了，还会再追加消息）
  const stoppedMsgs = useRef<MessageProps['_id'][]>([]);
  // 用来记录当前问答是否已经处理过relatedQuestionList，避免重复appendMsg
  const relatedQuestionListProcessed = useRef<boolean>(false);

  // 快捷问题
  // const [quickReplies, setQuickReplies] = useState<QuickReplyItemProps[]>( []);
  // 是否展示停止生成按钮
  const [showStopAnswer, setShowStopAnswer] = useState(false);

  // 历史会话面板是不是打开
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  // 风险提示弹窗打开还是关闭
  const [showRiskTip, setShowRiskTip] = useState(false);
  // 欢迎页是否展示，默认为true
  const openFlag: any = welcome && welcome?.hasOwnProperty('open') ? welcome?.open : true;
  // 是否是新会话
  const [isNewConversation, setIsNewConversation] = useState(openFlag); // showNewConversation
  // 因为sse接口里面需要判断，异步的state不准因此用Ref的
  const isNewConversationRef = useRef(true); // showNewConversation

  const [historyConversationList, setHistoryConversationList] = useState<ConversationItemProps[]>([]);

  // 是否选中互联网搜索 默认打开
  const [enableInternetSearch, setEnableInternetSearch] = useState(true);

  // 是否已经读了风险提示
  const [riskRead, setRiskRead] = useState(false);

  // 是否键盘弹起
  const [keyboardShow, setKeyboardShow] = useState(false);

  // 是否展示敏感词检验报错信息
  const [showSensitiveError, setShowSensitiveError] = useState(false);

  // 点踩反馈标签列表
  const [feedbackLabels, setFeedbackLabels] = useState<FeedbackLabelProps[]>([]);

  // 全局对话设置参数
  const VariableConfigs = config?.VariableConfigs || [];
  const chatConfigRef = useRef<any>();
  const [chatConfigs, setChatConfigs] = useState(VariableConfigs || []);

  // 切换会话以后需要滚动到底步
  const needScrollToEnd = useRef(false);
  const msgSendVar = useRef<MessageProps>({ _id: '', type: 'text' });
  const msgTextSendVar = useRef<Record<string, string>>({});
  const thinkingTextSendVar = useRef<Record<string, string>>({});
  const currentThinkingIdSendVar = useRef('');
  // 用来记录text的同一段话taskId，第一次append了文本消息以后push进来
  const textTaskIdsSendVar = useRef<string[]>([]);
  // 用来记录thinKing的同一段话taskId，第一次append了thinking消息以后push进来
  const thinkingTaskIdsSendVar = useRef<string[]>([]);
  // 用来记录文本和其thinkingId的对应关系
  const textThinkingIdsSendVar = useRef<Record<string, string>>({});
  // 用来记录参考资料，等拿到endFlag之后将参考资料追加到最后一条消息上
  const referencesSendVar = useRef<ReferenceProps[]>([]);
  // 用来记录之前解析send返回的流到了list的哪一个位置（hooks里面每一次返回的list，都是累加的）
  const prevSendHooksDataPosition = useRef(0);
  // 用来记录是否已经显示了备用对话内容
  const backupTalkingShownRef = useRef(false);


  // 网络判断
  const [lastPackageId, setLastPackageId] = useState<string | null>(null);
  const [isNetworkOnline, setIsNetworkOnline] = useState<boolean>(navigator.onLine);
  const [pendingRetry, setPendingRetry] = useState<boolean>(false);


  // 整合内外部上报方法
  const onConsolidateReports = (params: ILogParams) => {
    // SCK 内部上报
    // if(isReady){
    //   log?.(params);
    // }   
    // 外部传入上报 - 可选
    onReportLog?.(params);  
  }

  // 初始化接口调用
  const { data: initHooksData, run: initHooksRun } = useCreateConversation({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...initConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      appKey: requestConfig?.appKey,
    },
  });

  const { data: historyConversationHooksData, run: historyConversationHooksRun } = useGetConversationList({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...historyConversationConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      appKey: requestConfig?.appKey,
    },
  });

  const { data: deleteHistoryConversationConfigData, run: deleteHistoryConversationHooksRun } = useDeleteConversation({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...deleteHistoryConversationConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
    },
  });

  // const { data: reNamehistoryConversationConfigData, run: renameHistoryConversationHooksRun } = useRenameConversation({
  //   requestConfig: {
  //     manual: true,
  //     platform: requestConfig?.platform || 'custom',
  //     baseUrl: interfacePrefix || '',
  //     ...deleteHistoryConversationConfig
  //   },
  //   requestParams: {
  //     appId: appId,
  //     userId: userId,
  //     // sceneId: sceneId,
  //     appKey: requestConfig?.appKey,
  //   },
  // });

  const { data: historyHooksData, run: historyHooksRun } = useGetMessageList({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...historyConfig
    },
    requestParams: {
      // offsetMessagePkId: historyNextOffset?.current,
      /* hiAgent平台不支持分页，pageNum为1，pageSize由外部传入 */
      pageNum: requestConfig?.platform === 'hiAgent' ? 1 : historyPageNum?.current,
      pageSize: historyConfig?.pageSize || 100,
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
    },
  });

  const feedbackMessageRef = useRef<{ type?: any, score?: any, message?: any, reason?: any }>({});
  const { data: feedbackHooksData, run: feedbackHooksRun } = useFeedback({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...scoreConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
      messageId: '',
      score: 'good',
      type: 'submit'
    },
  })

  // 拿到最后一条消息
  const getLastMsg = useCallback(() => {
    // return messages?.[messages?.length - 1];
    return getMessages()?.[getMessages()?.length - 1];
  }, []);

  const { run: stopHooksRun } = useStopMessage({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...stopConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
      taskId: getLastMsg()?._id as string,
      messageId: getLastMsg()?.messageId as string,
    },
  });

  const { data: sendHooksData, error: sendHooksError, run: sendHooksRun } = useSendMessage({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...sendConfig
    },
    requestParams: {
      extendParams: sendConfig?.extendParams || {},
      appId: appId,
      userId: userId,
      question: '',
      stream: sendConfig?.stream,
      // sceneId: sceneId,
      appKey: requestConfig?.appKey,
      messageInterval: sendConfig?.messageInterval,
      onError: config?.bridge?.sendErrorCallback,
    },
  });

  const { run: deleteMessagesHooksRun } = useDeleteMessages({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: interfacePrefix || '',
      ...deleteMessageConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
      messageIds: [],
    },
  });

  // // 拿到最后一条答案消息
  // function getLastAnswerMsg() {
  //   const msgs = messages;
  //   if (msgs?.length > 0) {
  //     return (messages || [])?.findLast((record: MessageProps) => record?.position === 'left');
  //   }
  //   return undefined;
  // }

  // // 拿到最后一条问题消息
  // function getLastQuestionMsg() {
  //   return messages?.findLast((record: MessageProps) => record?.position === 'right');
  // }

  //   const sseWorkerCode = `
  // self.onmessage = async function (e) {
  //   const { type, payload } = e.data;
  //   if (type === 'start') {
  //     const { url, headers, body } = payload;
  //     try {
  //       const response = await fetch(url, {
  //         method: 'POST',
  //         headers,
  //         body: JSON.stringify(body),
  //       });
  //       if (!response.body) {
  //         self.postMessage({ type: 'error', error: 'No response body' });
  //         return;
  //       }
  //       const reader = response.body.getReader();
  //       const decoder = new TextDecoder('utf-8');
  //       let buffer = '';
  //       while (true) {
  //         const { done, value } = await reader.read();
  //         if (done) break;
  //         buffer += decoder.decode(value, { stream: true });
  //         let boundary;
  //         while ((boundary = buffer.indexOf('\\n\\n')) !== -1) {
  //           const rawEvent = buffer.slice(0, boundary);
  //           buffer = buffer.slice(boundary + 2);
  //           self.postMessage({ type: 'data', data: rawEvent });
  //         }
  //       }
  //       self.postMessage({ type: 'end' });
  //     } catch (err) {
  //       self.postMessage({ type: 'error', error: err instanceof Error ? err.message : String(err) });
  //     }
  //   }
  // };
  // `;

  // function createSSEWorker() {
  //   const blob = new Blob([sseWorkerCode], { type: 'application/javascript' });
  //   return new Worker(URL.createObjectURL(blob));
  // }

  // function handleInputFocus(e: React.FocusEvent<HTMLTextAreaElement>) {
  //   setTimeout(() => {
  //     if (msgsRef && msgsRef.current) {
  //       msgsRef.current.scrollToEnd({ animated: false, force: true });
  //     }
  //   }, 100);

  //   if (onInputFocus) {
  //     onInputFocus(e);
  //   }

  //   if (isMobile) {
  //     setKeyboardShow(true);
  //   }
  // }

  function handleInputFocus(e: React.FocusEvent<HTMLTextAreaElement>) {
    // 优化：主线程空闲时再滚动到底部，兼容性处理
    const doScrollToEnd = () => {
      if (msgsRef && msgsRef.current) {
        msgsRef.current.scrollToEnd({ animated: false, force: true });
      }
    };

    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      (window as any).requestIdleCallback(doScrollToEnd, { timeout: 300 });
    } else {
      setTimeout(doScrollToEnd, 100);
    }

    if (onInputFocus) {
      onInputFocus(e);
    }

    if (isMobile) {
      setKeyboardShow(true);
    }
  }

  function handleInputBlur(e: React.FocusEvent<HTMLTextAreaElement>) {
    setKeyboardShow(false);

    if (onInputBlur) {
      onInputBlur(e);
    }
  }

  // 点赞点踩
  const handleFeedBack = useCallback((score: MessageProps['feedbackResult'], message: MessageProps, reason: any) => {
    feedbackMessageRef.current = {};
    let type = 'submit';
    if (score === message?.feedbackResult) {
      type = 'reset';
    }
    if (props.config.onFeedback) {
      props.config.onFeedback(score, message);
    }
    // 埋点上报
    if (score) {
      let params: ILogParams;
      if (type === 'submit') {
        params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGood') : LogPointConfigMap.get('handleFeedBackBad')) as ILogParams;
      } else {
        params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGoodCancel') : LogPointConfigMap.get('handleFeedBackBadCancel')) as ILogParams;
      }
      if(params){
        onConsolidateReports?.(params);
      } 
    }

    // 本地立即更新 feedbackResult 字段，实现全局 UI 响应
    let feedbackResult = score;
    if (type === 'reset') {
      feedbackResult = undefined;
    }
    const newMessage = { ...message, feedbackResult };
    updateMsg?.(message._id, newMessage);
    
    feedbackMessageRef.current = { type: type, score: score, message: message, reason: reason };

    if (scoreConfig?.url) {
      feedbackHooksRun(
        {
          score,
          type,
          messageId: message?.messageId,
          appId: appId,
          userId: userId,
          sceneId: sceneId,
          conversationId: conversationId?.current,
          reason: reason,
        }
      );
    }
  }, []);

  const feedbackUpdateMsg = useCallback(() => {
    let feedbackResult = feedbackMessageRef?.current?.score;
    if (feedbackMessageRef?.current?.type === 'reset') {
      feedbackResult = undefined;
    }
    const TIME_GAP = 5 * 60 * 1000;
    let lastTs = 0;
    const ts = feedbackMessageRef?.current?.message?.createdAt || Date.now();
    const hasTime = feedbackMessageRef?.current?.message.hasTime || ts - lastTs < 0 || ts - lastTs > TIME_GAP;
    const newMessage = { ...feedbackMessageRef?.current?.message, feedbackResult, hasTime };

    updateMsg?.(feedbackMessageRef?.current?.message._id, newMessage);
  }, []);

  useEffect(() => {
    if (feedbackHooksData && feedbackHooksData?.code === '0') {
      if (feedbackMessageRef?.current?.reason) {
        toast.success('反馈成功', 1000, 'center');
      }
      feedbackUpdateMsg();

    }
  }, [feedbackHooksData])

  // const handleFeedBack = useCallback((score: MessageProps['feedbackResult'], message: MessageProps, reason: any) => {
  //   let type = 'submit';
  //   if (score === message?.feedbackResult) {
  //     type = 'reset';
  //   }
  //   if (props.config.onFeedback) {
  //     props.config.onFeedback(score, message);
  //   }
  //   // 埋点上报
  //   if (score) {
  //     let params: ILogParams;
  //     if (type === 'submit') {
  //       params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGood') : LogPointConfigMap.get('handleFeedBackBad')) as ILogParams;
  //     } else {
  //       params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGoodCancel') : LogPointConfigMap.get('handleFeedBackBadCancel')) as ILogParams;
  //     }
  //     onConsolidateReports(params);
  //   }

  //   // fetchClient.post<Tmessage>({
  //   if (scoreConfig?.url) {
  //     postRequest(interfacePrefix, {
  //       payload: {
  //         score,
  //         type,
  //         messageId: message?.messageId,
  //         appId: appId,
  //         userId: userId,
  //         sceneId: sceneId,
  //         conversationId: conversationId?.current,
  //         reason: reason,
  //       },
  //       ...scoreConfig
  //     }).then((data) => {
  //       if (data && data?.code === '0') {
  //         if (reason) {
  //           toast.success('反馈成功', 1000, 'center');
  //         }
  //         let feedbackResult = score;
  //         if (type === 'reset') {
  //           feedbackResult = undefined;
  //         }
  //         const newMessage = { ...message, feedbackResult };
  //         updateMsg?.(message._id, newMessage);
  //       }
  //     });
  //   }
  // }, [])

  // 停止生成
  const handleStopAnswer = useCallback(() => {
    // 拿到最后一条答案消息
    const message = getLastMsg();
    if (message) {

      // 埋点上报
      const params = LogPointConfigMap.get('handleStopAnswer');
      if (params) {
        onConsolidateReports(params);
      }

      const newMessage = { ...message, stopped: true };
      updateMsg?.(message._id, newMessage);
      stoppedMsgs?.current?.push(message?._id);
      setShowStopAnswer(false);
      // fetchClient.post<Tmessage>({
      //   url: stopConfig?.url,
      //   body: {

      if (stopConfig?.url) {
        // postRequest(interfacePrefix, {
        //   payload: {
        //     appId: appId,
        //     userID: userId,
        //     sceneId: sceneId,
        //     taskId: message?._id,
        //     messageId: message?.messageId,
        //   },
        //   ...stopConfig
        // });
        stopHooksRun({
          taskId: message?._id,
          messageId: message?.messageId,
          conversationId: conversationId?.current,
        });
      }
    }
  }, [])

  function setUnsend() {
    // 找到最后一条问题消息
    const lastMessage = getLastMsg();
    // 如果最后一条消息是问题，那么打上感叹号
    if (lastMessage?.position === 'right') {
      updateMsg?.(lastMessage?._id, { ...lastMessage, unsend: true });
    } else {
      // 表示最后一条消息是回答，标记答案生成异常，并且隐藏停止生成按钮
      updateMsg?.(lastMessage?._id, { ...lastMessage, sendError: true });
      // 隐藏停止生成按钮
      setShowStopAnswer(false);
    }
  }

  const dealwithSSEData = (parsedData: any) => {
    if (!conversationId?.current) {
      conversationId.current = parsedData?.conversationId;
    }
    const msgId = parsedData?.messageId;
    // 如果没有taskId则以msgId作为唯一键
    const taskId = parsedData?.taskId || parsedData?.messageId;

    // 本条消息如果被停止生成了跳出最外层循环
    if (stoppedMsgs?.current?.includes(taskId)) {
      setLastPackageId('');
      return;
    }

    // 网络判断 todo
    // console.log('parsedData?.packetId=' + parsedData?.packetId);
    if (parsedData?.packetId) {
      setLastPackageId(parsedData.packetId + 1);
    }

    // 开新会若原来会话里面若还有SSE接口没处理结束的，则直接不再处理
    if (isNewConversationRef?.current === true) {
      return;
    }

    // shootIntention有值且等于false表示不能回答需要兜底话术
    if (parsedData?.shootIntention === false && parsedData?.backUpTalking && !backupTalkingShownRef.current) {
      backupTalkingShownRef.current = true;
      appendMsg?.({
        createdAt: parsedData?.createTime,
        type: 'text',
        content: {
          text: parsedData?.backUpTalking,
        },
      });
    }

    const parsedDataListLength = parsedData?.list?.length;

    // 针对每一条msgBubbleList做处理  --> 计划改为从hook中取data，直接循环data.list然后判断
    for (let i = (prevSendHooksDataPosition?.current ?? 0); i < parsedDataListLength; i++) {
      const msgData = parsedData?.list?.[i];

      if ((msgData?.type === 'markdown' || msgData?.type === 'text' || msgData?.type === 'richtext') && msgData?.content?.text) {
        // console.log('textThinkingIdsSendVar?.current?.[taskId] = ' + textThinkingIdsSendVar?.current?.[taskId] + '; taskId=' + taskId) ;
        const idWithThinking = textThinkingIdsSendVar?.current?.[taskId] || taskId;
        
        msgTextSendVar.current[idWithThinking] = (msgTextSendVar?.current?.[idWithThinking] ?? '') + msgData?.content?.text;

        if (currentThinkingIdSendVar?.current) {
          textThinkingIdsSendVar.current[taskId] = currentThinkingIdSendVar?.current;
          currentThinkingIdSendVar.current = ''; // 此时清掉思维链id，表明已经有文本消息使用此思维链id
        }
        msgSendVar.current = {
          _id: idWithThinking, // 在有思维链的情况下，以思维链的id作为更新标准，这样实际内容会被追加到思维链对应的气泡上
          messageId: msgId,
          type: 'markdown',
          createdAt: parsedData?.createTime,
          content: { text: msgTextSendVar?.current?.[idWithThinking] },
          thinkContent: thinkingTextSendVar?.current?.[idWithThinking] ?? '',
          isThinking: false,
          thinkTime: parsedData?.thinkTime,
        }

        // 同一段话的多条消息需要合并成一条
        if (!textTaskIdsSendVar?.current?.includes(idWithThinking) && !thinkingTaskIdsSendVar?.current?.includes(idWithThinking)) {
          //首次append，非首次更新
          appendMsg?.(msgSendVar?.current);
          textTaskIdsSendVar?.current?.push(idWithThinking);
          setShowStopAnswer(true);
        } else {
          updateMsg?.(idWithThinking, msgSendVar?.current);
          // updateMsg?.(idWithThinking, { ...msgSendVar?.current, sendError: true });
        }
      }

      if (msgData?.type === 'thinking') {
        thinkingTextSendVar.current[taskId] = (thinkingTextSendVar?.current?.[taskId] || '') + msgData?.content?.text;
        currentThinkingIdSendVar.current = taskId; // 记录为当前的思考id
        msgSendVar.current = {
          _id: taskId,
          messageId: msgId,
          type: 'thinking',
          createdAt: parsedData?.createTime,
          content: { text: '' },
          thinkContent: thinkingTextSendVar?.current?.[taskId] || '',
          isThinking: true,
        }
        // 同一段话的多个数据帧需要合并成一条
        if (!thinkingTaskIdsSendVar?.current?.includes(taskId)) {
          //首次append，非首次更新
          appendMsg?.(msgSendVar?.current);
          thinkingTaskIdsSendVar?.current.push(taskId);
          setShowStopAnswer(true);
        } else {
          updateMsg?.(taskId, msgSendVar?.current);
        }
      }

      // 魔方卡片渲染
      if (msgData?.type === 'card' && msgData?.content?.cardBizType) {
        // 魔方渲染路径
        // console.log('card ur= ' + options?.url);
        let lowCodeMsg = {
          _id: taskId,
          messageId: msgId,
          type: 'card',
          content: formatLowcodeContent(msgData?.content),
          createdAt: parsedData?.createTime,
        }
        appendMsg?.(lowCodeMsg);
        setShowStopAnswer(true);
      }

      // 参考资料先都累加起来，等到endFlag以后，加到相应的答案上
      if (msgData?.type === 'references' && msgData?.references) {
        referencesSendVar.current = [...referencesSendVar?.current, ...(msgData?.references ?? [])]
      }
    }
    // 记住上次解析到的list的位置
    prevSendHooksDataPosition.current = parsedDataListLength;
    // 解析结束标志
    if (parsedData?.endFlag) {
      // 如果有关联问
      // if (parsedData?.relatedQuestionList?.length > 0 && !isAorta) {
      //   appendMsg?.({
      //     createdAt: parsedData?.createTime,
      //     type: 'relatedQuestionList',
      //     content: {
      //       relatedQuestionList: parsedData?.relatedQuestionList,
      //     },
      //   });
      // }
      setShowStopAnswer(false);
      setLastPackageId('');
      // 可能即使结束了也没有能解析到的数据帧，不再转圈
      setTyping(false);
      const lastMsg = getLastMsg();
      // 找到结束标记对应的气泡消息（正常都是当前流的最后一条），出现点赞点踩


      const scoreMsg = getMessages()?.filter((t) => t._id === taskId)?.[0] || {};
      // 结束标记对应的气泡消息存在
      if (scoreMsg?._id) {
        updateMsg?.(taskId, {
          // ...scoreMsg,
          needFeedback: parsedData?.needFeedback ?? needFeedback,
          showHallucination,
          references: referencesSendVar?.current,
          showToken,
          totalTokens: parsedData?.totalTokens,
          totalTime: parsedData?.totalTime,
          // sendError: false,
        });
      } else {
        // 不存在则最后一条消息，出现点赞点踩，幻觉标识
        // msgId === lastMsg?.messageId来确定当前返回的答案是最后一条消息（接口有可能没有返回任何可以解析的帧导致没有追加新消息）
        if (msgId === lastMsg?.messageId) {
          updateMsg?.(lastMsg?._id, {
            // ...lastMsg,
            needFeedback: parsedData?.needFeedback ?? needFeedback,
            showHallucination,
            references: referencesSendVar?.current,
            showToken,
            totalTokens: parsedData?.totalTokens,
            totalTime: parsedData?.totalTime,
            // sendError: false,
          });
        }
      }
    }
  }

  const dealwithCommonData = (resultData: any) => {
    if (!conversationId?.current) {
      conversationId.current = resultData?.conversationId;
    }

    const list = resultData?.list;
    for (let i = 0; i < list?.length; i++) {
      const item = list?.[i] ?? {};
      const msg = {
        createdAt: item?.createTime,
        type: item?.type,
        content: item?.content,
        needFeedback: i === (list?.length - 1) ? resultData?.needFeedback ?? needFeedback : false,
        feedbackResult: resultData?.feedbackResult,
        _id: item?.taskId,
        messageId: resultData?.messageId,
      }
      appendMsg?.(msg);
    }
    if (resultData?.relatedQuestionList?.length > 0) {
      appendMsg?.({
        type: 'relatedQuestionList',
        content: {
          relatedQuestionList: resultData?.relatedQuestionList,
        },
      });
    }
  }

  // 何时选择 fetch 请求 SSE
  // 自定义请求头：需要设置自定义请求头或发送认证信息时。
  // 跨域限制：若需要自定义跨域请求头或其他方式的跨域认证。
  // 复杂的错误处理或网络重连：希望对连接中断或异常处理有更细粒度的控制。
  // 何时选择 EventSource
  // 快速集成 SSE：不需要复杂自定义配置，且希望自动处理消息解析、事件监听和网络断连重试。
  // 浏览器原生支持：希望借助浏览器内置的功能来简化 SSE 处理，避免手动处理流式数据。
  // async function fetchSSE(options: RequestOptions): Promise<void> {
  //   // 用 worker 优化流式处理
  //   const worker = createSSEWorker();

  //   let msgText: Record<string, string> = {};
  //   let thinkingText: Record<string, string> = {};
  //   let currentThinkingId = '';
  //   const textTaskIds: string[] = [];
  //   const thinkingTaskIds: string[] = [];
  //   const textThinkingIds: Record<string, string> = {};
  //   let references: ReferenceProps[] = [];
  //   // let url = interfacePrefix ? interfacePrefix + options?.url : options?.url;
  //   let url = interfacePrefix
  //     ? requestConfig.baseUrl.replace(/\/$/, '') + options.url // 去掉baseUrl末尾斜杠
  //     : options.url;

  //   // 如果url不是以http/https开头，自动补全为当前origin
  //   if (!/^https?:\/\//.test(url)) {
  //     url = window.location.origin + (url.startsWith('/') ? url : '/' + url);
  //   }

  //   worker.onmessage = (event: MessageEvent) => {
  //     const { type, data, error } = event.data;
  //     if (type === 'data') {
  //       // 解析事件
  //       const rawEvent = data;
  //       const eventObj = rawEvent.split("\n").reduce((acc: { [x: string]: any; }, line: { split: (arg0: string) => [any, ...any[]]; }) => {
  //         const [key, ...curValue] = line.split(":");
  //         acc[key] = curValue.join(":");
  //         return acc;
  //       }, {} as { [key: string]: string });
  //       if (eventObj.data) {
  //         try {
  //           let parsedData = JSON.parse(eventObj.data);
  //           if (options.responseTransfer) {
  //             parsedData = options.responseTransfer(parsedData);
  //           }
  //           if (!conversationId?.current) {
  //             conversationId.current = parsedData?.conversationId;
  //           }
  //           const msgId = parsedData?.messageId;
  //           const taskId = parsedData?.taskId || parsedData?.messageId;

  //           if (stoppedMsgs?.current?.includes(taskId)) {
  //             worker.terminate();
  //             return;
  //           }
  //           if (isNewConversationRef?.current === true) {
  //             worker.terminate();
  //             return;
  //           }

  //           const parsedDataListLength = parsedData?.list?.length;
  //           for (let i = 0; i < parsedDataListLength; i++) {
  //             const msgData = parsedData?.list?.[i];

  //             if ((msgData?.type === 'markdown' || msgData?.type === 'text' || msgData?.type === 'richtext') && msgData?.content?.text) {
  //               const idWithThinking = textThinkingIds[taskId] || taskId;
  //               msgText[idWithThinking] = (msgText?.[idWithThinking] ?? '') + msgData?.content?.text;

  //               if (currentThinkingId) {
  //                 textThinkingIds[taskId] = currentThinkingId;
  //                 currentThinkingId = '';
  //               }
  //               const msg = {
  //                 _id: idWithThinking,
  //                 messageId: msgId,
  //                 type: 'markdown',
  //                 createdAt: parsedData?.createTime,
  //                 content: { text: msgText?.[idWithThinking] },
  //                 thinkContent: thinkingText?.[idWithThinking] ?? '',
  //                 isThinking: false,
  //               }
  //               if (!textTaskIds?.includes(idWithThinking) && !thinkingTaskIds?.includes(idWithThinking)) {
  //                 appendMsg?.(msg);
  //                 textTaskIds.push(idWithThinking);
  //                 setShowStopAnswer(true);
  //               } else {
  //                 updateMsg?.(idWithThinking, msg);
  //               }
  //             }

  //             if (msgData?.type === 'thinking') {
  //               thinkingText[taskId] = (thinkingText?.[taskId] || '') + msgData?.content?.text;
  //               currentThinkingId = taskId;
  //               const msg = {
  //                 _id: taskId,
  //                 messageId: msgId,
  //                 type: 'thinking',
  //                 createdAt: parsedData?.createTime,
  //                 content: { text: '' },
  //                 thinkContent: thinkingText?.[taskId] || '',
  //                 isThinking: true,
  //               }
  //               if (!thinkingTaskIds?.includes(taskId)) {
  //                 appendMsg?.(msg);
  //                 thinkingTaskIds.push(taskId);
  //                 setShowStopAnswer(true);
  //               } else {
  //                 updateMsg?.(taskId, msg);
  //               }
  //             }

  //             if (msgData?.type === 'card' && msgData?.content?.cardBizType) {
  //               let lowCodeMsg = {
  //                 _id: taskId,
  //                 messageId: msgId,
  //                 type: 'card',
  //                 content: formatLowcodeContent(msgData?.content),
  //                 createdAt: parsedData?.createTime,
  //               }
  //               appendMsg?.(lowCodeMsg);
  //               setShowStopAnswer(true);
  //             }

  //             if (msgData?.type === 'references' && msgData?.references) {
  //               references = [...references, ...(msgData?.references ?? [])]
  //             }
  //           }

  //           if (parsedData?.relatedQuestionList?.length > 0 && !isAorta) {
  //             appendMsg?.({
  //               createdAt: parsedData?.createTime,
  //               type: 'relatedQuestionList',
  //               content: {
  //                 relatedQuestionList: parsedData?.relatedQuestionList,
  //               },
  //             });
  //           }

  //           if (parsedData?.endFlag) {
  //             setShowStopAnswer(false);
  //             setTyping(false);
  //             const lastMsg = getLastMsg();
  //             const scoreMsg = getMessages()?.filter((t) => t._id === taskId)?.[0] || {};
  //             if (scoreMsg?._id) {
  //               updateMsg?.(taskId, {
  //                 ...scoreMsg,
  //                 needFeedback: parsedData?.needFeedback ?? true,
  //                 showHallucination: true,
  //                 references,
  //               });
  //             } else {
  //               if (msgId === lastMsg?.messageId) {
  //                 updateMsg?.(lastMsg?._id, {
  //                   ...lastMsg,
  //                   needFeedback: parsedData?.needFeedback ?? true,
  //                   showHallucination: true,
  //                   references,
  //                 });
  //               }
  //             }
  //             if (parsedData?.shootIntention === false) {
  //               appendMsg?.({
  //                 createdAt: parsedData?.createTime,
  //                 type: 'text',
  //                 content: {
  //                   text: parsedData?.backUpTalking,
  //                 },
  //               });
  //             }
  //             worker.terminate();
  //           }
  //         } catch (err) {
  //           setTyping(false);
  //           setUnsend();
  //           worker.terminate();
  //         }
  //       }
  //     } else if (type === 'end') {
  //       setShowStopAnswer(false);
  //       setTyping(false);
  //       worker.terminate();
  //     } else if (type === 'error') {
  //       setTyping(false);
  //       setUnsend();
  //       worker.terminate();
  //       console.error('SSE Worker error:', error);
  //     }
  //   };

  //   worker.postMessage({
  //     type: 'start',
  //     payload: {
  //       url,
  //       headers: {
  //         'Accept': 'text/event-stream',
  //         'Content-Type': 'application/json',
  //         ...options?.headers,
  //       },
  //       body: options?.payload,
  //     },
  //   });
  // }

  // const handleFirstLoad = () => {
  //   localStorage.setItem('hasChatUIComponentLoaded', 'true');
  //   setIsComponentFirstLoad(false);
  // }

  // 接口请求发送回调
  const handleSend = useCallback(async (type: string, val: string, payload?: any, transformedFiles?: UploadFile[]) => {
    
    // 重置relatedQuestionList处理标记
    relatedQuestionListProcessed.current = false;
    
    setSendHooksDataCache({});
    // 输入框没有东西，点击发送不做任何处理
    if (!payload?.agentId && !val) {
      toast.show('请输入您的问题', undefined, 1000, 'center');
      return false;
    }

    // 如果需要校验敏感词
    if (sensitiveConfig?.url) {
      const sensitiveRes = await postRequest(interfacePrefix, {
        payload: {
          question: val,
          appId: appId,
          userId: userId,
          sceneId: sceneId,
        },
        ...sensitiveConfig
      });

      // 如果敏感词校验不通过则提示
      if (!sensitiveRes?.resultData?.ifPass) {
        setShowSensitiveError(true);
        // toast.show('您的输入可能包含投资者敏感信息，请核对~', undefined, 1000, 'sensitive', document.getElementById("AiChatComposer") as HTMLElement);
        return false;
      }
    }

    const isFirstChat = isNewConversationRef.current;

    setIsNewConversation(false);
    isNewConversationRef.current = false;

    // if (isComponentFirstLoad) {
    //   handleFirstLoad();
    // }

    // console.log('val=' + val);
    if (getTyping() || showStopAnswer) {
      toast.show('稍等片刻，等回答生成后再发送哦～', undefined, 1000, 'typing', document.getElementById("MessageContainer") as HTMLElement);
      return false;
    }

    if ((type === 'text' && val.trim()) || payload?.agentId) {
      if (sendConfig?.stream) {
        // 重置回0，避免被上次答案计算的位置影响
        prevSendHooksDataPosition.current = 0;
      }
      // 仅在会话中显示时才追加消息
      if(!payload.hideConversation){
        appendMsg?.({
          type: 'text',
          content: { text: val },
          position: 'right',
        });
      }
      let questionExtends = {} as any;

      if (transformedFiles && transformedFiles?.length > 0) {
        const formatFiles = transformedFiles?.map((file: any) => {
          if (!file?.type || file?.type === 'image' || file?.type === 'file') {
            return ({
              type: file?.type || 'file',
              size: file?.content?.size,
              name: file?.content?.name,
              path: file?.content?.response?.Result?.Path || file.content?.path,
              picUrl: file?.content?.path,
              // 如果后端没有返回完整的文件url，需要根据path拼接前缀，则使用padUrlPrefix
              url: file?.content?.response?.Result?.Url
                || (composerConfig?.uploadConfig?.downloadAction && `${composerConfig?.uploadConfig?.downloadAction}/${file?.content?.response?.resultData?.fileId}`)
                || file?.content?.url || '',
            })
          }
          if (file?.type === 'web') {
            return ({
              type: file?.type,
              title: file?.content?.title,
              url: file?.content?.url,
              content: file?.content?.content,  // 网页dom信息
              ...file,
            });
          }
          if (file?.type === 'text') {
            return ({
              type: file?.type,
              title: '引用文本',
              text: file?.content,
              name: file?.content,
              ...file,
            });
          }

          // 默认返回文件类型的参数
          return ({
            type: file?.type || 'file',
            size: file?.size,
            name: file?.name,
            path: file?.response?.Result?.Path,
            // 如果后端没有返回完整的文件url，需要根据path拼接前缀，则使用padUrlPrefix
            url: file?.response?.Result?.Url
              || (composerConfig?.uploadConfig?.padUrlPrefix && composerConfig?.uploadConfig?.padUrlPrefix(file?.response?.Result?.Path))
              || '',
          })
        });
        questionExtends.files = formatFiles;
        // 仅在会话中显示时才追加消息
        if(!payload.hideConversation){
          for (let index = 0; index < formatFiles?.length; index++) {
          appendMsg?.({
            type: formatFiles[index]?.type,
            content: { ...formatFiles[index] },
            position: 'right',
          });
        }
        }
        
      }
      // 仅在会话中显示时才追加消息
      if(!payload.hideConversation){
        setTyping(true);
        // 发送了问题以后，滚动到最下面（避免发送时滚动还没停，延时一点再scrollToEnd滚一次）
        setTimeout(() => {
          if (msgsRef && msgsRef.current) {
            msgsRef.current.scrollToEnd({ animated: false, force: true });
          }
        }, 100);
      }

      if (sendConfig) {
        sendConfig.payload = {
          hideConversation: false, // 默认所有会话都在历史列表中展示，如果需要隐藏，则通过payload传入改写
          question: val,
          appId: appId,
          userId: userId,
          sceneId: sceneId,
          source: source,
          conversationId: conversationId?.current,
          enableInternetSearch,
          isFirstChat,
          questionExtends,
          inputs: chatConfigRef?.current?.getData(),
          actions: actions?.map((action: FrontendAction) => ({ name: action.name, description: action.description })),
          extendParams: {
            ...payload.extendParams,
          },
          ...payload,
        };
        // 根据conversationId是否为空，来判断是否需要创建会话。因为该逻辑各业务线不通用，手动传入Hook以下参数
        if(!sendConfig.payload.conversationId  || sendConfig.payload.conversationId === ''){
          sendConfig.payload.needCreateConversation = true;
          sendConfig.payload.createConversationUrl = initConfig?.url ?? '';
        } else {
          sendConfig.payload.needCreateConversation = false;
          sendConfig.payload.createConversationUrl = '';
        }
      }

      sendHooksRun(sendConfig?.payload);

      return true;

      // if (sendConfig.requestTransfer) {
      //   sendConfig.payload = sendConfig.requestTransfer(sendConfig.payload);
      // }
      // //1、sse接口请求，使用 fetchSSE 函数请求流式接口
      // if (sendConfig?.stream) {
      //   fetchSSE(sendConfig);
      // } else {
      //   //2、普通接口请求
      //   postRequest(interfacePrefix, sendConfig

      //     //   //2、Else 非sse接口请求
      //     //   fetchClient.post<Tmessage>({
      //     //     url: sendConfig?.url,
      //     //     body: { question: val, appId: appId, userId: userId, sceneId: sceneId },
      //     //     headers: sendConfig?.headers
      //   ).then((data: any) => {
      //     // console.log('Data received:', data); // 处理或显示数据
      //     if (data && data?.code === '0') {
      //       const resultData = data?.resultData;
      //       if (!conversationId?.current) {
      //         conversationId.current = resultData?.conversationId;
      //       }

      //       const list = resultData?.list;
      //       for (let i = 0; i < list?.length; i++) {
      //         const item = list?.[i] ?? {};
      //         const msg = {
      //           createdAt: item?.createTime,
      //           type: item?.type,
      //           content: item?.content,
      //           needFeedback: i === (list?.length - 1) ? resultData?.needFeedback : false,
      //           feedbackResult: resultData?.feedbackResult,
      //           _id: item?.taskId,
      //           messageId: resultData?.messageId,
      //         }
      //         appendMsg?.(msg);
      //       }

      //       if (resultData?.relatedQuestionList?.length > 0) {
      //         appendMsg?.({
      //           type: 'relatedQuestionList',
      //           content: {
      //             relatedQuestionList: resultData?.relatedQuestionList,
      //           },
      //         });
      //       }
      //     } else {
      //       setTyping(false);
      //       setUnsend();
      //     }
      //   })
      //     .catch(error => {
      //       setTyping(false);
      //       setUnsend();
      //       console.error('Error fetching data:', error); // 错误处理
      //     });
      // }
    }
    if(isMobile){
      setKeyboardShow(false);
    }
    return true;
  }, [showStopAnswer, enableInternetSearch, interfacePrefix]);

  // 输入内容改变则不显示敏感信息
  const handleInputChange = useCallback(() => {
    setShowSensitiveError(false);
  }, []);

  function handleQuickReplyClick(item: QuickReplyItemProps) {
    // if (isComponentFirstLoad) {
    //   handleFirstLoad();
    // }
    handleSend('text', item?.content || item?.code || item?.name || '', { allowChange: false });
  }

  const dealwithHistoryMessageList = useCallback((data: any) => {
    if (data && data?.code === '0') {
      const resultData = data?.resultData ?? {};
      const list = resultData?.list ?? [];
      const msgs = [];
      let thinkingText = '';
      for (let i = 0; i < list?.length; i++) {
        const item = list?.[i] ?? {};
        const position = item?.role === 'user' ? 'right' : 'left' as MessageProps['position'];
        const msgBubbleList = item?.list ?? [];
        let references: ReferenceProps[] = [];

        for (let j = 0; j < msgBubbleList?.length; j++) {
          const msgBubble = msgBubbleList[j];
          // 非问题且同一个messageId的最后一条需要展示点赞点踩/展示幻觉标识
          const lastQuestionMessage = j === (msgBubbleList?.length - 1) && item?.role !== 'user';
          // 思维链消息不追加,记录下来，等下一次文本消息再追加气泡
          if (msgBubble?.type === 'thinking') {
            thinkingText = msgBubble?.content?.text;
            continue;
          }
          if (msgBubble?.type === 'references' && msgBubble?.references) {
            references = [...references, ...(msgBubble?.references ?? [])];
          }
          const needThinkText = msgBubble?.type === 'text' || msgBubble?.type === 'markdown' || msgBubble?.type === 'richtext';
          const msg = {
            type: msgBubble?.type,
            // 魔方卡片需要拼一个url出来
            content: msgBubble?.type === 'card' ? formatLowcodeContent(msgBubble?.content) : msgBubble?.content,
            position,
            _id: getRandomString(), // 因为messageId一个可能对应多条，不能用messageId作为id，否则页面渲染会混乱
            messageId: item?.messageId,
            needFeedback: lastQuestionMessage ? item?.needFeedback ?? needFeedback : false,
            feedbackResult: lastQuestionMessage ? item?.feedbackResult : null,
            createdAt: position === 'right' ? (j === 0 ? item?.createTime : undefined) : item?.createTime,
            showHallucination: lastQuestionMessage && showHallucination,
            thinkContent: needThinkText ? thinkingText : '',
            // 用来避免思考过程中停止，历史消息返回一半，思维链会一直展示思考中，此时设置为stopped
            stopped: item?.role !== 'user' && getThinkContent(msgBubble?.content?.text)?.isThinking,
            references: lastQuestionMessage ? references : [],
            showToken: lastQuestionMessage && showToken,
            totalTokens: item?.totalTokens,
            totalTime: item?.totalTime,
            thinkTime: item?.thinkTime,
          }
          // 清掉思维链文本，这样如果一个messageId有多个思维链的话，这个思维链可以成为下一个文本消息的思维链
          if (needThinkText) {
            thinkingText = '';
          }
          // 魔方卡片判断下msgData?.content?.cardBizType
          if (msgBubble?.type === 'card') {
            if (msgBubble?.content?.cardBizType) {
              msgs.push(msg);
            }
          } else {
            if (msgBubble?.content) {
              msgs.push(msg);
            }
          }
        }

        // 添加文件
        const files = item?.queryExtends?.files || [];
        for (let index = 0; index < files?.length; index++) {
          msgs.push({
            type: 'file',
            _id: getRandomString(),
            content: { ...files[index] },
            position: 'right',
          } as MessageProps);
        }
      }
      prependMsgs?.(msgs);
      setNoMoreHistory(resultData?.noMore);
      historyNextOffset.current = resultData?.nextOffset;
      historyPageNum.current = historyPageNum?.current + 1;
      if (needScrollToEnd.current) {
        setTimeout(() => {
          if (msgsRef && msgsRef.current) {
            msgsRef.current.scrollToEnd({ animated: false, force: true });
          }
        }, 100);
      }
    }
  }, [])

  const handleRefresh = useCallback(() => {
    // 接口还没返回的时候，禁用下拉刷新
    setNoMoreHistory(true);

    // return fetchClient.post<Tmessage>({
    //   url: historyConfig?.url,
    //   body: {
    //     offsetMessagePkId: historyNextOffset.current,
    //     pageNum: historyPageNum.current,
    //     pageSize: historyConfig?.pageSize || 6,
    //     appId: appId,
    //     userId: userId,
    //     sceneId: sceneId,
    //   },
    //   headers: historyConfig?.headers
    return postRequest(interfacePrefix, {
      payload: requestConfig?.platform === 'hiAgent' ? {
        UserID: userId,
        AppConversationID: conversationId?.current,
        Limit: 1 * (historyConfig?.pageSize || 100),
        AppKey: requestConfig?.appKey,
      } : {
        offsetMessagePkId: historyNextOffset?.current,
        pageNum: historyPageNum?.current,
        pageSize: historyConfig?.pageSize || 100,
        appId: appId,
        userId: userId,
        sceneId: sceneId,
        conversationId: conversationId?.current,
      },
      ...historyConfig
    }).then((data) => {
      dealwithHistoryMessageList(data);
    });
  }, []);

  const toggleHistory = () => {
    // 打开面板的时候,刷新历史会话
    if (!isHistoryOpen && showPushHistory) {
      if (historyConversationConfig?.url) {
        // postRequest(interfacePrefix, {
        //   payload: { appId: appId, userId: userId, sceneId: sceneId },
        //   ...historyConversationConfig
        // }).then((data) => {
        //   if (data && data?.code === '0') {
        //     const list = data?.resultData?.list ?? [];
        //     setHistoryConversationList(list);
        //   }
        // });
        if (showPushHistory) {
          // 需要打开历史会话面板的情况在打开的时候查
          historyConversationHooksRun();
        }
      }
    }

    // 埋点上报
    const params = LogPointConfigMap.get(isHistoryOpen ? 'toggleHistoryClose' : 'toggleHistoryOpen');
    if (params) {
      onConsolidateReports(params);
    }

    if (showPushHistory) {
      setIsHistoryOpen(!isHistoryOpen);
    }
  };

  const handleNewConversation = () => {
    // 开新会话，如果历史面板打开则关闭
    if (isHistoryOpen) {
      toggleHistory();
    }

    // 先判断一下当前会话有没有消息，如果没有则表示已经是最新会话了,什么也不做
    if (isNewConversationRef?.current) {
      if (isMobile) {
        toast.show('当前已是新对话', undefined, 1000, 'center');
      }
      return;
    }

    // 清空输入框和上传文件 & 如果当前停留在技能输入框中，则切换到通用输入框
    if (composerInputRef && composerInputRef.current) {
      composerInputRef.current.setText('');
      composerInputRef.current.setComposer?.("common", { title: ''});
    }
    setShowSensitiveError(false);

    if (showStopAnswer) {
      handleStopAnswer();
    }

    // 埋点上报
    const params = LogPointConfigMap.get('handleNewConversation');
    if (params) {
      onConsolidateReports(params);
    }

    setIsNewConversation(true);
    isNewConversationRef.current = true;
    resetList();

    if (initConfig?.url) {
      // 调用欢迎语接口获取conversationId
      // postRequest(interfacePrefix, {
      //   payload: { appId: appId, userId: userId, sceneId: sceneId },
      //   ...initConfig
      // }).then((data) => {
      //   if (data && data?.code === '0') {
      //     const resultData = data?.resultData;
      //     setWelcomeInfo(resultData || {});
      //     conversationId.current = resultData?.conversationId;
      //     setNoMoreHistory(true);
      //   }
      // });
      initHooksRun();
    } else {
      // 不调用接口拿conversationId的情况清空
      conversationId.current = '';
    }
  }

  useEffect(() => {
    if (initHooksData && initHooksData?.code === '0') {
      const resultData = initHooksData?.resultData;
      setWelcomeInfo(resultData || {});
      conversationId.current = resultData?.conversationId;
      setNoMoreHistory(true);
    }
  }, [initHooksData]);

  useEffect(() => {
    // console.log('historyConversationHooksData', historyConversationHooksData);
    if (historyConversationHooksData && historyConversationHooksData?.code === '0') {
      const list = historyConversationHooksData?.resultData?.list ?? [];
      setHistoryConversationList(list);
    }
  }, [historyConversationHooksData]);

  useEffect(() => {
    dealwithHistoryMessageList(historyHooksData);
  }, [historyHooksData]);

  const { executeAction, isProcessing } = useActionHook(actions);

  useEffect(() => {
    if (sendHooksData && sendHooksData?.code === '0') {
      const resultData = sendHooksData?.resultData;
      const newSendHooksDataCache = {
        ...sendHooksDataCache,
        ...resultData,
        list: [...(sendHooksDataCache?.list || []), ...(resultData?.list || [])],
      };
      // console.log('___sendHooksData.resultData', sendHooksData.resultData);
      // const hshshshs = sendHooksDataCache;
      // console.log('___sendHooksDataCache', hshshshs);
      // console.log('___newSendHooksDataCache', newSendHooksDataCache);
      setSendHooksDataCache(newSendHooksDataCache);
      if (sendConfig?.stream) {
        // 如果接口返回带有action，则命中actions中的具体action，useActionHook
        if(hasFrontActionFlag(newSendHooksDataCache, actions)){
          // 目前action的触发需要经过onSend发送消息至会话中，需要手动处理拦截tying，待优化
          if (newSendHooksDataCache?.endFlag) {
            setShowStopAnswer(false);
            setTyping(false);
          }
          executeAction(newSendHooksDataCache?.action?.name, sendHooksData)         
        } else {
          dealwithSSEData(newSendHooksDataCache);
        }       
      } else {
        // 如果接口返回带有action，则命中actions中的具体action，useActionHook
        if(hasFrontActionFlag(resultData, actions)){
          if(isProcessing){
            setTyping(true);
            setShowStopAnswer(true);
          } else {
            setTyping(false);
            setShowStopAnswer(false);
          }       
          // @ts-ignore
          executeAction(resultData?.action?.name, resultData);
        } else {
          dealwithCommonData(resultData);
        }
      }
      // 保证只在返回数据endFlag为true-最后一条的时候执行
      if (!showPushHistory && changedConversation.current && sendHooksData?.resultData?.endFlag && historyConversationConfig?.url) {
        // 不是打开历史会话面板的情况需要在发送成功再查一次历史会话列表
        historyConversationHooksRun();
      }
    }
  }, [sendHooksData]);

  useEffect(() => {
    const flag = sendHooksDataCache?.endFlag;
    if (flag) {
      if (sendHooksDataCache?.relatedQuestionList?.length > 0 && !isAorta && !relatedQuestionListProcessed.current) {
        relatedQuestionListProcessed.current = true; // 标记为已处理
        appendMsg?.({
          createdAt: sendHooksDataCache?.createTime,
          type: 'relatedQuestionList',
          content: {
            relatedQuestionList: sendHooksDataCache?.relatedQuestionList,
          },
        });
      }
    }
  }, [sendHooksDataCache?.endFlag])

  useEffect(() => {
    // 如果发送有报错，则打上报错标记
    if (sendHooksError) {
      setTyping(false);
      setUnsend();
      config?.bridge?.sendErrorCallback?.(sendHooksError);
    }
  }, [sendHooksError])

  // useEffect(() => {
  //   if (sendHooksData?.code === '0' && sendHooksData?.resultData?.endFlag) {
  //     if (!showPushHistory && changedConversation.current) {
  //       // 不是打开历史会话面板的情况需要在发送成功再查一次历史会话列表
  //       historyConversationHooksRun();
  //     }
  //   }
  // }, [sendHooksData?.resultData?.endFlag])

  // useEffect(() => {
  //   // 初始化完成且有用户id的情况，上报用户id
  //   if(userId && appId){
  //     setUUID(userId);
  //   }
  // },[userId, isReady])

  // useEffect(() => {
  //   if(isReady){
  //     // 初始化即上报加载完成
  //     logPV(PV_LOADED_PARAMS);
  //   }
  // },[isReady])

  useEffect(() => {
  //   // Xlog初始化
  //   if(!isSCKDoc){
  //     initXlog(appId, isDev);
  //   }
    
    if (isSafari()) {
      document.documentElement.dataset.safari = '';
    }
    if (initConfig?.url) {
      initHooksRun();
      // 初始进入
      // fetchClient.post<Tmessage>({
      //   url: initConfig?.url,
      //   body: { appId: appId, userId: userId, sceneId: sceneId },
      //   headers: initConfig?.headers
      // postRequest(interfacePrefix, {
      //   payload: { appId: appId, userId: userId, sceneId: sceneId },
      //   ...initConfig
      // }).then((data) => {
      //   if (data && data?.code === '0') {
      //     const resultData = data?.resultData;
      //     setWelcomeInfo(resultData || {});
      //     // setIsComponentFirstLoad(!resultData?.hasHistory);
      //     conversationId.current = resultData?.conversationId;

      //     // if (!resultData?.hasHistory) {
      //     // setIsComponentFirstLoad(!localStorage.getItem('hasChatUIComponentLoaded'));
      //     // }
      //     // if (resultData?.title || resultData?.subtitle) {
      //     // if (resultData?.welcomeMsg) {
      //     //   appendMsg?.({
      //     //     type: 'text',
      //     //     // content: { text: `${resultData?.title || ''}${resultData?.subtitle || ''}` },
      //     //     content: { text: `${resultData?.welcomeMsg || ''}` },
      //     //     createdAt: Date.now(),
      //     //     hasTime: true,
      //     //   });
      //     // }

      //     // if (resultData?.relatedQuestionList?.length > 0) {
      //     //   appendMsg?.({
      //     //     type: 'relatedQuestionList',
      //     //     content: {
      //     //       relatedQuestionList: resultData?.relatedQuestionList,
      //     //     },
      //     //   });
      //     // }
      //     // setNoMoreHistory(!resultData?.hasHistory);
      //   }
      // });
    }
    // if (quickReplyConfig?.url) {
    //   // 快捷问题查询
    //   // fetchClient.post<Tmessage>({
    //   //   url: quickReplyConfig?.url,
    //   //   body: { appId: appId, userId: userId, sceneId: sceneId },
    //   //   headers: quickReplyConfig?.headers
    //   postRequest(interfacePrefix, {
    //     payload: { appId: appId, userId: userId, sceneId: sceneId },
    //     ...quickReplyConfig
    //   }).then((data) => {
    //     if (data && data?.code === '0') {
    //       const quickReply = data?.resultData?.quickReply ?? [];
    //       const newQuickReplies = [];

    //       for (let i = 0; i < quickReply?.length; i++) {
    //         newQuickReplies.push({
    //           name: quickReply[i]?.title,
    //           code: quickReply[i]?.content,
    //         });
    //       }
    //       setQuickReplies(newQuickReplies)
    //     }
    //   });
    // }

    if (historyConversationConfig?.url) {
      // 查历史会话
      // postRequest(interfacePrefix, {
      //   payload: { appId: appId, userId: userId, sceneId: sceneId },
      //   ...historyConversationConfig
      // }).then((data) => {
      //   if (data && data?.code === '0') {
      //     const list = data?.resultData?.list ?? [];
      //     setHistoryConversationList(list);
      //     // 如果没有历史会话，那展示新会话页面
      //     // if (list?.length === 0) {
      //     //   setIsNewConversation(true);
      //     //   isNewConversationRef.current = true;
      //     // } else if (!showNewConversation) {
      //     //   // 如果没有要展示新会话，则默认展示历史会话中最新的那一条的历史消息,并且滚动到底部
      //     //   conversationId.current = list?.[0]?.conversationId
      //     //   handleRefresh(true);
      //     // }
      //   }
      // });
      historyConversationHooksRun();
    }

    if (queryRiskReadConfig?.url) {
      // 查询是否阅读了风险提示接口
      postRequest(interfacePrefix, {
        payload: { appId: appId, userId: userId, sceneId: sceneId, businessTypeCode: "AortaAIContract" },
        ...queryRiskReadConfig
      }).then((data) => {
        if (data && data?.code === '0') {
          setRiskRead(data?.resultData?.hasRead ?? false);
          // 没签署过强制提示风险
          if (!data?.resultData?.hasRead) {
            setShowRiskTip(true);
          }
        }
      });
    }

    if (feedbackLabelConfig?.url) {
      postRequest(interfacePrefix, {

        payload: { appId: appId, userId: userId, sceneId: sceneId },
        ...feedbackLabelConfig
      }
      ).then((data) => {
        if (data && data?.code === '0') {
          setFeedbackLabels(data?.resultData ?? []);
        }
      })
    }

  }, []);

  const toggleEnableInternetSearch = () => {
    setEnableInternetSearch(!enableInternetSearch);

    // 埋点上报
    const params = !enableInternetSearch ? LogPointConfigMap.get('toggleEnableInternetSearchOpen') : LogPointConfigMap.get('toggleEnableInternetSearchClose');
    if (params) {
      onConsolidateReports(params);
    }
  }

  // 切换历史会话
  const selectHistoryConversation = (conversationItem: ConversationItemProps) => {
    // 如果点中的是本来就高亮的，关闭弹窗，啥也不做
    if (conversationId?.current === conversationItem.conversationId) {
      changedConversation.current = false;
      toggleHistory();
      return;
    }

    conversationId.current = conversationItem.conversationId;
    changedConversation.current = true;

    setIsNewConversation(false);
    isNewConversationRef.current = false;

    if (showStopAnswer) {
      handleStopAnswer();
    }

    // 清空输入框
    if (composerInputRef && composerInputRef.current) {
      composerInputRef.current.setText('');
    }

    resetList();

    toggleHistory();

    // 切换历史会话消除敏感词提示
    setShowSensitiveError(false);

    historyPageNum.current = 1;
    historyNextOffset.current = '';
    // 刷新历史会话消息，并滚动到底部
    needScrollToEnd.current = true;
    setNoMoreHistory(true);

    // 支持动态传参
    historyHooksRun({ conversationId: conversationId?.current, pageNum: historyPageNum.current });
    // 埋点上报
    const params = LogPointConfigMap.get('selectHistoryConversation');
    if (params) {
      onConsolidateReports({
        ...params,
        btn_title: {
          ...(typeof params.btn_title === 'object' ? params.btn_title : {}),
          value: conversationItem.conversationId,
        }
      });
    }
  }
  // 重命名历史会话
  const handleRenameHistoryConversation = (conversationItem: ConversationItemProps) => {
    console.log('conversationItem', conversationItem);

  }
  //删除历史会话
  const handleDeleteHistoryConversation = (conversationItem: ConversationItemProps) => {
    console.log('开始删除会话:', conversationItem);
    
    if(conversationItem?.conversationId){
      console.log('调用删除API, conversationId:', conversationItem.conversationId);
      deleteHistoryConversationHooksRun({ conversationId: conversationItem.conversationId });
    } else {
      console.error('conversationId不存在:', conversationItem);
    }
  }

  const handleRiskOpen = () => {
    setShowRiskTip(true);
    // 埋点上报
    const params = LogPointConfigMap.get('handleRiskOpen');
    if (params) {
      onConsolidateReports(params);
    }
  }

  const handleRiskClose = () => {
    setShowRiskTip(false);
    // 埋点上报
    const params = LogPointConfigMap.get('handleRiskClose');
    if (params) {
      onConsolidateReports(params);
    }
  };

  const handleRiskAgree = () => {
    if (riskReadConfig?.url) {
      // 埋点上报
      const params = LogPointConfigMap.get('handleRiskAgree');
      if (params) {
        onConsolidateReports(params);
      }
      postRequest(interfacePrefix, {
        payload: {
          appId: appId,
          userId: userId,
          sceneId: sceneId,
          businessTypeCode: "AortaAIContract",
          // 20250318这种数字格式
          sequenceNumber: Number(new Date().toISOString().slice(0, 10).replace(/-/g, '')),
          // 2025-03-18 11:37:27这种格式
          stamp: getDateFormat(Date.now()),
        },
        ...riskReadConfig
      }).then((res) => {
        if (res?.resultData?.ifSuccess) {
          setRiskRead(true);
        }
      });
    }
  }
  const isHistoryShow = typeof props.historyConversation?.show === 'boolean' ? props.historyConversation?.show : true;

  const historyPanelStyle = pushPosition === 'left'
    ?
    {
      left: 0,
      display: isHistoryShow ? '' : 'none',
      transform: 'translateX(-100%)', /* 初始隐藏在左边 101去掉*/
      width: `${pushPercent}%`, /* 历史面板展开宽度 */
    }
    : {
      right: 0,
      display: isHistoryShow ? '' : 'none',
      transform: 'translateX(100%)', /* 初始隐藏在右边 */
      width: `${pushPercent}%`, /* 历史面板展开宽度 */
    };

  /* 历史面板展开时复位 */
  const historyPanelPushStyle = isHistoryOpen ? { transform: 'translateX(0)' } : {}

  /* 历史面板展开时，用transform实现视觉偏移 */
  const chatPannelPushStyle = isHistoryShow ? (
    pushPosition === 'left'
      ? {
        transform: `translateX(${pushPercent}%)` /* 向左推相应宽度 */,
      }
      : {
        transform: `translateX(-${pushPercent}%)` /* 向右推相应宽度 */,
      }) : {};



  const chatContextState: ChatContext = useMemo(() => {

    const data = {
      showStopAnswer,
      isNewConversation,
      setShowStopAnswer,
      setIsNewConversation,
      historyConversationList,
      conversationId,
    };
    window.document.dispatchEvent(new CustomEvent('chatui-update-chat-context', { detail: data }))
    return data;
  }, [showStopAnswer, isNewConversation, historyConversationList, conversationId?.current]);

  useImperativeHandle(ref, () => {
    return {
      chatContext: {
        ...chatContextState,
        config,
        onSend: onSend || handleSend,
        handleStopAnswer,
        handleNewConversation,
        selectHistoryConversation,
        toggleHistory,
        setText: (text: string) => {
          if (composerInputRef && composerInputRef.current) {
            composerInputRef.current.setText(text);
          }
        },
        handleInputFocus,
        handleInputBlur,
      },
    };
  }, [chatContextState, config, onSend, handleStopAnswer]);

  // 监听网络变化
  useEffect(() => {
    const handleOnline = () => {
      // console.log('handleOnline=========');
      setIsNetworkOnline(true);
      setPendingRetry(true);
    };
    const handleOffline = () => {
      // console.log('handleOffline=========');
      setIsNetworkOnline(false);
      // setUnsend();
    };
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 网络恢复判断
  useEffect(() => {
    if (pendingRetry && isNetworkOnline && lastPackageId) {
      
      // console.log('网络恢复==lastPackageId=' + lastPackageId + ',conversationId='+conversationId?.current + ',messageid=' + getLastMsg()?.messageId);

      
      // 找到最后一条问题消息
      const lastMessage = getLastMsg();
      // 表示最后一条消息是回答，取消标记答案生成异常，并且显示停止生成按钮
      updateMsg?.(lastMessage?._id, { ...lastMessage, sendError: false });
      // 显示停止生成按钮
      setShowStopAnswer(true);

      
      sendConfig.payload = {
        ...sendConfig?.payload,
        extendParams:{
          lastPacketId: lastPackageId,
          messageId: getLastMsg()?.messageId,
        },
        conversationId: conversationId?.current,
      }
      // 重新发起请求，带上 packageId
      sendHooksRun({
        ...sendConfig?.payload,
        // 你可以根据后端协议加上其他必要参数
      });
      setPendingRetry(false);
    }
  }, [pendingRetry, isNetworkOnline, lastPackageId]);

  const handleDeleteMessage = useCallback((message: MessageProps) => {
    // 埋点上报
    const params = LogPointConfigMap.get('handleDeleteMessage');
    if (params) {
      onConsolidateReports(params);
    }

    if (deleteMessageConfig?.url) {
      deleteMsg?.(message?._id || '');
      const role = message?.position === 'right' ? 'user' : 'assistant';
      
      deleteMessagesHooksRun({
        appKey: requestConfig?.appKey,
        appId: appId,
        userId: userId,
        messageIds: message?.messageId ? [{
          messageId: message?.messageId || '',
          role
        }] : [],
        conversationId: conversationId?.current,
      });
    }
  }, [messages]);

  const handleQuote = useCallback((message: MessageProps) => {
    const { content } = message;
    let citeInfo: CiteInfoProps = {} as CiteInfoProps;
    switch (message?.type) {
      case 'text':
      case 'richtext':
      case 'thinking':
      case 'markdown':
      case 'citetext':
        citeInfo = { type: 'text', content: content?.text };
        break;
      case 'web':
      case 'citeweb':
        citeInfo = {
          type: 'web',
          content: { title: content?.title, url: content?.url, favicon: '' }
        };
        break;
      case 'image':
        citeInfo = {
          type: 'image',
          content: { path: content?.picUrl, name: '', size: 0, url: '' }
        };
        break;
      case 'file':
        citeInfo = {
          type: message?.type,
          content: { name: content?.name, size: content?.size, url: content?.url, icon: '', uid: content?.uid || '' }
        };
        break;
      default:
        citeInfo = {} as CiteInfoProps;
    }
    if (composerInputRef && composerInputRef.current) {
      composerInputRef.current?.setCite?.(citeInfo?.type, citeInfo?.content);
    }
  }, [composerInputRef]);

  const updateSkillOnClick = (skill: Skill[], targetKeys: string[]) => {
    return skill?.map(item => {
      // 浅拷贝当前对象（避免直接修改原对象）
      const newItem = { ...item };
      let ComposerType: ComposerType;
      // 如果当前项的 key 在 targetKeys 中，替换 onClick
      if (targetKeys.includes(newItem.key)) {
        if (newItem.key === 'translate') {
          ComposerType = 'translate';
        } else if (newItem.key === 'summary') {
          ComposerType = 'web';
        } else if (showTexComposerCodes?.includes(newItem.key)) {
          ComposerType = 'text';
        }
        newItem.onClick = () => {
          // console.log(`Matched item: ${{ ...newItem, title: item?.label }}`, ComposerType);
          if (composerInputRef && composerInputRef.current) {
            // 切换输入框 测试
            composerInputRef.current?.setComposer?.(ComposerType, { title: item?.label, agentId: newItem.agentId, ...newItem });
            // composerInputRef.current.setComposer('translate',{ title:"AI翻译"});
            // composerInputRef.current.setComposer('web',{ title:"wen输入框"});
            // composerInputRef.current.setComposer('text',{ title:"文本输入框"});

            // 设置引用测试
            // composerInputRef.current.setCite('text', '我很好');

            // composerInputRef.current.setCite('web', {
            //   title: 'Baidu',
            //   url: 'https://www.baidu.com/',
            //   favicon: 'https://www.baidu.com/favicon.ico',
            // });

            // composerInputRef.current.setCite('image', {
            //   path: '',
            //   name: '',
            //   size: 123,
            //   url: 'https://img0.baidu.com/it/u=1230336655,1371416184&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
            // });

            // composerInputRef.current.setCite('file', {
            //   name: 'web-HTSC.xlsx',
            //   url: 'http://eip.htsc.com.cn/htscPortal/home',
            //   icon: 'http://eip.htsc.com.cn/htscPortal/favicon.ico',
            //   size: '140 Mb',
            // });
          }
        } 
      }

      // 递归处理 children（如果有）
      if (newItem.children && newItem.children.length > 0) {
        newItem.children = updateSkillOnClick(newItem.children, targetKeys);
      }
      return newItem;
    });
  };

  useEffect(() => {
    if (deleteHistoryConversationConfigData && deleteHistoryConversationConfigData?.code === '0') {
      // 删除成功后重新调用接口刷新一次历史会话列表
      historyConversationHooksRun();
      
      // 如果删除的是当前会话，则开启新会话
      // if (conversationId?.current === deleteHistoryConversationConfigData?.deletedConversationId) {
      //   handleNewConversation();
      // }
    }
  }, [deleteHistoryConversationConfigData]);

  return (
    <div className="PushDivContainer">
      {
        showPushHistory &&
        <div className="HistoryPanel" style={{ ...historyPanelStyle, ...historyPanelPushStyle }}>
          <HistoryConversation
            standaloneMode={false}
            style={{ height: 'calc(100% - 6px)', width: 'calc(100% - 6px)', margin: 0 }}
            title={historyConversation?.title}
            navbar={{
              // 会话历史列表页navbar自定义logo图标先取historyConversation?.navbar?.logo，没有再取navbar?.logo，没有再取config?.robot?.logo
              // 如果都没有，Navbar组件中有默认内置logo图标
              ...PreprocessNavBarConfig(historyConversation?.navbar?.logo || navbar?.logo || config?.robot?.logo || '', navbar, {}, historyConversation?.navbar)?.historyConversationNavbar,
              onNewButtonClick: handleNewConversation, 
              onCloseButtonClick: toggleHistory,
            }}
            list={historyConversationList}
            activeConversationId={conversationId?.current}
            onConversationClick={(conversationItem: ConversationItemProps) => selectHistoryConversation(conversationItem)}
            onConversationRename={handleRenameHistoryConversation}
            onConversationDelete={handleDeleteHistoryConversation}
            renderBrand={historyConversation?.renderBrand}
            renderListItem={historyConversation?.renderListItem}
            renderFooter={historyConversation?.renderFooter}
            showSearch={historyConversation?.showSearch}
            showDeleteConversation={historyConversation?.showDeleteConversation}
            showRename={historyConversation?.showRename}
          />
        </div>
      }

      <div className="ChatWrap" id="ChatComponentsWrap" style={isHistoryOpen ? chatPannelPushStyle : {}}>
        {/* 历史会话面板打开时的遮罩层 */}
        {showPushHistory && isHistoryOpen && <div className={`ChatOverlay ${isHistoryOpen && isHistoryShow ? 'ChatOverlayShow' : ''}`} onClick={toggleHistory}></div>}
        {showRiskTip && isMobile && <RiskTipApp onClose={handleRiskClose} onAgree={handleRiskAgree} hasAgreedRisk={riskRead} onChatClose={navbar?.onCloseButtonClick} />}
        {showRiskTip && !isMobile && <RiskTipPc onClose={handleRiskClose} onAgree={handleRiskAgree} hasAgreedRisk={riskRead} onChatClose={navbar?.onCloseButtonClick} />}
        <LocaleProvider locale={locale} locales={locales}>
          <WaterMark  {...{ ...config?.waterMark, show: config?.waterMark?.show && !isNewConversation }}  >
            <div className="ChatApp">
              {isNewConversation && chatConfigs?.length > 0 &&
                <ChatConfig
                  title={historyConversation?.title}
                  ref={chatConfigRef}
                  VariableConfigs={chatConfigs}
                  onChange={(e: any) => setChatConfigs(e)}
                  isNewConversation={isNewConversation}
                />}
              {/* 新开会话需要展示GuidePage */}
              {isNewConversation ?
                <>
                  {renderWelcome ? renderWelcome({
                    guideWelcome: { ...welcomeInfo, ...welcome, logo: welcome?.logo || config?.robot?.logo || '' },
                    navbar: {
                      ...PreprocessNavBarConfig(welcome?.navbar?.logo || navbar?.logo || config?.robot?.logo || '', navbar, welcome?.navbar)?.welcomeNavBar,
                      newConTooltip: isMobile ? '' : (isNewConversation ? '当前已是新对话' : '开启新对话'),
                      onNewButtonClick: handleNewConversation, onHistoryButtonClick: toggleHistory
                    },
                    onRiskClick: handleRiskOpen,
                    isAorta: isAorta,
                    keyboardShow: keyboardShow
                  }) :
                    <GuidePage
                      // 接口返回配置只取title,subtitle,riskTip展示，如外部配置了则用外部配置的覆盖掉
                      guideWelcome={{ ...welcomeInfo, ...welcome, logo: welcome?.logo || config?.robot?.logo || '' }}
                      navbar={{
                        // 欢迎页navbar自定义logo图标先取welcome?.navbar?.logo，没有再取navbar?.logo，没有再取config?.robot?.logo
                        // 如果都没有，Navbar组件中有默认内置logo图标
                        ...PreprocessNavBarConfig(welcome?.navbar?.logo || navbar?.logo || config?.robot?.logo || '', navbar, welcome?.navbar)?.welcomeNavBar,
                        newConTooltip: isMobile ? '' : (isNewConversation ? '当前已是新对话' : '开启新对话'),
                        onNewButtonClick: handleNewConversation, onHistoryButtonClick: toggleHistory
                      }}
                      onRiskClick={handleRiskOpen}
                      isAorta={isAorta}
                      keyboardShow={keyboardShow}
                      onSend={onSend || handleSend}
                      // 如果有welcome?.renderNavbar则用welcome?.renderNavbar的自定义渲染；没有则用welcome?.navbar的参数渲染；
                      // 以上两个都没有再用公共renderNavbar方法渲染
                      // 如果连公共renderNavbar方法也没有，在PreprocessNavBarConfig中处理了取公共navbar参数渲染
                      renderNavbar={welcome?.renderNavbar ? welcome?.renderNavbar : (welcome?.navbar && Object.keys(welcome?.navbar)?.length ? undefined : renderNavbar)}
                    />
                  }
                </> :
                <>
                  {renderNavbar ? renderNavbar() :
                    <Navbar
                      // 公共navbar自定义logo图标先取navbar?.logo，没有再取config?.robot?.logo
                      // 如果都没有，Navbar组件中有默认内置logo图标
                      {...PreprocessNavBarConfig(navbar?.logo || config?.robot?.logo || '', navbar)?.navBar}
                      newConTooltip={isMobile ? '' : `${isNewConversation ? '当前已是新对话' : '开启新对话'}`}
                      onNewButtonClick={handleNewConversation}
                      onHistoryButtonClick={toggleHistory}
                    />
                  }
                  <MessageContainer
                    ref={msgsRef}
                    loadMoreText={loadMoreText}
                    messages={messages}
                    lowCodeConfig={config?.lowCode}
                    renderBeforeMessageList={renderBeforeMessageList}
                    renderMessageContent={renderMessageContent}
                    onRefresh={onRefresh || (!noMoreHistory ? handleRefresh : undefined)}
                    onScroll={onScroll}
                    backBottomButton={backBottomButton}
                    onBackBottomShow={onBackBottomShow}
                    onBackBottomClick={onBackBottomClick}
                    onFeedBack={handleFeedBack}
                    onConsolidateReports={onConsolidateReports}
                    copyText={config?.bridge?.copyText}
                    onQuote={handleQuote}
                    showDeleteMessage={true}
                    handleDeleteMessage={handleDeleteMessage}
                    openPage={config?.bridge?.openWebPage}
                    openFileViews={config?.bridge?.openFileViews}
                    preprocessContent={config?.bridge?.preprocessContent}
                    onSend={(onSend ? onSend : undefined) || handleSend}
                    isDev={config.isDev}
                    userId={config.userId}
                    robot={config.robot}
                    // 是否展示反馈弹窗
                    showFeedbackModal={showFeedbackModal}
                    // 反馈弹窗自定义配置
                    feedbackModalConfig={feedbackModalConfig}
                    feedbackLabels={feedbackLabels}
                    referencesConfig={referencesConfig}
                    renderStopAnswer={renderStopAnswer}
                    customThinkingStyle={customThinkingStyle}
                    customDeepThinkingStyle={customDeepThinkingStyle}
                    customThinkingCompleteStyle={customThinkingCompleteStyle}
                    autoThinkCollapsed={autoThinkCollapsed}
                    isWide={isWide}
                    renderReferencesContent={renderReferencesContent}
                    hallucinationText={hallucinationText}
                    {...messageContainerConfig}
                  />
                </>}
              {renderQuickReplies ? (
                renderQuickReplies()
              ) : (
                <QuickReplies
                  quickReplies={quickReplies}
                  openPage={config?.bridge?.openWebPage}
                  onClick={onQuickReplyClick || handleQuickReplyClick}
                  onScroll={onQuickReplyScroll}
                />
              )}
              <div className="ChatFooter">
                {CustomerComposer
                  ? <CustomerComposer ref={composerInputRef}
                    inputType={inputType}
                    text={composerConfig?.text}
                    inputOptions={{ maxRows: isNewConversation ? 3 : 6, ...inputOptions }}
                    placeholder={composerConfig?.placeholder}
                    onAccessoryToggle={onAccessoryToggle}
                    recorder={recorder}
                    toolbar={toolbar}
                    onToolbarClick={onToolbarClick}
                    onInputTypeChange={onInputTypeChange}
                    onFocus={handleInputFocus}
                    onChange={handleInputChange || onInputChange}
                    onBlur={handleInputBlur}
                    onSend={onSend || handleSend}
                    onImageSend={onImageSend}
                    onConsolidateReports={onConsolidateReports}
                    // rightAction={rightAction}
                    // 没有配置停止生成的接口时，不展示停止生成按钮
                    showStopAnswer={showStopAnswer && stopConfig?.url}
                    onStopAnswer={handleStopAnswer}
                    showInternetSearch={composerConfig?.showInternetSearch}
                    enableInternetSearch={enableInternetSearch}
                    toggleEnableInternetSearch={toggleEnableInternetSearch}
                    aboveNode={showSensitiveError ? (<div className='SensitiveError'>您的输入可能包含投资者敏感信息，请核对~</div>) :  composerConfig?.aboveNode}
                    belowNode={composerConfig?.belowNode}
                    extraAction={composerConfig?.extraAction}
                    showThinking={composerConfig?.showThinking}
                    operationConfig={composerConfig?.quoteOperations}
                    skills={composerConfig?.skill ? updateSkillOnClick(composerConfig?.skill, innerSikllCodeList) : null}
                    quickNewConversation={composerConfig?.quickNewConversation}
                    quickOpenHistory={composerConfig?.quickOpenHistory}
                    handleNewConversation={handleNewConversation}
                    toggleHistory={toggleHistory}
                  />
                  : <Composer
                    // wideBreakpoint={wideBreakpoint}
                    isWide={isWide}
                    ref={composerInputRef}
                    inputType={inputType}
                    text={composerConfig?.text}
                    inputOptions={{ maxRows: isNewConversation ? 3 : 6, ...inputOptions }}
                    placeholder={composerConfig?.placeholder}
                    onAccessoryToggle={onAccessoryToggle}
                    recorder={recorder}
                    toolbar={toolbar}
                    onToolbarClick={onToolbarClick}
                    onInputTypeChange={onInputTypeChange}
                    onFocus={handleInputFocus}
                    onChange={handleInputChange || onInputChange}
                    onBlur={handleInputBlur}
                    onSend={onSend || handleSend}
                    onImageSend={onImageSend}
                    onConsolidateReports={onConsolidateReports}
                    // rightAction={rightAction}
                    // 没有配置停止生成的接口时，不展示停止生成按钮
                    showStopAnswer={showStopAnswer && stopConfig?.url}
                    onStopAnswer={handleStopAnswer}
                    showInternetSearch={composerConfig?.showInternetSearch}
                    uploadConfig={composerConfig?.uploadConfig}
                    enableInternetSearch={enableInternetSearch}
                    toggleEnableInternetSearch={toggleEnableInternetSearch}
                    aboveNode={showSensitiveError ? (<div className='SensitiveError'>您的输入可能包含投资者敏感信息，请核对~</div>) : composerConfig?.aboveNode}
                    belowNode={composerConfig?.belowNode}
                    extraAction={composerConfig?.extraAction}
                    showThinking={composerConfig?.showThinking}
                    operationConfig={composerConfig?.quoteOperations || {
                      text: [{
                        label:"文本处理",
                        question:"文本处理"
                      }],
                      web: [
                        {
                          label:"网页处理",
                          question:"网页处理"
                        }
                      ],
                      image: [
                        {
                          label:"图片处理",
                          question:"图片处理"
                        }
                      ],
                      file:[
                        {
                          label:"文件处理",
                          question:"文件处理"
                        }
                      ],
                    }}
                    skills={composerConfig?.skill ? updateSkillOnClick(composerConfig?.skill, innerSikllCodeList) : null}
                    quickNewConversation={composerConfig?.quickNewConversation}
                    quickOpenHistory={composerConfig?.quickOpenHistory}
                    handleNewConversation={handleNewConversation}
                    toggleHistory={toggleHistory}
                  />
                }
                {
                  renderFooterVersion
                    ? renderFooterVersion()
                    : (<div className="ChatFooter-Version" />)
                  // : (<div className="ChatFooter-Version">内容由大数据模型生成，不构成投资建议</div>)
                }
              </div>
            </div>
          </WaterMark>
        </LocaleProvider>
      </div>
    </div >
  );
});

/*
 * @Author: 020446
 * @Description: 文件描述
 * @Date: 2025-05-09 17:20:31
 */
import { defineConfig } from 'dumi';
import path from 'path';
import fs from 'fs';

const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

export default defineConfig({
  logo: '/home/<USER>',
  favicons: ['/home/<USER>'],
  themeConfig: {
    nav: [
      { title: '指南', link: '/guide' },
      { title: 'AI组件库', link: '/components' },
      { title: 'Hooks', link: '/hooks' },
      { title: '通信协议', link: '/protocol' },
    ],
  },

  scripts: [
    `//g.alicdn.com/chatui/icons/2.6.2/index.js`, 
    `/xlog.umd.js`,
    
    // 站点xlog脚本
    `
    (function() {
      // 初始化 xlog
      function initXlog() {
        if (window.xlog && window.xlog.init) {
        console.log('window.xlog', window.xlog);
          try {
            window.xlog.init({
              from: 'SpriteCopilotKit',
              types: ['windowError', 'unhandledrejection', 'xhr', 'fetch'],
              myTrackConfig: {
                product_id: '542',
                product_name: 'SCK_Website',
                channel_env: 'prd_outer_test',
              },
            });
            return true;
          } catch (e) {
            console.error('xlog初始化失败！', e);
            return false;
          }
        }
        return false;
      }
      
      function trackPageView() {
        if (window.xlog && window.xlog.logPV) {
          const path = window.location.pathname;
          window.xlog.logPV({
            page_id: 'SCK_WebSite_' + path,
            page_title: 'SCK_Website：' + path,
          });
        }
      }
      
      // 等待 xlog 加载并初始化
      function waitForXlogAndInit() {
        if (window.xlog) {
          const initSuccess = initXlog();
          if (initSuccess) {
            // 初始化成功后立即上报当前页面
            trackPageView();
            
            // 监听路由变化（SPA应用）
            let currentPath = window.location.pathname;
            const observer = new MutationObserver(function() {
              if (currentPath !== window.location.pathname) {
                currentPath = window.location.pathname;
                setTimeout(trackPageView, 100);
              }
            });
            
            observer.observe(document.body, {
              childList: true,
              subtree: true
            });
            
            // 监听 popstate 事件（浏览器前进后退）
            window.addEventListener('popstate', trackPageView);
          }
        } else {
          // 如果 xlog 还未加载，继续等待
          setTimeout(waitForXlogAndInit, 100);
        }
      }
      
      // 页面加载完成后开始等待 xlog
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForXlogAndInit);
      } else {
        waitForXlogAndInit();
      }
    })();
    `
],
  //   outputPath: 'docs-dist',
  //   mode: 'site',
  //   // 以下为重要配置
  //   resolve: {
  //     includes: ['src'], // 从 src 目录解析组件文档
  //   },
  alias: {
    '@ht/chatui/es/styles/index.less': path.resolve(__dirname, 'src/styles/index.less'), // 配置别名
    '../../../src/styles/@ht/sprite-ui/dist/@ht/sprite-ui.min.css': path.resolve(__dirname, 'node_modules/@ht/sprite-ui/dist/@ht/sprite-ui.min.css'),
  },
  //   extraBabelPlugins: [
  //     // 如果你的库需要特殊 Babel 插件
  //   ],
  proxy: {
    // 会话管理转发配置，高可用，需要demo中send接口配置isaibag=true
    '/hiAgent/chat_query': {
      // target: 'http://10.102.82.66:6789/api/v1',
      target: 'http://10.102.92.209:9607/chat/llmpf/api/proxy/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/hiAgent': '' },
      onProxyRes: function (proxyRes) {
        proxyRes.headers['cache-control'] = 'no-cache, no-transform';
      },
    },
    '/hiAgent': {
      // target: 'http://10.102.82.66:6789/api/v1',
      // target: 'http://10.102.80.240:3000/llmpf/api/proxy/api/v1/',
      target: 'http://hiagenthub.sit.saas.htsc/llmpf/api/proxy/api/v1/',
      changeOrigin: true,
      pathRewrite: { '^/hiAgent': '' },
      onProxyRes: function (proxyRes) {
        proxyRes.headers['cache-control'] = 'no-cache, no-transform';
      },
    },
    
    // '/api': {
    //   target: 'http://mock.htsc',
    //   changeOrigin: true,
    //   rewrite: (path) => path.replace(/^\/api/, '')
    // },
    '/airobot/api/groovy/ai/adapter': {
      // target: 'http://168.63.127.233:8082',
      target: 'http://10.102.74.110:8082',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/airobot/, ''),
    },
    '/goapi': {
      target: 'http://mock.htsc',
      changeOrigin: true,
    },
    '/mcrm/api': {
      target: 'http://168.61.125.127:9082',
      changeOrigin: true,
    },
    '/api': {
      target: 'http://168.61.90.156:8080',
      // target: 'http://aorta.saasuat.htsc.com.cn:8090',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, ''),
    },
    '/tec/fusion/api': {
      target: 'http://168.63.117.2:9011',
      // target: ' http://168.61.127.58:8082',
      changeOrigin: true,
    },
    '/aorta/operation/api': {
      // 染色环境
      // target: 'http://10.102.76.215:8080',
      // 61环境
      // target: 'http://168.61.90.113:8080',
      // 63环境
      target: 'http://168.63.89.158:8080',
      changeOrigin: true,
      onProxyRes: function (proxyRes) {
        proxyRes.headers['cache-control'] = 'no-cache, no-transform';
      },
    },
    '/webassist': {
      target: 'http://webassist.sit.sass.htsc',
      changeOrigin: true,
      pathRewrite: { '^/webassist': '' },
      onProxyRes: function (proxyRes) {
        proxyRes.headers['cache-control'] = 'no-cache, no-transform';
      },
    },
  },

  // 更多配置见 https://d.umijs.org/config
});

import React, { useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
// import remarkGemoji from "remark-gemoji";
import rehypeRaw from 'rehype-raw';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import MarkdownLink from '../MarkdownLink';
// import rehypeExternalLinks from 'rehype-external-links'; // 关键插件
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { coldarkCold } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { preprocessContent } from './utils';
import { Tooltip } from '../Tooltip';
import { ReferenceContent } from '../References';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'katex/dist/katex.min.css';
import { ReferenceProps } from '../References/interface';
// import 'github-markdown-css/github-markdown.css'; // 此css已经拷贝到style.less

export interface MarkDownProps {
  content: string;
  // 如果传了，打开链接使用传入的打开页面方法
  openPage?: (url: string) => void;
  // 是否解析外部链接（思考部分不解析）
  disableLink?: boolean;
  preprocessContent?: (content: string) => string;
  references?: ReferenceProps[];
  isWide?: boolean;
}

export const MarkDown: React.FC<MarkDownProps> = (props) => {
  const {
    content,
    openPage,
    disableLink,
    preprocessContent: inputPreprocessContent,
    references,
    isWide = true,
  } = props;

  const handleCode = useCallback(({ node, inline, className, children, ...restProps }) => {
    // 如果是内联代码或者数学公式，直接返回普通code标签
    if (inline || className?.includes('math')) {
      return (
        <code className={className} {...restProps}>
          {children}
        </code>
      );
    }

    // 确保children是字符串
    const codeContent = Array.isArray(children) ? children.join('') : String(children || '');

    // 提取语言类型
    const match = /language-(\w+)/.exec(className || '');
    const language = match?.[1];

    return (
      <SyntaxHighlighter
        language={language}
        style={coldarkCold}
        showLineNumbers={true}
        PreTag="div"
        className={`syntax-highlight-wrapper ${className || ''}`}
        {...restProps}
      >
        {codeContent.replace(/\n$/, '')}
      </SyntaxHighlighter>
    );
  }, []);

  // 处理链接解析
  const handleLink = useCallback(
    ({ href, className, children }) => {
      // 如果不解析链接（思考内容部分不解析），解码为span
      if (disableLink) {
        return <span>{href && decodeURIComponent(href)}</span>;
      }
      // 如果不是以http开头的链接，不解析为外部链接
      if (!href?.startsWith('http')) {
        // 优先展示方括号里面的网站名，没有的情况下解码链接
        return <span>{children ? children : href && decodeURIComponent(href)}</span>;
      }

      return (
        <MarkdownLink href={href} className={className} openPage={openPage}>
          {children}
        </MarkdownLink>
      );
    },
    [disableLink, openPage],
  );

  const handleText = useCallback(({ node, children, ...restProps }) => {
    // 匹配 [数字] 格式的文本

    const isReference =
      node.tagName === 'span' && (node.properties?.className || []).includes('ReferenceHighlight');
    if (isReference) {
      const sliceId = node.properties?.dataIndex || children;
      // 获取reference数据
      const reference = (references || []).find(
        (item) => String(item.slice_id) === String(sliceId),
      );
      if (reference) {
        return (
          <Tooltip
            theme={{
              background: '#fff',
              borderRadius: '0.5px solid #DDDEE0',
              boxShadow: '0px 4px 20px 0px rgba(0, 0, 0, 0.08)',
              maxWidth: isWide ? '712px' : '296px',
            }}
            content={
              <ReferenceContent
                data={{ title: `${sliceId}、${reference?.title || ''}`, content: reference?.content || '' }}
              />
            }
          >
            <span data-index={sliceId} className="ReferenceHighlight">
              {sliceId}
            </span>
          </Tooltip>
        );
      } else {
        return <>[{sliceId}]</>;
      }
    }
    return <span {...restProps}>{children}</span>;
  }, [references, isWide]);

  return (
    <ReactMarkdown
      className="markdown-body"
      children={
        inputPreprocessContent ? inputPreprocessContent(content) : preprocessContent(content)
      }
      // remarkPlugins={[remarkGfm]}
      remarkPlugins={[[remarkGfm, { singleTilde: false }], remarkMath]}
      // remarkPlugins={[remarkGfm,remarkGemoji]}
      rehypePlugins={[
        rehypeRaw,
        // rehypeKatex,
        [rehypeKatex, { strict: false }], // 禁用严格模式（忽略非致命警告，减少控制台输出）
        // [rehypeExternalLinks, {
        //   target: '_blank', // 新开页面
        //   rel: ['noopener', 'noreferrer'], // 安全增强
        //   // 可选：仅处理外链
        //   // test: (node) =>
        //   //   node.properties?.href?.startsWith('http')
        // }],
      ]}
      components={{
        code: handleCode,
        a: handleLink,
        span: handleText,
      }}
    />
  );
};
